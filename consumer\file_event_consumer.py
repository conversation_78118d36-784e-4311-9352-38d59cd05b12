import time
import pika
import json
import logging
import sys
import asyncio
import threading
import functools
import os
from typing import Dict, Any, Optional
from core.document_processor import DocumentProcessor

from core.docling import DoclingProcessor
from core.file import (
    download_file,
    download_file_to_path_from_minio,
    generate_link_download,
)
from models import get_db
import repository.modelmanagement as modelmanagement_repo

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import sessionmaker

from settings import SERVICE_NAME, FILE_STORAGE_ADAPTER, VECTOR_COLLECTION_NAME, LOCAL_PATH
from concurrent.futures import ThreadPoolExecutor

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def update_file_status(
        file_id: int, 
        status: str,
        message: Optional[str] = None
    ) -> bool:
    """Synchronous wrapper for the async update_file_status function"""
    from models import sync_engine
    from sqlalchemy.orm import Session
    from sqlalchemy import update
    from models.Files import File as Files

    try:
        with Session(sync_engine) as session:
            query = (
                update(Files)
                .where(Files.id == file_id)
                .values(processing_status=status, message=message)
            )
            session.execute(query)
            session.commit()
            logger.info(f"Updated file ID {file_id} status to '{status}'")
            return True
    except Exception as e:
        logger.error(f"Error updating file status: {e}")
        return False


def save_uploaded_file(
    directory: str,
    filename: str,
    filesize: int,
    filetype: str,
    user_id: str,
):
    """Synchronous wrapper for the async save_uploaded_file function"""
    from models import sync_engine
    from sqlalchemy.orm import Session
    from models.Files import File as Files

    try:
        with Session(sync_engine) as session:
            new_file = Files(
                directory=directory,
                filename=filename,
                filesize=filesize,
                filetype=filetype,
                processing_status="queued",
                service=SERVICE_NAME,
                user_id=user_id,
                isact=True,
            )
            session.add(new_file)
            session.commit()
            session.refresh(new_file)
            logger.info(f"Saved new file: {new_file.filename} with ID {new_file.id}")
            return new_file.id
    except Exception as e:
        logger.error(f"Error saving uploaded file: {e}")
        raise e


class FileEventConsumer:
    def __init__(
        self,
        rabbitmq_url: str = "amqp://localhost",
        service_name: str = "default",
        binding_patterns: list = None,
    ):
        """
        Initialize the RabbitMQ consumer for file events using topic exchange

        Args:
            rabbitmq_url: RabbitMQ connection URL
            exchange_name: Name of the topic exchange
            queue_name: Name of the queue (if None, a random name will be generated)
            binding_patterns: List of routing patterns to bind to (default: ["file.#"])
        """
        self.rabbitmq_url = rabbitmq_url
        self.service_name = service_name
        self.queue_name = f"file_events_{service_name}"
        
        self.executor = ThreadPoolExecutor(max_workers=3)
        
        self.heartbeat_interval = 300
        self.reconnect_delay = 5
        self._connection = None
        self._channel = None

        if binding_patterns is not None:
            self.binding_patterns = binding_patterns
        else:
            self.binding_patterns = self._get_default_binding_patterns()

    def _is_connected(self) -> bool:
        """Memeriksa apakah koneksi dan channel dalam keadaan baik."""
        return (
            self._connection and self._connection.is_open and
            self._channel and self._channel.is_open
        )

    def _get_default_binding_patterns(self):
        """Get default binding patterns based on service name"""
        if self.service_name == "dcs_dashboard":
            return [
                "file.created.pdf",
                "file.created.docx",
            ]
        elif self.service_name == "external_data_crawling":
            return [
                "file.created.pdf",
                "file.created.csv",
                "file.modified.xlsx",
            ]
        else:
            return ["file.created.#"]
    
    def _threaded_callback(self, ch, method, properties, body):
        """
        Wrapper callback yang mengeksekusi proses di thread pool dan mengirimkan
        acknowledgment kembali ke thread I/O Pika dengan aman.
        """
        def do_work():
            try:
                logger.info(f"Processing message in worker thread: {body.decode('utf-8')}")
                # Proses event yang memakan waktu lama ada di sini
                self.process_file_event(ch, method, properties, body)

                # Setelah selesai, jadwalkan 'ack' untuk dijalankan di thread utama Pika
                cb = functools.partial(self.ack_message, ch, method.delivery_tag)
                self._connection.add_callback_threadsafe(cb)
            except Exception as e:
                logger.error(f"Error memproses pesan di worker thread: {e}")
                # Jika error, jadwalkan 'nack' untuk dijalankan di thread utama Pika
                cb = functools.partial(self.nack_message, ch, method.delivery_tag)
                self._connection.add_callback_threadsafe(cb)

        self.executor.submit(do_work)
    
    def ack_message(self, ch, delivery_tag):
        """Callback aman untuk mengirim ack."""
        if ch.is_open:
            ch.basic_ack(delivery_tag=delivery_tag)
            logger.info(f"Acknowledged message with delivery_tag: {delivery_tag}")
        else:
            logger.warning("Channel is closed, cannot ack message.")

    def nack_message(self, ch, delivery_tag):
        """Callback aman untuk mengirim nack."""
        if ch.is_open:
            ch.basic_nack(delivery_tag=delivery_tag, requeue=True)
            logger.info(f"Nacknowledged message with delivery_tag: {delivery_tag}")
        else:
            logger.warning("Channel is closed, cannot nack message.")

    def connect(self):
        """Establish connection to RabbitMQ and set up topic exchange"""
        if self._is_connected():
            return
        
        logger.info(f"Connecting to RabbitMQ service: {self.service_name}")

        try:
            params = pika.URLParameters(self.rabbitmq_url)
            params.heartbeat = self.heartbeat_interval
            params.blocked_connection_timeout = 600


            self._connection = pika.BlockingConnection(params)
            self._channel = self._connection.channel()

            self._channel.exchange_declare(
                exchange="file_events", exchange_type="topic", durable=True
            )

            result = self._channel.queue_declare(
                queue=self.queue_name,
                durable=True,
                exclusive=not self.queue_name,
            )
    
            self.queue_name = result.method.queue

            for pattern in self.binding_patterns:
                self._channel.queue_bind(
                    exchange="file_events",
                    queue=self.queue_name,
                    routing_key=pattern,
                )

            self._channel.basic_qos(prefetch_count=3)

            logger.info(f"Connected to RabbitMQ service: {self.service_name}")
            logger.info(
                f"Bound queue '{self.queue_name}' with patterns: {self.binding_patterns}"
            )

        except pika.exceptions.AMQPConnectionError as e:
            logger.error(f"Failed to connect to RabbitMQ: {e}")
            self.close() 
            raise
    
    def close(self):
        """Close the RabbitMQ connection and channel"""
        try:
            if self._channel and self._channel.is_open:
                self._channel.close()
            if self._connection and self._connection.is_open:
                self._connection.close()
            logger.info("RabbitMQ connection resources closed.")
        except Exception as e:
            logger.warning(f"Error while closing RabbitMQ connection: {e}")
        finally:
            self._connection = None
            self._channel = None

    def process_file_event(self, ch, method, properties, body):
        """
        Process incoming file event messages

        Args:
            ch: Channel
            method: Delivery method
            properties: Message properties
            body: Message body
        """
        try:
            # Parse the message
            message = json.loads(body.decode("utf-8"))
            logger.info(f"Received file event: {message}")

            # Extract file information
            file_path = message.get("file_path")
            event_type = message.get(
                "event_type"
            )  # e.g., 'created', 'modified', 'deleted'
            timestamp = message.get("timestamp")
            file_size = message.get("file_size")

            # Validate required fields
            if not file_path or not event_type:
                logger.error("Missing required fields in message")
                try:
                    ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
                except Exception as nack_err:
                    logger.error(f"Failed to nack message: {nack_err}")

            # Process based on event type
            if event_type == "created":
                self.handle_file_created(file_path, message)
            elif event_type == "modified":
                self.handle_file_modified(file_path, message)
            elif event_type == "deleted":
                self.handle_file_deleted(file_path, message)
            else:
                logger.warning(f"Unknown event type: {event_type}")

            # Acknowledge message processing
            # try:
            #     ch.basic_ack(delivery_tag=method.delivery_tag)
            # except Exception as ack_err:
            #     logger.error(f"Failed to ack message: {ack_err}")

            logger.info(f"Successfully processed file event for: {file_path}")

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse message JSON: {e}")
            try:
                ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
            except Exception as nack_err:
                logger.error(f"Failed to nack message: {nack_err}")
        except Exception as e:
            logger.error(f"Error processing file event: {e}")
            try:
                ch.basic_nack(delivery_tag=method.delivery_tag, requeue=True)
            except Exception as nack_err:
                logger.error(f"Failed to nack message: {nack_err}")

    def handle_file_created(self, file_path: str, message: Dict[str, Any]):
        """Handle file creation events"""
        logger.info(f"Processing file creation: {file_path}")
        print(f"Processing file creation: {file_path}")
        file_id = None
        try:
            processor = DocumentProcessor()
            print("Start the queue...")

           
            file_id = message.get("metadata", {}).get("file_id")
            doc_type = message.get("metadata", {}).get("doc_type")
            print(f"\n\nRabbitmq Message Document type: {doc_type}\n\n")
            update_file_status(file_id, "processing")
            local_tmp_path = None
            try:
                logger.info(f"Downloading file from: {file_path}")

                file = download_file(
                    path=f"{file_path}",
                )
                logger.info(f"File downloaded: {file}")

                if FILE_STORAGE_ADAPTER == "local":
                    local_tmp_path = os.path.join(LOCAL_PATH, "documents", file_path)
                else:
                    local_tmp_path = os.path.join("./tmp", file_path)

                logger.info(f"Local temporary path: {local_tmp_path}")

                if not os.path.exists(local_tmp_path):
                    logger.error(f"Failed to download file: {file_path}")
                    update_file_status(file_id, "failed", message="File download failed")
                    return
                print("\nStepping to process the file...\n")

                if processor.check_file_exists(filename=file_path):
                    logger.warning(f"File {file_path} already exists in collection. Skipping.")
                    update_file_status(file_id, "done", message="File already exists in collection")
                    os.unlink(local_tmp_path)
                    return
                
                processed_data = processor.process_file(
                    file_path=local_tmp_path,
                    filename=file_path,
                )
                print("\nFile processed successfully...\n")


                start_id = processor.get_next_id()
                
                points = processor.create_points(
                    processed_data=processed_data, 
                    start_id=start_id,
                    doc_type=doc_type
                )

                print(f"\nPoints created: {len(points)}\n")
                print("Print first 5 points for brevity\n")
                print(f"Points: {points}") 

                if points:
                    upsert_results = processor.batch_upsert_points(points)
                    logger.info(f"Upsert results: {upsert_results}")
                    print("\nFile upserted successfully...\n")

                os.unlink(local_tmp_path)

            except Exception as url_error:
                logger.error(f"Error with presigned URL: {url_error}")
                raise

            print("Queue ended, processing file...")
            update_file_status(file_id, "done")
        except Exception as e:
            logger.error(f"Processing error: {e}")
            try:
                update_file_status(file_id, "failed", message=str(e))
            except Exception as inner_e:
                logger.error(f"Failed to update status to 'failed': {inner_e}")

    def handle_file_modified(self, file_path: str, message: Dict[str, Any]):
        """Handle file modification events"""
        logger.info(f"Processing file modification: {file_path}")

        # Add your modification handling logic here
        # This might trigger reprocessing or incremental updates

    def handle_file_deleted(self, file_path: str, message: Dict[str, Any]):
        """Handle file deletion events"""
        logger.info(f"Processing file deletion: {file_path}")

        # Add your deletion handling logic here
        # This might include cleanup of related data or models

    def start_consuming(self):
        """Start consuming messages from the queue"""
        try:
            if not self._channel:
                self.connect()

            # Set up consumer
            # self._channel.basic_consume(
            #     queue=self.queue_name, on_message_callback=self.process_file_event
            # )

            self._channel.basic_consume(
                queue=self.queue_name,
                on_message_callback=self._threaded_callback  # Pakai wrapper threaded
            )

            logger.info(f"Starting to consume messages from queue: {self.queue_name}")
            logger.info("To exit press CTRL+C")

            try:
                self._channel.start_consuming()
            except KeyboardInterrupt:
                logger.info("Stopping consumer...")
                self._channel.stop_consuming()
                self._connection.close()
                sys.exit(0)
        except pika.exceptions.AMQPConnectionError as e:
            logger.error(f"Connection error: {e}")
            # sys.exit(1)
        except Exception as e:
            logger.error(f"Error starting consumer: {e}")

    def close_connection(self):
        """Close the RabbitMQ connection"""
        if self._connection and not self.connection.is_closed:
            self._connection.close()
            logger.info("RabbitMQ connection closed")


def main():
    """Main function to run the consumer"""
    # Configuration from environment variables or defaults
    rabbitmq_url = os.getenv("RABBITMQ_URL", "amqp://localhost")
    service_name = os.getenv("SERVICE_NAME", "dcs_dashboard")

    # Create and start consumer
    consumer = FileEventConsumer(rabbitmq_url, service_name)

    try:
        consumer.start_consuming()
    except Exception as e:
        logger.error(f"Consumer failed: {e}")
        consumer.close_connection()
        sys.exit(1)


if __name__ == "__main__":
    main()
