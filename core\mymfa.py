from datetime import datetime, timedelta
import traceback
import pyotp
from pytz import timezone
import qrcode
import io
import base64
from sqlalchemy import func, select, and_, or_, update
from core.encryption import encrypt_value
from models.GlobalVariabel import GlobalVariabel
from models.User import User
from sqlalchemy.ext.asyncio import AsyncSession
from models.UserKey import User<PERSON><PERSON>
from settings import TZ

class WebTOTPManager:
    def __init__(self, service_name="TelkomDCS"):
        self.service_name = service_name
    
    def generate_setup(self, account_name):
        """Generate TOTP setup dengan QR code untuk web"""
        # Generate secret
        secret = pyotp.random_base32()
        
        # Create TOTP instance
        totp = pyotp.TOTP(secret)
        
        # Generate provisioning URI
        provisioning_uri = totp.provisioning_uri(
            name=account_name,
            issuer_name=self.service_name
        )
        
        # Generate QR code sebagai base64 image
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(provisioning_uri)
        qr.make(fit=True)
        
        # Convert ke image
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Convert ke base64 untuk display di web
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        img_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        return {
            'secret': secret,
            'qr_code': img_base64,
            'provisioning_uri': provisioning_uri
        }
    
    def verify_token(self, secret, token):
        """Verify TOTP token"""
        totp = pyotp.TOTP(secret)
        return totp.verify(token)
    
    def get_current_token(self, secret):
        """Get current token"""
        totp = pyotp.TOTP(secret)
        return totp.now()
    

totp_manager = WebTOTPManager("TelkomDCS")

def generate_setup(name):
    """Generate TOTP setup"""
    try:
        account_name = name
        setup_data = totp_manager.generate_setup(account_name)
        
        return ({
            'success': True,
            'secret': setup_data['secret'],
            'qr_code': setup_data['qr_code']
        })
        
    except Exception as e:
        traceback.print_exc()
        return print("Error generating TOTP setup:", str(e))


async def temp_save_secret(secret: str, user_id: str, db: AsyncSession):
    try:
        tz = timezone(TZ)
        # Simulate saving the secret to a database
        new_data = UserKey(
            userid=user_id,
            secret_key=secret,
            created_at=datetime.now(tz),
        )
        db.add(new_data)
        await db.commit()
        return True
    except Exception as e:
        traceback.print_exc()
        return False
    
async def save_secret(
        secret: str, 
        user_id: str, 
        db: AsyncSession
    ):
    try:
        gvalue = await db.execute(
            select(GlobalVariabel).where(
                GlobalVariabel.name == "mfa_exp",
                GlobalVariabel.isact == True,
            )
        )
        gvalue = gvalue.scalar()
        if not gvalue:
            raise ValueError("Global variable 'mfa_exp' not found")
        exp_hour = int(gvalue.value) if gvalue.value else 24
        tz = timezone(TZ)
        expire = datetime.now(tz) + timedelta(hours=exp_hour)
        user_key = await db.execute(
            select(UserKey).where(
                UserKey.secret_key==secret,
                UserKey.userid==user_id,
            )
        )
        user_key = user_key.scalar()
        if not user_key:
            raise ValueError("Key not found")
        user_key.status = True
        user = await db.execute(
            select(User).where(User.id==user_id)
        )
        user = user.scalar()
        if not user:
            raise ValueError("User not found")
        user.key = encrypt_value(secret)
        user.key_exp = expire.replace(tzinfo=None)

        await db.commit()
        return True
    except Exception as e:
        traceback.print_exc()
        return False

async def get_user_from_secret(secret:str, db:AsyncSession):
    try:
        user_key = await db.execute(
            select(UserKey).where(UserKey.secret_key==secret)
            )
        user_key = user_key.scalar()
        if not user_key:
            raise ValueError("User not found")
        user = await db.execute(
            select(User).where(User.id==user_key.userid)
        )
        return user.scalar()
    except Exception as e:
        traceback.print_exc()
        return None

def verify_token(secret, token):
    """Verify TOTP token"""
    try:
        if not secret or not token:
            return ({'success': False, 'error': 'Secret dan token required'}), 400
        
        is_valid = totp_manager.verify_token(secret, token)
        print(f"Token valid: {is_valid}")
        
        return is_valid
        
    except Exception as e:
        traceback.print_exc()
        return print({'success': False, 'error': str(e)}), 500

def current_token():
    """Get current TOTP token"""
    try:
        data = {}
        secret = data.get('secret')
        
        if not secret:
            return ({'success': False, 'error': 'Secret required'}), 400
        
        token = totp_manager.get_current_token(secret)
        
        return ({
            'success': True,
            'token': token
        })
        
    except Exception as e:
        traceback.print_exc()
        return print({'success': False, 'error': str(e)})

async def check_mfa_suspend_status(db: AsyncSession, user_id: str) -> dict:
    try:
        user = await db.execute(
            select(User).where(User.id == user_id)
        )
        user = user.scalar()
        
        if not user:
            return {'is_suspended': False, 'suspended_until': None, 'remaining_time': None, 'failed_attempts': 0}
        
        # Cek apakah user sedang di-suspend
        if user.mfa_suspended_until and user.mfa_suspended_until > datetime.now():
            remaining = user.mfa_suspended_until - datetime.now()
            
            if remaining.total_seconds() <= 0 or remaining.total_seconds() > 3600:  # max 1 jam
                await reset_mfa_failed_attempts(db, user_id)
                return {
                    'is_suspended': False,
                    'suspended_until': None,
                    'remaining_time': None,
                    'failed_attempts': 0
                }
            
            remaining_minutes = int(remaining.total_seconds() / 60)
            remaining_seconds = int(remaining.total_seconds() % 60)
            
            return {
                'is_suspended': True,
                'suspended_until': user.mfa_suspended_until,
                'remaining_time': f"{remaining_minutes}m {remaining_seconds}s",
                'failed_attempts': user.mfa_failed_attempts
            }
        
        if user.mfa_last_failed_attempt:
            expired_hours_result = await db.execute(
                select(GlobalVariabel).where(
                    GlobalVariabel.name == "failed_attempts_expired_hours",
                    GlobalVariabel.isact == True,
                )
            )
            expired_hours = expired_hours_result.scalar()
            expired_hours = int(expired_hours.value) if expired_hours and expired_hours.value else 1 
            
            expired_time = user.mfa_last_failed_attempt + timedelta(hours=expired_hours)
            if datetime.now() > expired_time:
                await reset_mfa_failed_attempts(db, user_id)
                return {
                    'is_suspended': False,
                    'suspended_until': None,
                    'remaining_time': None,
                    'failed_attempts': 0
                }
        
        if user.mfa_suspended_until and user.mfa_suspended_until <= datetime.now():
            await reset_mfa_failed_attempts(db, user_id)
        
        return {
            'is_suspended': False,
            'suspended_until': None,
            'remaining_time': None,
            'failed_attempts': user.mfa_failed_attempts
        }
        
    except Exception as e:
        traceback.print_exc()
        return {'is_suspended': False, 'suspended_until': None, 'remaining_time': None, 'failed_attempts': 0}


async def increment_mfa_failed_attempts(db: AsyncSession, user_id: str) -> dict:
    try:
        max_attempts_result = await db.execute(
            select(GlobalVariabel).where(
                GlobalVariabel.name == "mfa_max_attempts",
                GlobalVariabel.isact == True,
            )
        )
        max_attempts = max_attempts_result.scalar()
        max_attempts = int(max_attempts.value) if max_attempts and max_attempts.value else 3
        
        suspend_duration_result = await db.execute(
            select(GlobalVariabel).where(
                GlobalVariabel.name == "mfa_suspend_duration_minutes",
                GlobalVariabel.isact == True,
            )
        )
        suspend_duration = suspend_duration_result.scalar()
        suspend_duration = int(suspend_duration.value) if suspend_duration and suspend_duration.value else 1
        
        # Update failed attempts
        user = await db.execute(
            select(User).where(User.id == user_id)
        )
        user = user.scalar()
        
        if not user:
            return {'is_suspended': False, 'suspended_until': None, 'remaining_time': None, 'failed_attempts': 0}
        
        if user.mfa_last_failed_attempt:
            expired_hours_result = await db.execute(
                select(GlobalVariabel).where(
                    GlobalVariabel.name == "failed_attempts_expired_hours",
                    GlobalVariabel.isact == True,
                )
            )
            expired_hours = expired_hours_result.scalar()
            expired_hours = int(expired_hours.value) if expired_hours and expired_hours.value else 1
            
            expired_time = user.mfa_last_failed_attempt + timedelta(hours=expired_hours)
            if datetime.now() > expired_time:
                user.mfa_failed_attempts = 0
                user.mfa_suspended_until = None
        
        user.mfa_failed_attempts += 1
        user.mfa_last_failed_attempt = datetime.now()
        
        # Cek apakah perlu suspend
        if user.mfa_failed_attempts >= max_attempts:
            tz = timezone(TZ)
            suspend_until = datetime.now(tz) + timedelta(minutes=suspend_duration)
            user.mfa_suspended_until = suspend_until.replace(tzinfo=None)
            
            await db.commit()
            
            return {
                'is_suspended': True,
                'suspended_until': user.mfa_suspended_until,
                'remaining_time': f"{suspend_duration}m 0s",
                'failed_attempts': user.mfa_failed_attempts
            }
        
        await db.commit()
        
        return {
            'is_suspended': False,
            'suspended_until': None,
            'remaining_time': None,
            'failed_attempts': user.mfa_failed_attempts
        }
        
    except Exception as e:
        traceback.print_exc()
        return {'is_suspended': False, 'suspended_until': None, 'remaining_time': None, 'failed_attempts': 0}

async def reset_mfa_failed_attempts(db: AsyncSession, user_id: str) -> bool:
    """Reset failed attempts dan suspend status"""
    try:
        user = await db.execute(
            select(User).where(User.id == user_id)
        )
        user = user.scalar()
        
        if not user:
            return False
        
        user.mfa_failed_attempts = 0
        user.mfa_suspended_until = None
        user.mfa_last_failed_attempt = None
        
        await db.commit()
        return True
        
    except Exception as e:
        traceback.print_exc()
        return False

async def check_login_suspend_status(db: AsyncSession, user_id: str) -> dict:
    try:
        user = await db.execute(
            select(User).where(User.id == user_id)
        )
        user = user.scalar()
        
        if not user:
            return {'is_suspended': False, 'suspended_until': None, 'remaining_time': None, 'failed_attempts': 0}
        
        # Cek apakah user sedang di-suspend
        if user.login_suspended_until and user.login_suspended_until > datetime.now():
            remaining = user.login_suspended_until - datetime.now()
            
            if remaining.total_seconds() <= 0 or remaining.total_seconds() > 3600:  # max 1 jam
                await reset_login_failed_attempts(db, user_id)
                return {
                    'is_suspended': False,
                    'suspended_until': None,
                    'remaining_time': None,
                    'failed_attempts': 0
                }
            
            remaining_minutes = int(remaining.total_seconds() / 60)
            remaining_seconds = int(remaining.total_seconds() % 60)
            
            return {
                'is_suspended': True,
                'suspended_until': user.login_suspended_until,
                'remaining_time': f"{remaining_minutes}m {remaining_seconds}s",
                'failed_attempts': user.login_failed_attempts
            }
        
        if user.login_last_failed_attempt:
            expired_hours_result = await db.execute(
                select(GlobalVariabel).where(
                    GlobalVariabel.name == "failed_attempts_expired_hours",
                    GlobalVariabel.isact == True,
                )
            )
            expired_hours = expired_hours_result.scalar()
            expired_hours = int(expired_hours.value) if expired_hours and expired_hours.value else 1  # default 1 jam
            
            expired_time = user.login_last_failed_attempt + timedelta(hours=expired_hours)
            if datetime.now() > expired_time:
                await reset_login_failed_attempts(db, user_id)
                return {
                    'is_suspended': False,
                    'suspended_until': None,
                    'remaining_time': None,
                    'failed_attempts': 0
                }
        
        # Jika suspend sudah expired, reset failed attempts
        if user.login_suspended_until and user.login_suspended_until <= datetime.now():
            await reset_login_failed_attempts(db, user_id)
        
        return {
            'is_suspended': False,
            'suspended_until': None,
            'remaining_time': None,
            'failed_attempts': user.login_failed_attempts
        }
        
    except Exception as e:
        traceback.print_exc()
        return {'is_suspended': False, 'suspended_until': None, 'remaining_time': None, 'failed_attempts': 0}

async def increment_login_failed_attempts(db: AsyncSession, user_id: str) -> dict:
    try:
        # Ambil konfigurasi dari global variable
        max_attempts_result = await db.execute(
            select(GlobalVariabel).where(
                GlobalVariabel.name == "login_max_attempts",
                GlobalVariabel.isact == True,
            )
        )
        max_attempts = max_attempts_result.scalar()
        max_attempts = int(max_attempts.value) if max_attempts and max_attempts.value else 5
        
        suspend_duration_result = await db.execute(
            select(GlobalVariabel).where(
                GlobalVariabel.name == "login_suspend_duration_minutes",
                GlobalVariabel.isact == True,
            )
        )
        suspend_duration = suspend_duration_result.scalar()
        suspend_duration = int(suspend_duration.value) if suspend_duration and suspend_duration.value else 5
        
        # Update failed attempts
        user = await db.execute(
            select(User).where(User.id == user_id)
        )
        user = user.scalar()
        
        if not user:
            return {'is_suspended': False, 'suspended_until': None, 'remaining_time': None, 'failed_attempts': 0}
        
        if user.login_last_failed_attempt:
            expired_hours_result = await db.execute(
                select(GlobalVariabel).where(
                    GlobalVariabel.name == "failed_attempts_expired_hours",
                    GlobalVariabel.isact == True,
                )
            )
            expired_hours = expired_hours_result.scalar()
            expired_hours = int(expired_hours.value) if expired_hours and expired_hours.value else 1
            
            expired_time = user.login_last_failed_attempt + timedelta(hours=expired_hours)
            if datetime.now() > expired_time:
                user.login_failed_attempts = 0
                user.login_suspended_until = None
        
        user.login_failed_attempts += 1
        user.login_last_failed_attempt = datetime.now()
        
        # Cek apakah perlu suspend
        if user.login_failed_attempts >= max_attempts:
            tz = timezone(TZ)
            suspend_until = datetime.now(tz) + timedelta(minutes=suspend_duration)
            user.login_suspended_until = suspend_until.replace(tzinfo=None)
            
            await db.commit()
            
            return {
                'is_suspended': True,
                'suspended_until': user.login_suspended_until,
                'remaining_time': f"{suspend_duration}m 0s",
                'failed_attempts': user.login_failed_attempts
            }
        
        await db.commit()
        
        return {
            'is_suspended': False,
            'suspended_until': None,
            'remaining_time': None,
            'failed_attempts': user.login_failed_attempts
        }
        
    except Exception as e:
        traceback.print_exc()
        return {'is_suspended': False, 'suspended_until': None, 'remaining_time': None, 'failed_attempts': 0}

async def reset_login_failed_attempts(db: AsyncSession, user_id: str) -> bool:
    """Reset failed attempts dan suspend status untuk login"""
    try:
        user = await db.execute(
            select(User).where(User.id == user_id)
        )
        user = user.scalar()
        
        if not user:
            return False
        
        user.login_failed_attempts = 0
        user.login_suspended_until = None
        user.login_last_failed_attempt = None
        
        await db.commit()
        return True
        
    except Exception as e:
        traceback.print_exc()
        return False