"""
Konfigurasi parameter model untuk setiap produk
File ini berisi konfigurasi yang bisa dengan mudah ditambahkan untuk produk baru
"""

# Konfigurasi produk yang akan diproses
PRODUCT_CONFIGS = {
    'sms_a2p': {
        'filter_keywords': ['sms a2p'],
        'model_params': {
            'growth': 'linear',
            'yearly_seasonality': False,
            'weekly_seasonality': False,
            'daily_seasonality': False,
            'changepoint_prior_scale': 0.01,
            'seasonality_prior_scale': 20,
            'changepoint_range': 0.75,
            'seasonality_mode': 'multiplicative',
            'interval_width': 0.8,
            'n_changepoints': 6
        },
        'seasonality_configs': [
            {'name': 'yearly', 'period': 12, 'fourier_order': 10},
            {'name': 'monthly', 'period': 30.5, 'fourier_order': 8}
        ]
    },
    'ip_transit': {
        'filter_keywords': ['ip transit'],
        'model_params': {
            'growth': 'linear',
            'changepoint_prior_scale': 0.1,
            'seasonality_mode': 'multiplicative',
            'seasonality_prior_scale': 5,
            'changepoint_range': 0.9,
            'yearly_seasonality': 15,
            'weekly_seasonality': False,
            'daily_seasonality': False
        },
        'seasonality_configs': [
            {'name': 'semester', 'period': 182.5, 'fourier_order': 3}
        ]
    },
    'voice_pots': {
        'filter_keywords': ['voice pots'],
        'model_params': {
            'growth': 'linear',
            'changepoint_prior_scale': 0.3,
            'seasonality_mode': 'multiplicative',
            'seasonality_prior_scale': 5,
            'changepoint_range': 0.9,
            'yearly_seasonality': False,
            'weekly_seasonality': False,
            'daily_seasonality': False,
            'interval_width': 0.95
        },
        'seasonality_configs': [
            {'name': '4monthly', 'period': 4*30.5, 'fourier_order': 3},
            {'name': 'monthly', 'period': 30.5, 'fourier_order': 2}
        ]
    },
    'interconnection': {
        'filter_keywords': ['interconnection'],
        'model_params': {
            'growth': 'linear',
            'yearly_seasonality': 15,
            'weekly_seasonality': False,
            'daily_seasonality': False,
            'changepoint_prior_scale': 0.05,
            'seasonality_prior_scale': 15,
            'changepoint_range': 0.8,
            'seasonality_mode': 'additive',
            'interval_width': 0.95,
            'n_changepoints': 15
        },
        'seasonality_configs': []
    },
    'astinet': {
        'filter_keywords': ['astinet'],
        'model_params': {
            'growth':'linear',
            'yearly_seasonality':False,
            'weekly_seasonality':False,
            'daily_seasonality':False,
            'changepoint_prior_scale':0.2,
            'seasonality_prior_scale':11,
            'interval_width':0.95,
            'n_changepoints':20,
            'seasonality_mode':'multiplicative'
        },
        'seasonality_configs': [
            {'name': 'monthly', 'period': 30.5, 'fourier_order': 2}
        ]
    },
    'hsi_b2b': {
        'filter_keywords': ['hsi'],
        'model_params': {
            'changepoint_prior_scale':0.05,
            'seasonality_prior_scale':10,
            'seasonality_mode':'additive',
            'changepoint_range': 0.9,
            'yearly_seasonality': 15
        },
        'seasonality_configs': []
    },
    'sip': {
        'filter_keywords': ['sip trunk'],
        'model_params': {
            'growth': 'linear',
            'changepoint_prior_scale': 0.71,  
            'seasonality_prior_scale': 4.0,           
            'changepoint_range': 0.88,              
            'seasonality_mode': 'additive',
            'yearly_seasonality': False,                
            'weekly_seasonality': False,
            'daily_seasonality': False,
            'n_changepoints': 40,                   
            'interval_width': 0.8
        },
        'seasonality_configs': [
            {'name': 'biannual', 'period': 182.5, 'fourier_order': 3},
        ]
    },
    'call_center': {
        'filter_keywords': ['call center'],
        'model_params': {
            'growth': 'linear',
            'changepoint_prior_scale': 0.05,
            'seasonality_mode': 'additive', 
            'seasonality_prior_scale': 11,
            'changepoint_range': 0.7,
            'yearly_seasonality': 15,
            'weekly_seasonality': False,
            'daily_seasonality': False,
            'n_changepoints': 6,
            'interval_width': 0.7
        },
        'seasonality_configs': [
            {'name': 'semester', 'period': 182.5, 'fourier_order': 2},
            {'name': '4monthly', 'period': 122, 'fourier_order': 3},
        ]
    },
    'vpn_ip': {
        'filter_keywords': ['vpn ip'],
        'model_params': {
            'growth': 'linear',
            'changepoint_prior_scale': 0.45,
            'seasonality_prior_scale': 42,
            'changepoint_range': 0.72,
            'seasonality_mode': 'multiplicative',
            'yearly_seasonality': False,
            'weekly_seasonality': False,
            'daily_seasonality': False,
            'n_changepoints': 22, 
            'interval_width': 0.95
        },
        'seasonality_configs': [
            {'name': 'quarterly', 'period': 91.25, 'fourier_order': 18},
            {'name': 'semester', 'period': 182.5, 'fourier_order': 9},
        ]
    },
    'metro_ethernet': {
        'filter_keywords': ['METRO'],
        'model_params': {
            'growth': 'linear',
            'changepoint_prior_scale': 0.29,
            'seasonality_prior_scale': 4.3,
            'changepoint_range': 0.93,
            'seasonality_mode': 'multiplicative',
            'yearly_seasonality': False,
            'weekly_seasonality': False,
            'daily_seasonality': False,
            'n_changepoints': 3
        },
        'seasonality_configs': [
            {'name': 'yearly_custom', 'period': 362.25, 'fourier_order': 5},
            {'name': 'quarterly', 'period': 91.25, 'fourier_order': 2},
        ]
    },
    'sl_domestik': {
        'filter_keywords': ['sl domestik'],
        'model_params': {
            'growth': 'linear',
            'changepoint_prior_scale': 0.5,
            'seasonality_mode': 'multiplicative', 
            'seasonality_prior_scale': 5,
            'changepoint_range': 0.85,
            'yearly_seasonality': 19,
            'weekly_seasonality': False,
            'daily_seasonality': False,
            'interval_width': 0.8,
            'n_changepoints': 25
        },
        'seasonality_configs': [
            {'name': 'bulanan', 'period': 30.44, 'fourier_order': 5},
            {'name': 'quarterly', 'period': 91.25, 'fourier_order': 5},
        ]
    },
    'global_link': {
        'filter_keywords': ['global link'],
        'model_params': {
            'growth': 'linear',
            'changepoint_prior_scale': 0.3,
            'seasonality_mode': 'multiplicative',
            'seasonality_prior_scale': 11,
            'changepoint_range': 0.9,
            'yearly_seasonality': 9,
            'weekly_seasonality': False,
            'daily_seasonality': False,
            'interval_width': 0.7,
            'n_changepoints': 25
        },
        'seasonality_configs': [
            {'name': 'bulanan', 'period': 30.44, 'fourier_order': 8},
            {'name': 'semester', 'period': 182.5, 'fourier_order': 3},
        ]
    },
    'collocation_dc': {
        'filter_keywords': ['collocation dc'],
        'model_params': {
            'growth': 'linear',
            'changepoint_prior_scale': 0.3,
            'seasonality_mode': 'multiplicative',
            'seasonality_prior_scale': 10,
            'changepoint_range': 0.8,
            'yearly_seasonality': 10,
            'weekly_seasonality': False,
            'daily_seasonality': False,
            'interval_width': 0.7,
            'n_changepoints': 5
        },
        'seasonality_configs': [
            {'name': 'bulanan', 'period': 30.44, 'fourier_order': 8},
        ]
    },
    'managed_sdwan_service': {
        'filter_keywords': ['MANAGED SDWAN SERVICE'],
        'model_params': {
            'growth': 'linear',
            'changepoint_prior_scale': 0.1,        
            'seasonality_mode': 'multiplicative', 
            'seasonality_prior_scale': 8.5,       
            'changepoint_range': 0.66,           
            'yearly_seasonality': 10,      
            'weekly_seasonality': False,
            'daily_seasonality': False,
            'interval_width': 0.7,
            'n_changepoints': 25
        },
        'seasonality_configs': [
            {'name': 'quarterly', 'period': 91.25, 'fourier_order': 1},
            {'name': 'semester', 'period': 182.5, 'fourier_order': 10},
        ]
    },
    'wifi_managed_service': {
        'filter_keywords': ['WIFI MANAGED SERVICE'],
        'model_params': {
            'growth': 'linear',
            'changepoint_prior_scale': 0.5,        
            'seasonality_mode': 'multiplicative', 
            'seasonality_prior_scale': 5,       
            'changepoint_range': 0.7,           
            'yearly_seasonality': 10,      
            'weekly_seasonality': False,
            'daily_seasonality': False,
            'interval_width': 0.7,
            'n_changepoints': 15
        },
        'seasonality_configs': [
            {'name': 'monthly', 'period': 30.5, 'fourier_order': 3},
        ]
    },
    'wifi_vas_digital_advertising': {
        'filter_keywords': ['WIFI VAS', 'digital advertising'], # querynya masih salah
        'model_params': {
            'growth': 'linear',
            'changepoint_prior_scale': 0.05,        
            'seasonality_mode': 'multiplicative', 
            'seasonality_prior_scale': 19,       
            'changepoint_range': 0.7,           
            'yearly_seasonality': 11,      
            'weekly_seasonality': False,
            'daily_seasonality': False,
            'interval_width': 0.7,
            'n_changepoints': 15
        },
        'seasonality_configs': [
            {'name': 'bimonthly', 'period': 61, 'fourier_order': 1},
            {'name': 'quarterly', 'period': 91.25, 'fourier_order': 3},
            {'name': 'semester', 'period': 182.5, 'fourier_order': 8},
        ]
    },
    'wifi_voucher_b2b2c': {
        'filter_keywords': ['WIFI VOUCHER'],
        'model_params': {
            'growth':'linear',
            'changepoint_prior_scale':0.05,        
            'seasonality_mode':'multiplicative', 
            'seasonality_prior_scale':20,       
            'changepoint_range':0.7,           
            'yearly_seasonality':False,      
            'weekly_seasonality':False,
            'daily_seasonality':False,
            'interval_width':0.7,
            'n_changepoints':25
        },
        'seasonality_configs': [
            {'name': 'yearly', 'period': 365.25, 'fourier_order': 10},
        ]
    },
    'wholesale_port_international_roaming': {
        'filter_keywords': ['wholesale', 'international roaming'], # querynya masih salah
        'model_params': {
            'growth': 'linear',
            'changepoint_prior_scale': 0.5,        
            'seasonality_mode': 'multiplicative', 
            'seasonality_prior_scale': 5,       
            'changepoint_range': 0.85,           
            'yearly_seasonality': 10,      
            'weekly_seasonality': False,
            'daily_seasonality': False,
            'interval_width': 0.7,
            'n_changepoints': 5
        },
        'seasonality_configs': [
            {'name': 'quarterly', 'period': 91.25, 'fourier_order': 7},
        ]
    },
    
}

# Template untuk menambahkan produk baru
PRODUCT_TEMPLATE = {
    'product_name': {
        'filter_keyword': 'keyword untuk filter data',
        'model_params': {
            'growth': 'linear',  # atau 'logistic', 'flat'
            'yearly_seasonality': False,  # atau True, atau angka
            'weekly_seasonality': False,
            'daily_seasonality': False,
            'changepoint_prior_scale': 0.01,  # 0.001 - 0.5
            'seasonality_prior_scale': 10,  # 0.01 - 10
            'changepoint_range': 0.8,  # 0.8 - 0.95
            'seasonality_mode': 'multiplicative',  # atau 'additive'
            'interval_width': 0.8,  # 0.5 - 0.95
            'n_changepoints': 25  # 0 - 100
        },
        'seasonality_configs': [
            {'name': 'yearly', 'period': 12, 'fourier_order': 10},
            {'name': 'monthly', 'period': 30.5, 'fourier_order': 8}
        ]
    }
}

# Cara menambahkan produk baru:
# 1. Copy PRODUCT_TEMPLATE
# 2. Ganti 'product_name' dengan nama produk yang sebenarnya
# 3. Sesuaikan filter_keyword dengan keyword yang ada di database
# 4. Sesuaikan parameter model berdasarkan karakteristik data
# 5. Sesuaikan seasonality_configs berdasarkan pola musiman data
# 6. Tambahkan ke PRODUCT_CONFIGS

# Contoh menambahkan produk baru:
# PRODUCT_CONFIGS['internet_service'] = {
#     'filter_keyword': 'internet service',
#     'model_params': {
#         'growth': 'linear',
#         'yearly_seasonality': 10,
#         'weekly_seasonality': False,
#         'daily_seasonality': False,
#         'changepoint_prior_scale': 0.05,
#         'seasonality_prior_scale': 15,
#         'changepoint_range': 0.85,
#         'seasonality_mode': 'multiplicative',
#         'interval_width': 0.9,
#         'n_changepoints': 15
#     },
#     'seasonality_configs': [
#         {'name': 'yearly', 'period': 12, 'fourier_order': 12},
#         {'name': 'quarterly', 'period': 91.25, 'fourier_order': 5}
#     ]
# }
