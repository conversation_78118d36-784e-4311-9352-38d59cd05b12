import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from services.predictive_analytics_service import PredictiveAnalyticsService


class TestPredictiveAnalyticsService:
    
    @pytest.fixture
    def service(self):
        """Fixture untuk service instance"""
        return PredictiveAnalyticsService()
    
    @pytest.fixture
    def sample_data(self):
        """Sample data untuk testing"""
        dates = pd.date_range(start='2019-01-01', end='2025-06-30', freq='M')
        np.random.seed(42)
        
        data = []
        for date in dates:
            data.append({
                'gl_dss_prod_cat': 'sms a2p',
                'periode_rev': date,
                'real_revenue': np.random.uniform(1000, 10000),
                'regional': 'JABODETABEK',
                'kode_regional': '01',
                'witel_name': 'JAKARTA',
                'gl_dss_prod_grp': 'SMS',
                'gl_account': '123456',
                'gl_desc': 'SMS A2P Revenue',
                'cfu': 'CFU1',
                'divisi': 'DIV1',
                'ubis': 'UBIS1',
                'segmen': 'SEG1',
                'segmen_desc': 'Segment 1',
                'subsegmen_desc': 'Sub Segment 1',
                'witel': 'WITEL1',
                'tahun': str(date.year),
                'periode': str(date.month),
                'bulan': date.strftime('%B'),
                'target_revenue': np.random.uniform(8000, 12000),
                'tgl_upd': date,
                'created_at': date
            })
        
        return pd.DataFrame(data)
    
    def test_ensure_models_directory(self, service, tmp_path):
        """Test pembuatan direktori models"""
        with patch('os.path.exists', return_value=False):
            with patch('os.makedirs') as mock_makedirs:
                service.ensure_models_directory()
                mock_makedirs.assert_called_once()
    
    def test_clean_data(self, service, sample_data):
        """Test data cleaning"""
        # Test dengan data sample
        cleaned_data = service.clean_data(sample_data)
        
        # Verifikasi hasil
        assert not cleaned_data.empty
        assert 'periode_rev' in cleaned_data.columns
        assert 'real_revenue' in cleaned_data.columns
        assert 'kode_cc' not in cleaned_data.columns  # Kolom yang di-drop
        assert 'witel' not in cleaned_data.columns     # Kolom yang di-drop
        assert 'target_revenue' not in cleaned_data.columns  # Kolom yang di-drop
        
        # Verifikasi data tidak kosong
        assert cleaned_data['regional'].notna().all()
        assert cleaned_data['kode_regional'].notna().all()
        assert cleaned_data['witel_name'].notna().all()
    
    def test_detect_and_handle_outliers(self, service, sample_data):
        """Test outlier detection dan handling"""
        # Clean data terlebih dahulu
        cleaned_data = service.clean_data(sample_data)
        
        # Test outlier handling
        result_data = service.detect_and_handle_outliers(cleaned_data)
        
        # Verifikasi hasil
        assert 'real_revenue_clean' in result_data.columns
        assert not result_data['real_revenue_clean'].isna().all()
    
    def test_replace_outliers(self, service):
        """Test outlier replacement"""
        # Buat sample data dengan outlier
        data = pd.Series([1, 2, 3, 100, 4, 5, 6])  # 100 adalah outlier
        outlier_indices = np.array([3])
        
        # Test dengan method auto
        result = service.replace_outliers(data, outlier_indices, method='auto')
        
        # Verifikasi outlier diganti
        assert result.iloc[3] != 100
        assert len(result) == len(data)
    
    @patch('joblib.dump')
    @patch('os.path.join')
    def test_train_prophet_model(self, mock_join, mock_dump, service, sample_data):
        """Test model training"""
        # Mock path
        mock_join.return_value = '/fake/path/model.pkl'
        
        # Clean data
        cleaned_data = service.clean_data(sample_data)
        cleaned_data = service.detect_and_handle_outliers(cleaned_data)
        
        # Test training
        config = service.product_configs['sms_a2p']
        model_path, model_version = service.train_prophet_model(cleaned_data, 'sms_a2p', config)
        
        # Verifikasi
        assert model_path == '/fake/path/model.pkl'
        assert model_version.startswith('v')
        assert '.' in model_version
        mock_dump.assert_called_once()
    
    @patch('joblib.load')
    def test_run_prediction(self, mock_load, service, sample_data):
        """Test prediction generation"""
        # Mock model
        mock_model = Mock()
        mock_model.make_future_dataframe.return_value = pd.DataFrame({
            'ds': pd.date_range(start='2025-07-01', periods=11, freq='M')
        })
        mock_model.predict.return_value = pd.DataFrame({
            'ds': pd.date_range(start='2025-07-01', periods=11, freq='M'),
            'yhat': np.random.uniform(5000, 15000, 11)
        })
        mock_load.return_value = mock_model
        
        # Mock save_predictions_to_db
        with patch.object(service, 'save_predictions_to_db'):
            # Prepare data
            cleaned_data = service.clean_data(sample_data)
            cleaned_data = service.detect_and_handle_outliers(cleaned_data)
            df_prophet = cleaned_data.rename(columns={
                'periode_rev': 'ds',
                'real_revenue_clean': 'y'
            })
            
            # Test prediction
            result = service.run_prediction(
                '/fake/model.pkl', 
                df_prophet, 
                'sms a2p'
            )
            
            # Verifikasi
            assert not result.empty
            assert 'prediction_revenue' in result.columns
            assert 'prediction_direction' in result.columns
            assert 'absolute_difference' in result.columns
            assert 'percentage_difference' in result.columns
    
    def test_process_product(self, service, sample_data):
        """Test processing satu produk"""
        # Mock semua method yang dipanggil
        with patch.object(service, 'get_data_from_vgla_all', return_value=sample_data):
            with patch.object(service, 'clean_data', return_value=sample_data):
                with patch.object(service, 'detect_and_handle_outliers', return_value=sample_data):
                    with patch.object(service, 'train_prophet_model', return_value=('/fake/model.pkl', 'v2025.08')):
                        with patch.object(service, 'run_prediction'):
                            # Test processing
                            service.process_product('sms_a2p', service.product_configs['sms_a2p'])
                            
                            # Verifikasi semua method dipanggil
                            service.get_data_from_vgla_all.assert_called_once()
                            service.clean_data.assert_called_once()
                            service.detect_and_handle_outliers.assert_called_once()
                            service.train_prophet_model.assert_called_once()
                            service.run_prediction.assert_called_once()
    
    def test_run_full_retrain_and_prediction(self, service):
        """Test full retrain dan prediction"""
        # Mock process_product
        with patch.object(service, 'process_product') as mock_process:
            # Test full process
            service.run_full_retrain_and_prediction()
            
            # Verifikasi semua produk diproses
            assert mock_process.call_count == len(service.product_configs)
            
            # Verifikasi produk yang diproses
            called_products = [call[0][0] for call in mock_process.call_args_list]
            expected_products = list(service.product_configs.keys())
            assert set(called_products) == set(expected_products)
    
    def test_error_handling_in_process_product(self, service):
        """Test error handling dalam process_product"""
        # Mock get_data_from_vgla_all untuk raise error
        with patch.object(service, 'get_data_from_vgla_all', side_effect=Exception("Database error")):
            # Test error handling
            with pytest.raises(Exception):
                service.process_product('sms_a2p', service.product_configs['sms_a2p'])
    
    def test_error_handling_in_full_process(self, service):
        """Test error handling dalam full process"""
        # Mock process_product untuk raise error pada produk pertama
        with patch.object(service, 'process_product', side_effect=Exception("Processing error")):
            # Test error handling
            with pytest.raises(Exception):
                service.run_full_retrain_and_prediction()
    
    def test_configuration_validation(self, service):
        """Test validasi konfigurasi produk"""
        # Verifikasi semua produk memiliki konfigurasi yang diperlukan
        required_keys = ['filter_keyword', 'model_params', 'seasonality_configs']
        
        for product_key, config in service.product_configs.items():
            for key in required_keys:
                assert key in config, f"Missing {key} in {product_key} config"
            
            # Verifikasi model_params
            assert 'growth' in config['model_params']
            assert 'seasonality_mode' in config['model_params']
            
            # Verifikasi seasonality_configs
            assert isinstance(config['seasonality_configs'], list)
            for seasonality in config['seasonality_configs']:
                assert 'name' in seasonality
                assert 'period' in seasonality
                assert 'fourier_order' in seasonality


if __name__ == "__main__":
    pytest.main([__file__])
