"""
Service untuk retrain model Predictive Analytics untuk semua produk
Menggunakan konfigurasi dari model_parameters.py
"""

import pandas as pd
import numpy as np
import joblib
import os
from datetime import datetime, timedelta
from sqlalchemy import text
from prophet import Prophet
from prophet.diagnostics import cross_validation, performance_metrics
from statsmodels.tsa.seasonal import STL
from scipy.stats import shapiro
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from itertools import product
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
import warnings
from typing import Dict, List, Tuple
import logging

# Import konfigurasi
from config.model_parameters import PRODUCT_CONFIGS
from config.model_parameters import PRODUCT_TEMPLATE
from models import sync_engine

warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Sama dengan notebook Cell 11: Set style untuk visualisasi
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 6)
plt.rcParams['font.size'] = 10

logger.info("✅ Semua library berhasil diimport!")
logger.info("📊 Sistem Prediksi Revenue dengan Prophet siap digunakan")


class PredictiveAnalyticsRetrainService:
    """
    Service untuk retrain model Predictive Analytics
    """
    
    def __init__(self, storage_path: str = "storage/models"):
        """
        Inisialisasi service
        
        Args:
            storage_path: Path untuk menyimpan model
        """
        self.storage_path = storage_path
        self.engine = sync_engine
        
        # Buat direktori storage jika belum ada
        os.makedirs(storage_path, exist_ok=True)
        
    def connect_database(self) -> bool:
        """
        Test koneksi database
        
        Returns:
            bool: True jika berhasil, False jika gagal
        """
        try:
            # Test koneksi
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.info("✅ Koneksi database berhasil")
            return True
        except Exception as e:
            logger.error(f"❌ Gagal koneksi database: {e}")
            return False
    
    def get_data_from_vgla_all(self, filter_keywords: List[str]) -> pd.DataFrame:
        """
        Ambil data dari tabel v_gla_all berdasarkan filter keyword
        Menggunakan filter langsung di query untuk efisiensi resource
        """
        if not filter_keywords:
            logger.warning("⚠️ filter_keywords kosong, mengembalikan DataFrame kosong.")
            return pd.DataFrame()

        try:
            logger.info(f"📊 Mengambil data untuk produk: {filter_keywords}")
            
            where_clauses = [f"COALESCE(gl_dss_prod_cat, '') ILIKE :keyword_{i}" for i in range(len(filter_keywords))]
            
            full_where_clause = " OR ".join(where_clauses)

            # Mapping parameter untuk query
            params = {
                f"keyword_{i}": f"%{keyword.strip()}%"
                for i, keyword in enumerate(filter_keywords)
            }

            query_string = f"""
                SELECT *
                FROM satgas_ai.v_gla_all
                WHERE {full_where_clause}
                ORDER BY periode_rev
            """
            print(f"\n\nExecuting query: {query_string}\nWith params: {params}\n\n")

            query = text(query_string)
            
            # Execute query dengan parameter
            with self.engine.connect() as conn:
                result = conn.execute(query, params)
                df = pd.DataFrame(result.fetchall(), columns=result.keys())
            
            logger.info(f"✅ Data berhasil diambil: {len(df):,} rows untuk keyword '{filter_keywords}'")
            
            # Debug: Cek struktur data
            if len(df) > 0:
                logger.info(f"📊 Sample data columns: {list(df.columns)}")
                logger.info(f"📊 Sample gl_dss_prod_cat values: {df['gl_dss_prod_cat'].unique()[:5]}")
                logger.info(f"📊 Data types: {df.dtypes.to_dict()}")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Gagal mengambil data: {e}")
            raise
            
        except Exception as e:
            logger.error(f"❌ Gagal mengambil data: {e}")
            raise
    
    def clean_data(self, df: pd.DataFrame, product_name: str) -> pd.DataFrame:
        """
        Bersihkan data untuk training
        Sama persis dengan notebook func_retrain
        """
        try:
            logger.info(f"🧹 Membersihkan data untuk {product_name}")
            
            df_filtered = df.copy() 
            logger.info(f"Data setelah filter '{product_name}': {len(df_filtered)} rows")
            
            columns_to_drop = ['kode_cc', 'witel', 'target_revenue', 'sumber_table']
            df_clean = df_filtered.drop(columns=[col for col in columns_to_drop if col in df_filtered.columns], errors='ignore')
            
            cols_to_check = ['regional', 'kode_regional', 'witel_name']
            df_clean = df_clean[~df_clean[cols_to_check].apply(lambda row: any(str(val).strip() == '' for val in row), axis=1)].copy()

            df_clean['periode_rev'] = pd.to_datetime(df_clean['periode_rev'])
            df_clean['periode_rev'] = df_clean['periode_rev'] + pd.offsets.MonthEnd(0)
            
            logger.info(f"Data setelah check kosong: {len(df_clean)} rows")
            # take 5 from df_clean
            logger.info(f"Contoh data bersih:\n{df_clean.head(3)}")
            
            df_monthly = (df_clean.groupby('periode_rev', as_index=False)['real_revenue'].sum())
            # convert to float64
            df_monthly['real_revenue'] = pd.to_numeric(df_monthly['real_revenue'], errors='coerce')

            logger.info(f"Data setelah group by: \n{df_monthly.info()}")
            
            df_valid_revenue = df_monthly[(df_monthly['real_revenue'].notna()) & (df_monthly['real_revenue'] != 0)]
        
            if df_valid_revenue.empty:
                logger.error(f"❌ Tidak ditemukan data revenue yang valid (> 0) untuk produk {product_name}. Proses dihentikan.")
                return pd.DataFrame() # Mengembalikan DataFrame kosong

            # Tentukan tanggal mulai dan akhir dari data yang valid
            # start_date = '2019-01-31'
            # end_date = '2025-05-30' 
            start_date = df_valid_revenue['periode_rev'].min()
            end_date = df_valid_revenue['periode_rev'].max()
            
            logger.info(f"📅 Rentang tanggal data dinamis terdeteksi: {start_date.strftime('%Y-%m')} hingga {end_date.strftime('%Y-%m')}")
            
            expected_months = pd.date_range(start=start_date, end=end_date, freq='M')
            df_template = pd.DataFrame({'periode_rev': expected_months})
            
            df_monthly = df_template.merge(df_monthly, on='periode_rev', how='left')
            
            logger.info(f"✅ Data berhasil dibersihkan: {len(df_monthly)} bulan")
            return df_monthly
            
        except Exception as e:
            logger.error(f"❌ Gagal membersihkan data: {e}")
            logger.error(f"❌ Error details: {type(e).__name__}: {str(e)}")
            raise
    
    def detect_and_handle_outliers(self, df: pd.DataFrame, product_name: str) -> pd.DataFrame:
        """
        Deteksi dan handle outliers menggunakan STL decomposition
        Sama persis dengan notebook func_retrain Cell 15-17
        """
        try:
            logger.info(f"🔍 Deteksi outliers untuk {product_name}")
            
            stl = STL(df['real_revenue'].fillna(0), period=12) # fillna(0) untuk handle missing months
            res = stl.fit()
            resid = res.resid
            
            def detect_outliers_modified_zscore(data, threshold=3.5):
                median = np.median(data)
                mad = np.median(np.abs(data - median))
                if mad == 0:
                    return np.array([])
                modified_z_scores = 0.6745 * (data - median) / mad
                outlier_indices = np.where(np.abs(modified_z_scores) > threshold)[0]
                return outlier_indices
            
            outlier_idx = detect_outliers_modified_zscore(resid, threshold=3.0)
            logger.info(f"Outlier ke- {outlier_idx}")
            
            if len(outlier_idx) > 0:
                logger.info(f"📊 Ditemukan {len(outlier_idx)} outliers")
                
                df_clean = df.copy()
                df_clean['real_revenue_clean'] = self._replace_outliers(
                    df_clean['real_revenue'], outlier_idx, method='auto'
                )
                
                return df_clean
            else:
                logger.info("✅ Tidak ada outliers yang terdeteksi")
                # Tetap buat kolom real_revenue_clean untuk konsistensi
                df['real_revenue_clean'] = df['real_revenue']
                return df
                
        except Exception as e:
            logger.error(f"❌ Gagal handle outliers: {e}")
            # Jika gagal, return data original
            df['real_revenue_clean'] = df['real_revenue']
            return df
    
    def _replace_outliers(self, data: pd.Series, outlier_indices: np.ndarray, method: str = 'auto') -> pd.Series:
        """
        Ganti outlier dengan nilai yang sesuai
        
        Args:
            data: Series data
            outlier_indices: Indeks outlier
            method: Metode replacement ('auto', 'mean', 'median', atau nilai custom)
            
        Returns:
            pd.Series: Data dengan outlier yang sudah diganti
        """
        data_clean = data.copy()
        
        data_wo_outliers = data.drop(index=outlier_indices)
        
        if method == 'auto':
            try:
                stat, p = shapiro(data_wo_outliers)
                method_used = 'mean' if p > 0.05 else 'median'
            except:
                method_used = 'median'
        else:
            method_used = method
        
        # Hitung nilai pengganti
        if method_used == 'mean':
            replacement_value = data_wo_outliers.mean()
        elif method_used == 'median':
            replacement_value = data_wo_outliers.median()
        elif isinstance(method_used, (int, float)):
            replacement_value = method_used
        else:
            raise ValueError("Invalid method")
        
        # Ganti outlier
        data_clean.iloc[outlier_indices] = replacement_value
        return data_clean

    def _select_best_interpolation_method(self, df: pd.DataFrame, column_name: str) -> str:
        clean_data = df[df[column_name].notna()][column_name]
    
        if len(clean_data) < 3:
            return 'linear'  # Not enough data for complex methods
        
        # Calculate data characteristics
        data_length = len(clean_data)
        coefficient_of_variation = clean_data.std() / clean_data.mean() if clean_data.mean() != 0 else 0
        
        # Check for trend
        x = np.arange(len(clean_data))
        correlation = np.corrcoef(x, clean_data.values)[0, 1] if len(clean_data) > 1 else 0
        
        # Check for monotonicity
        is_monotonic = clean_data.is_monotonic_increasing or clean_data.is_monotonic_decreasing
        
        
        # Selection rules
        if data_length < 4:
            method = 'linear'
            reason = "Few data points"
        elif is_monotonic and abs(correlation) > 0.8:
            method = 'linear'
            reason = "Strong linear trend"
        elif coefficient_of_variation < 0.2:  # Low variability
            method = 'linear'
            reason = "Low variability, stable data"
        elif coefficient_of_variation > 0.5:  # High variability
            method = 'cubic'
            reason = "High variability, need smooth curve"
        elif data_length >= 6:
            method = 'polynomial'
            reason = "Sufficient data for polynomial fit"
        else:
            method = 'linear'
            reason = "Default choice"
        
        logger.info(f"  Selected method: {method} ({reason})")
        return method

    def _interpolate_invalid(self, df: pd.DataFrame, col: str, method: str = 'auto') -> pd.DataFrame:
        df_copy = df.copy()

        if method == 'auto':
            if pd.api.types.is_numeric_dtype(df_copy[col]):
                method_use = 'linear'
            else:
                raise ValueError(f"Tipe data kolom {col} tidak bisa diinterpolasi")
        else:
            method_use = method

        invalid_mask = df_copy[col] <= 0
        df_copy.loc[invalid_mask, col] = np.nan
        df_copy[col] = df_copy[col].interpolate(method=method_use, limit_direction='both')

        # Kalau masih ada NaN (misal di ujung), isi dengan forward/backward fill
        if df_copy[col].isna().any():
            df_copy[col] = df_copy[col].fillna(method='bfill').fillna(method='ffill')

        return df_copy

    def prepare_prophet_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Siapkan data untuk Prophet model
        Auto pilih real_revenue atau real_revenue_clean berdasarkan outlier detection
        """
        if 'real_revenue_clean' in df.columns:
            y_column = 'real_revenue_clean'
            logger.info("✅ Menggunakan real_revenue_clean (outlier sudah dihandle)")
        else:
            y_column = 'real_revenue'
            logger.info("✅ Menggunakan real_revenue original (tidak ada outlier)")
        df_prophet = df.rename(columns={
            'periode_rev': 'ds',
            y_column: 'y'  
        })
        logger.info("Info data frame prophet")
        df_prophet.info()
        logger.info(f"Data setelah rename: {len(df_prophet)} rows")
        logger.info(f"Kolom y menggunakan: {y_column}")
        
        return df_prophet
    
    def create_and_train_prophet_model(self, df_train: pd.DataFrame, product_config: dict, product_name: str) -> Prophet:
        """
        Buat dan train Prophet model
        Sama persis dengan notebook func_retrain Cell 20-21
        """
        try:
            logger.info(f"🤖 Membuat model Prophet untuk {product_name}")

            if product_config and 'model_params' in product_config:
                model_params = product_config['model_params']
                print(f"\n\n🔧 Menggunakan model params dari config: {model_params}\n\n")
                model = Prophet(**model_params)
                
                seasonality_configs = product_config.get('seasonality_configs', [])
                for seasonality in seasonality_configs:
                    model.add_seasonality(
                        name=seasonality['name'],
                        period=seasonality['period'],
                        fourier_order=seasonality['fourier_order']
                    )

            logger.info(f"🔄 Training model {product_name}...")
            model.fit(df_train)
            logger.info(f"✅ Model {product_name} berhasil ditrain")
            
            return model
            
        except Exception as e:
            logger.error(f"❌ Gagal train model {product_name}: {e}")
            raise
    
    def save_model_locally(self, model: Prophet, product_name: str) -> str:
        """
        Simpan model ke local storage
        Sama persis dengan notebook func_retrain Cell 22
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d")
            filename = f"vgla_{product_name}_prophet_{timestamp}.pkl"
            filepath = os.path.join(self.storage_path, filename)
            
            # Save model
            joblib.dump(model, filepath)
            logger.info(f"✅ Model berhasil disimpan dengan joblib: {filename}")
            
            return filepath
            
        except Exception as e:
            logger.error(f"❌ Gagal simpan model: {e}")
            raise
    
    def run_prediction_and_save_to_db(self, model: Prophet, df_train: pd.DataFrame, 
                                    product_name: str, horizon: int = 11) -> bool:
        """
        Jalankan prediksi dan simpan hasil ke database
        Sama persis dengan notebook func_retrain Cell 23-24
        """
        try:
            logger.info(f"🔮 Menjalankan prediksi untuk {product_name}")

            future_df = model.make_future_dataframe(periods=horizon, freq='M')
            
            forecast = model.predict(future_df)
            
            last_train_date = df_train['ds'].max()
            logger.info(f"Last train date: {last_train_date}")
            
            forecast_future = forecast[forecast['ds'] > last_train_date]
            
            
            if forecast_future.empty:
                logger.warning(f"❌ Forecast future kosong. Cek horizon atau data training.")
                return False
            
            previous_revenue = df_train['y'].iloc[-1]
            
            df_result = forecast_future[['ds', 'yhat']].copy()
            df_result['previous_revenue'] = previous_revenue
            df_result['prediction_revenue'] = df_result['yhat'].round(2)
            
            previous_revenue_float = float(previous_revenue)
            df_result['absolute_difference'] = (
                df_result['prediction_revenue'] - previous_revenue_float
            ).abs()
            
            df_result['percentage_difference'] = (
                (df_result['prediction_revenue'] - previous_revenue_float) / previous_revenue_float
            ).round(4)
            
            df_result['prediction_direction'] = df_result['prediction_revenue'].apply(
                lambda x: 'up' if x > previous_revenue_float else ('down' if x < previous_revenue_float else 'same')
            )
            
            # Generate model version
            model_version = f"v{datetime.now().strftime('%Y%m%d')}"
            
            df_result['gl_dss_prod_cat'] = product_name.upper()
            df_result['actual_revenue'] = None
            df_result['model_version'] = model_version
            df_result = df_result.rename(columns={'ds': 'period_date'})
            
            # Hapus data lama dengan versi yang sama sebelum insert
            self._delete_old_predictions(product_name, model_version)
            
            cols_to_insert = [
                'gl_dss_prod_cat', 'previous_revenue', 'period_date',
                'actual_revenue', 'prediction_revenue', 'prediction_direction',
                'absolute_difference', 'percentage_difference', 'model_version'
            ]
            
            df_to_db = df_result[cols_to_insert]
            
            df_to_db.to_sql(
                'predictive_analytics',
                con=self.engine,
                schema='satgas_ai',
                if_exists='append',
                index=False
            )
            
            logger.info(f"✅ Prediksi untuk '{product_name.upper()}' berhasil disimpan.")
            return True
            
        except Exception as e:
            logger.error(f"❌ Gagal prediksi dan simpan {product_name}: {e}")
            return False
    
    def _check_existing_predictions(self, product_name: str, model_version: str) -> int:
        """
        Cek jumlah data existing dengan versi yang sama
        
        Returns:
            int: Jumlah data existing
        """
        try:
            check_query = text("""
                SELECT COUNT(*) FROM satgas_ai.predictive_analytics 
                WHERE gl_dss_prod_cat = :product_name 
                AND model_version = :model_version
            """)
            
            with self.engine.connect() as conn:
                result = conn.execute(check_query, {
                    "product_name": product_name.upper(),
                    "model_version": model_version
                })
                count = result.scalar()
            
            return count
                
        except Exception as e:
            logger.warning(f"⚠️ Gagal cek data existing: {e}")
            return 0
    
    def _delete_old_predictions(self, product_name: str, model_version: str):
        """
        Hapus data lama dengan versi yang sama sebelum insert data baru
        """
        try:
            # Hapus data lama dengan gl_dss_prod_cat dan model_version yang sama
            delete_query = text("""
                DELETE FROM satgas_ai.predictive_analytics 
                WHERE gl_dss_prod_cat = :product_name 
                AND model_version = :model_version
            """)
            
            with self.engine.connect() as conn:
                result = conn.execute(delete_query, {
                    "product_name": product_name.upper(),
                    "model_version": model_version
                })
                deleted_rows = result.rowcount
                conn.commit()
            
            if deleted_rows > 0:
                logger.info(f"🗑️ Berhasil hapus {deleted_rows} data lama dengan versi {model_version}")
            else:
                logger.info(f"ℹ️ Tidak ada data lama dengan versi {model_version} untuk dihapus")
                
        except Exception as e:
            logger.warning(f"⚠️ Gagal hapus data lama: {e}")
    
    def retrain_single_product(self, product_name: str, product_config: dict) -> bool:
        """
        Retrain model untuk satu produk
        Sama persis dengan notebook func_retrain step by step
        """
        try:
            logger.info(f"🚀 Memulai retrain untuk produk: {product_name}")
            
            df_raw = self.get_data_from_vgla_all(product_config['filter_keywords'])

            if len(df_raw) == 0:
                logger.error(f"❌ Tidak ada data untuk produk {product_name}")
                return False
                
            df_clean = self.clean_data(df_raw, product_name)
            
            df_monthly = self.detect_and_handle_outliers(df_clean, product_name)
            bad_rows = df_monthly[df_monthly['real_revenue_clean'] <= 0]
            logger.info(f"Baris data yang invalid {bad_rows}")
            if len(bad_rows) > 0:
                df_interpolated = self._interpolate_invalid(df_monthly, 'real_revenue', method='auto')
                bad_rows = df_interpolated[df_interpolated['real_revenue'] <= 0]
                logger.info(f"Baris interpolasi yang invalid {bad_rows}")
                df_prophet = self.prepare_prophet_data(df_interpolated)
            else:
                df_prophet = self.prepare_prophet_data(df_monthly)
            logger.info(f"\n\nData Prophet untuk {product_name}:\n{df_prophet.head()}\n\n")
            df_prophet.info()
            
            cutoff_date = df_prophet['ds'].iloc[-3]
            df_train = df_prophet[df_prophet['ds'] < cutoff_date]
            df_test = df_prophet[df_prophet['ds'] >= cutoff_date]
            
            logger.info(f"📊 Data training: {len(df_train)} bulan, Data test: {len(df_test)} bulan")
            df_train.info()
    
            model = self.create_and_train_prophet_model(df_train, product_config, product_name)
            
            model_path = self.save_model_locally(model, product_name)
            
            prediction_success = self.run_prediction_and_save_to_db(
                model, df_train, product_name, horizon=11
            )
            
            if prediction_success:
                logger.info(f"✅ Retrain {product_name} berhasil selesai")
                return True
            else:
                logger.warning(f"⚠️ Retrain {product_name} selesai tapi prediksi gagal")
                return False
                
        except Exception as e:
            logger.error(f"❌ Retrain {product_name} gagal: {e}")
            return False
    
    def retrain_all_products(self) -> Dict[str, bool]:
        """
        Retrain semua produk yang ada di konfigurasi
        
        Returns:
            Dict[str, bool]: Hasil retrain untuk setiap produk
        """
        if not self.connect_database():
            logger.error("❌ Tidak bisa koneksi ke database")
            return {}
        
        results = {}
        total_products = len(PRODUCT_CONFIGS)
        
        logger.info(f"🎯 Memulai retrain untuk {total_products} produk")
        
        for i, (product_name, product_config) in enumerate(PRODUCT_CONFIGS.items(), 1):
            logger.info(f"📋 Progress: {i}/{total_products} - {product_name}")
            
            try:
                success = self.retrain_single_product(product_name, product_config)
                results[product_name] = success
                
                if success:
                    logger.info(f"✅ {product_name}: SUCCESS")
                else:
                    logger.error(f"❌ {product_name}: FAILED")
                    
            except Exception as e:
                logger.error(f"❌ {product_name}: ERROR - {e}")
                results[product_name] = False
        
        successful = sum(results.values())
        failed = len(results) - successful
        
        logger.info(f"📊 SUMMARY RETRAIN:")
        logger.info(f"   Total produk: {total_products}")
        logger.info(f"   Berhasil: {successful}")
        logger.info(f"   Gagal: {failed}")
        
        return results
    
    def cleanup_old_models(self, keep_days: int = 30):
        """
        Hapus model lama untuk menghemat storage
        
        Args:
            keep_days: Jumlah hari untuk menyimpan model
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=keep_days)
            
            for filename in os.listdir(self.storage_path):
                if filename.endswith('.pkl'):
                    filepath = os.path.join(self.storage_path, filename)
                    file_modified = datetime.fromtimestamp(os.path.getmtime(filepath))
                    
                    if file_modified < cutoff_date:
                        os.remove(filepath)
                        logger.info(f"🗑️ Model lama dihapus: {filename}")
                        
        except Exception as e:
            logger.error(f"❌ Gagal cleanup model lama: {e}")


def run_predictive_analytics_retrain_sync():
    """
    Fungsi untuk menjalankan retrain secara synchronous
    Bisa dipanggil dari endpoint atau scheduler
    """
    try:
        service = PredictiveAnalyticsRetrainService(
            storage_path="storage/models"
        )
        
        results = service.retrain_all_products()
        
        service.cleanup_old_models(keep_days=30)
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Error dalam retrain service: {e}")
        return {}


if __name__ == "__main__":
    results = run_predictive_analytics_retrain_sync()
    print("Retrain Results:", results)