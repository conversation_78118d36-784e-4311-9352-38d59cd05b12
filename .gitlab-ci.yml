stages:
 - build
 - sonarqube-check
 - sonarqube-vulnerability-report
 - deploy

variables:
  DOCKER_DRIVER: overlay2
  CONTAINER_IMAGE_REPO: satgas-ai-pgm/be-dcs-dashboard-dev
  CONTAINER_IMAGE: satgas-ai-pgm/be-dcs-dashboard-dev:latest
  CONTAINER_RELEASE_IMAGE: harbor.telkom.co.id/satgas-ai-pgm/be-dcs-dashboard-dev:latest
  DOCKER_TLS_CERTDIR: ""
  DOCKER_REPO : harbor.telkom.co.id
  USER_DOCKER : putro_sigma
  PROJECT_DEV: dcs-dashboard-dev
  DEP_DEV2: be-dcs-dashboard-dev

build-image:
  stage: build
  only:
    - development
  tags:
    - dit
    - docker
  script:
    - docker login $DOCKER_REPO --username $USER_DOCKER --password $PASSWORD_REPO
    - docker build -t $CONTAINER_IMAGE .
    - docker tag $CONTAINER_IMAGE_REPO $CONTAINER_RELEASE_IMAGE
    - docker push $CONTAINER_RELEASE_IMAGE

sonarqube-check-development:
  stage: sonarqube-check
  tags:
    - dit
    - docker
  image: 
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script: 
    - echo "sonar.projectKey=satgasai_be-dcs-dashboard_be-dcs-dashboard_7129db9a-adc8-4c6d-878c-4b3af81b0764" >> sonar-project.properties
    - echo "sonar.sources=." >> sonar-project.properties
    - echo "sonar.login=$SONAR_TOKEN" >> sonar-project.properties
    - sonar-scanner
  retry: 2
  artifacts:
    when: always
    expire_in: 1 week
  rules:
    - if: '$CI_COMMIT_BRANCH == "development"'
  allow_failure: true


sonarqube-vulnerability-report:
  stage: sonarqube-vulnerability-report
  image: curlimages/curl:latest
  only:
    - development
  script:
    - >
      curl -u "${SONAR_TOKEN}:" 
      "${SONAR_HOST_URL}/api/issues/gitlab_sast_export?projectKey=satgasai_be-dcs-dashboard_be-dcs-dashboard_7129db9a-adc8-4c6d-878c-4b3af81b0764&branch=${CI_COMMIT_BRANCH}&pullRequest=${CI_MERGE_REQUEST_IID}" 
      -o gl-sast-sonar-report.json
  allow_failure: true
  artifacts:
    expire_in: 1 day
    reports:
      sast: gl-sast-sonar-report.json
    
deploy-image:
  stage: deploy
  only:
    - development
  tags:
    - myihx2
  script:
    - echo "This for job rollout deploy"
  #  - oc login --token=$OC_TOKEN_DEV --server=$OPENSHIFT --insecure-skip-tls-verify
    - oc login -u $OC_LOGIN -p $OC_PASS --server=$OPENSHIFT --insecure-skip-tls-verify
    - oc project $PROJECT_DEV
    - oc rollout restart deployment/$DEP_DEV2