# file_logging_config.py
import logging
import os
from core.logging_config import <PERSON><PERSON><PERSON><PERSON>att<PERSON>


def add_file_handler(logger_name: str = "be-dcs-dashboard", log_file: str = "logs/file.log", level: str = None):
    """
    Tambahkan file handler ke logger tertentu untuk menyimpan log ke file terpisah.
    """
    logger = logging.getLogger(logger_name)
    if not level:
        level = os.environ.get("LOG_LEVEL", "INFO").upper()
    file_handler = logging.FileHandler(log_file, encoding="utf-8")
    file_handler.setLevel(getattr(logging, level, logging.INFO))
    file_handler.setFormatter(JSONFormatter())
    logger.addHandler(file_handler)
    return logger

# Contoh penggunaan:
add_file_handler("be-dcs-dashboard", "file.log")
add_file_handler("be-dcs-dashboard.auth", "auth.log")
add_file_handler("be-dcs-dashboard.qdrant", "qdrant.log")
add_file_handler("be-dcs-dashboard.rbac", "rbac.log")
add_file_handler("be-dcs-dashboard.docling", "docling.log")
add_file_handler("be-dcs-dashboard.file", "file-uploads.log")
add_file_handler("be-dcs-dashboard.document_processor", "document-processor.log")