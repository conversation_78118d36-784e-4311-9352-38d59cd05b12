# DCS DASHBOARD TELKOM AI

## Penting
server sigma dan telkom ada perbedaan code
1. docker file
2. models/__init__.py

## Fitur

- FastAPI sebagai backend utama
- Integrasi PostgreSQL (via SQLAlchemy/asyncpg)
- Redis untuk cache/token
- Sentry untuk error monitoring
- File storage (Minio/local)
- JWT Authentication
- External Data Crawling (BigSocial API)
- Automated PDF Report Generation
- RabbitMQ Event Notification

## Requirements

- Python 3.11+
- PostgreSQL
- Redis
- (Opsional) Minio untuk file storage

## Instalasi

1. Clone repository ini.
2. Install dependencies menggunakan Poetry:
   ```bash
   poetry install --no-root
   ```
3. Copy file `.env.example` (jika ada) menjadi `.env` dan sesuaikan konfigurasi environment (lihat variabel di `settings.py`).
4. Jalankan migrasi database jika diperlukan.

## Cara Menjalankan

1. Jalankan migrasi database:
```bash
python migrate.py
```

2. (Opsional) Tambahkan keywords default:
```bash
python seed_keywords.py
```

3. Jalankan server FastAPI menggunakan Uvicorn:
```bash
poetry run uvicorn main:app --reload
```

Server akan berjalan di `http://localhost:8000`.

## Endpoints

- `/auth/*` : Endpoint otentikasi (lihat detail di folder `routes/auth.py`)
- `/keywords/*` : Endpoint manajemen keywords untuk BigSocial crawling
- `/test-bigsocial-scheduler` : Endpoint testing manual BigSocial crawling
- `/docs` : Swagger UI (hanya di mode development)

## External Data Crawling

Untuk informasi lengkap tentang fitur external data crawling, lihat [README_EXTERNAL_DATA_CRAWLING.md](README_EXTERNAL_DATA_CRAWLING.md)

## Struktur Project

- `main.py` : Entry point aplikasi
- `core/` : Modul utilitas, logging, email, dsb
- `models/` : Model database
- `repository/` : Repository pattern untuk akses data
- `routes/` : Routing FastAPI
- `schemas/` : Skema request/response

---

Silakan sesuaikan bagian konfigurasi environment sesuai kebutuhan project Anda.
