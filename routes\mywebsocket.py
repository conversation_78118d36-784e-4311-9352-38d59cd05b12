from typing import Union, Optional, List
from fastapi import APIRouter, Query, Depends, status, WebSocket, WebSocketDisconnect
from core.security import (
    get_user_from_jwt_token,
    get_user_from_jwt_token_ws
)
import json
from datetime import datetime, date
from settings import TZ
from pytz import timezone
from sqlalchemy.orm import Session
from websocket.ws_connection import ws_manager
from schemas.common import (
    BadRequestResponse,
    NotFoundResponse,
    UnauthorizedResponse,
    ForbiddenResponse,
    NoContentResponse,
    InternalServerErrorResponse,
)
from core.responses import (
    NoContent,
    Ok,
    Unauthorized,
    common_response,
    Created,
    InternalServerError,
    NotFound,
    BadRequest,
    NotImplemented,
)
from models import get_db
from models.User import User
import asyncio

router = APIRouter(tags=["WS Task"])


@router.websocket("/")
async def new_task_notifier(
    websocket: WebSocket,
    # token: str = None,
    db: Session = Depends(get_db),
    token: Union[str, None] = Query(default=None),
):
    #token required
    if token is None:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
    user: Optional[User] = await get_user_from_jwt_token_ws(db=db, jwt_token=token)
    if user == None:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
    await ws_manager.connect(
        websocket=websocket,
        listen_channel=(
            f"task:{user.pengirim_penerima_id}"
            if user.pengirim_penerima_id != None
            else "task:"
        ),
        user_id=user.id,
        # System Inventory
        # role_ids=[x.id for x in user.roles],
        # System Ticketing
        role_id=int(user.roles[0].id if user.roles else None),
    )
    try:
        while True:
            # _ = await websocket.receive_text()
            rcv_text = await websocket.receive_text()
            print('incoming message :', rcv_text)
    except WebSocketDisconnect:
        ws_manager.disconnect(websocket)


@router.get(
    "/send_message",
    responses={
        "400": {"model": BadRequestResponse},
        "401": {"model": UnauthorizedResponse},
        "403": {"model": ForbiddenResponse},
        "500": {"model": InternalServerErrorResponse},
    },
)
async def send_message(listen_channel: str):
    await ws_manager.send_to_channel(
        listen_channel=listen_channel, message="wah ada task baru nih"
    )
    return common_response(NoContent())
@router.get(
    "/send_personal_message",
    responses={
        "400": {"model": BadRequestResponse},
        "401": {"model": UnauthorizedResponse},
        "403": {"model": ForbiddenResponse},
        "500": {"model": InternalServerErrorResponse},
    },
)
async def send_message(user_id: str, payload:str):
    await ws_manager.send_personal_message(
        user_id=user_id,
        data=payload
    )
    return common_response(NoContent())
@router.get(
    "/send_role",
    responses={
        "400": {"model": BadRequestResponse},
        "401": {"model": UnauthorizedResponse},
        "403": {"model": ForbiddenResponse},
        "500": {"model": InternalServerErrorResponse},
    },
)
async def send_message(role_id: int, payload:Optional[str]):
    print('tipe payload',type(payload))
    await ws_manager.send_to_role(
        data=payload,
        role_id=role_id, 
    )
    return common_response(NoContent())
@router.get(
    "/send_role/test",
    responses={
        "400": {"model": BadRequestResponse},
        "401": {"model": UnauthorizedResponse},
        "403": {"model": ForbiddenResponse},
        "500": {"model": InternalServerErrorResponse},
    },
)
async def send_message(role_id: int):
    # dummy data
    dict_notif = {
            "id": 1,
            "aktivitas": {
                "aktivitas_selanjutnya": "https://google.com",
            },
            "isi_notifikasi": "Notif baru nih",
            "extra": {
                "code_ticket": "TA129038921789",
                "kode_spbu": "1233233",
            },
            "read_at": datetime.now().astimezone(timezone(TZ)).strftime(
                "%Y-%m-%d %H:%M:%S"
            ),
            "created_at": datetime.now().astimezone(timezone(TZ)).strftime(
                "%Y-%m-%d %H:%M:%S"
            ),
            "title": "Ticketing",
            "message": "Notif baru nih",
        }
    print('tipe payload',type(dict_notif))
    await ws_manager.send_to_role_test(
        data=json.dumps(dict_notif),
        role_id=role_id, 
    )
    return common_response(NoContent())
