from typing import List, Dict, Optional, Any
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import date, datetime
import logging
import re
from models.VGlaAll import VGlaAll
from datetime import datetime, date


logger = logging.getLogger(__name__)


class PredictiveAnalyticsRepository:
    
    @staticmethod
    async def get_latest_model_version(db: AsyncSession) -> Optional[str]:
        """
        Mendapatkan model version terbaru dari database
        """
        try:
            query = text("""
                SELECT DISTINCT model_version 
                FROM satgas_ai.predictive_analytics 
                ORDER BY model_version DESC 
                LIMIT 1
            """)
            
            result = await db.execute(query)
            latest_version = result.scalar()
            
            return latest_version
            
        except Exception as e:
            logger.error(f"Error getting latest model version: {e}")
            raise
    
    @staticmethod
    async def get_available_periods(db: AsyncSession, model_version: str) -> List[Dict[str, Any]]:
        """
        Mendapatkan opsi periode yang tersedia dari versi model tertentu
        """
        try:
            query = text("""
                SELECT DISTINCT 
                    EXTRACT(YEAR FROM period_date) as year,
                    EXTRACT(MONTH FROM period_date) as month,
                    period_date
                FROM satgas_ai.predictive_analytics 
                WHERE model_version = :model_version
                ORDER BY year DESC, month DESC
            """)
            
            result = await db.execute(query, {"model_version": model_version})
            data = result.fetchall()
            
            periods = []
            for row in data:
                month_name = datetime(int(row.year), int(row.month), 1).strftime("%B")
                periods.append({
                    "year": int(row.year),
                    "month": int(row.month),
                    "month_name": month_name,
                    "period": f"{int(row.year)}-{int(row.month):02d}",
                    "period_date": (
                        row.period_date.isoformat()
                        if isinstance(row.period_date, (date, datetime)) else row.period_date
                    )
                })
            
            return periods
            
        except Exception as e:
            logger.error(f"Error getting available periods: {e}")
            raise
    
    @staticmethod
    async def get_current_month_predictions(
        db: AsyncSession, 
        current_month: int, 
        current_year: int, 
        model_version: str
    ) -> List[Dict[str, Any]]:
        """
        Mendapatkan prediksi untuk bulan ini
        """
        try:
            query = text("""
                SELECT 
                    gl_dss_prod_cat,
                    prediction_revenue,
                    percentage_difference
                FROM satgas_ai.predictive_analytics 
                WHERE EXTRACT(MONTH FROM period_date) = :month
                AND EXTRACT(YEAR FROM period_date) = :year
                AND model_version = :model_version
                ORDER BY prediction_revenue DESC
            """)
            
            result = await db.execute(query, {
                "month": current_month,
                "year": current_year,
                "model_version": model_version
            })
            
            data = result.fetchall()
            
            # Convert to list of dictionaries
            predictions = []
            for row in data:
                predictions.append({
                    "gl_dss_prod_cat": row.gl_dss_prod_cat,
                    "prediction_revenue": float(row.prediction_revenue),
                    "percentage_difference": round(float(row.percentage_difference) * 100, 2) if row.percentage_difference else 0
                })
            
            return predictions
            
        except Exception as e:
            logger.error(f"Error getting current month predictions: {e}")
            raise
    
    @staticmethod
    async def get_next_month_predictions(
        db: AsyncSession, 
        next_month: int, 
        next_year: int, 
        model_version: str
    ) -> List[Dict[str, Any]]:
        """
        Mendapatkan prediksi untuk bulan depan
        """
        try:
            query = text("""
                SELECT 
                    gl_dss_prod_cat,
                    prediction_revenue,
                    percentage_difference
                FROM satgas_ai.predictive_analytics 
                WHERE EXTRACT(MONTH FROM period_date) = :month
                AND EXTRACT(YEAR FROM period_date) = :year
                AND model_version = :model_version
                ORDER BY prediction_revenue DESC
            """)
            
            result = await db.execute(query, {
                "month": next_month,
                "year": next_year,
                "model_version": model_version
            })
            
            data = result.fetchall()
            
            # Convert to list of dictionaries
            predictions = []
            for row in data:
                predictions.append({
                    "gl_dss_prod_cat": row.gl_dss_prod_cat,
                    "prediction_revenue": float(row.prediction_revenue),
                    "percentage_difference": round(float(row.percentage_difference) * 100, 2) if row.percentage_difference else 0
                })
            
            return predictions
            
        except Exception as e:
            logger.error(f"Error getting next month predictions: {e}")
            raise
    
    @staticmethod
    async def get_monthly_predictions(
        db: AsyncSession, 
        year: int, 
        month: int, 
        model_version: str
    ) -> List[Dict[str, Any]]:
        """
        Mendapatkan prediksi untuk bulan dan tahun tertentu
        """
        try:
            query = text("""
                SELECT 
                    gl_dss_prod_cat,
                    prediction_revenue,
                    percentage_difference,
                    prediction_direction,
                    absolute_difference,
                    period_date
                FROM satgas_ai.predictive_analytics 
                WHERE EXTRACT(MONTH FROM period_date) = :month
                AND EXTRACT(YEAR FROM period_date) = :year
                AND model_version = :model_version
                ORDER BY prediction_revenue DESC
            """)
            
            result = await db.execute(query, {
                "month": month,
                "year": year,
                "model_version": model_version
            })
            
            data = result.fetchall()
            
            # Convert to list of dictionaries
            predictions = []
            for row in data:
                predictions.append({
                    "gl_dss_prod_cat": row.gl_dss_prod_cat,
                    "prediction_revenue": float(row.prediction_revenue),
                    "percentage_difference": round(float(row.percentage_difference) * 100, 2) if row.percentage_difference else 0,
                    "prediction_direction": row.prediction_direction,
                    "absolute_difference": float(row.absolute_difference) if row.absolute_difference else 0,
                    "period_date": (
                        row.period_date.isoformat()
                        if isinstance(row.period_date, (date, datetime)) else row.period_date
                    )
                })
            
            return predictions
            
        except Exception as e:
            logger.error(f"Error getting monthly predictions: {e}")
            raise
    
    @staticmethod
    async def get_all_next_month_predictions(
        db: AsyncSession, 
        model_version: str,
        period: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Mendapatkan semua prediksi untuk periode tertentu dari semua gl_dss_prod_cat
        Jika period tidak diisi, akan menggunakan bulan depan sebagai default
        """
        try:
            if period:
                # Parse period dari format "YYYY-MM"
                try:
                    year, month = period.split("-")
                    target_year = int(year)
                    target_month = int(month)
                except ValueError:
                    raise ValueError("Invalid period format. Use YYYY-MM format")
            else:
                # Tentukan bulan depan sebagai default
                current_date = datetime.now()
                current_month = current_date.month
                current_year = current_date.year
                
                if current_month == 12:
                    target_month = 1
                    target_year = current_year + 1
                else:
                    target_month = current_month + 1
                    target_year = current_year
            
            query = text("""
                SELECT 
                    gl_dss_prod_cat,
                    prediction_revenue,
                    percentage_difference,
                    prediction_direction,
                    absolute_difference,
                    period_date,
                    previous_revenue,
                    model_version
                FROM satgas_ai.predictive_analytics 
                WHERE EXTRACT(MONTH FROM period_date) = :month
                AND EXTRACT(YEAR FROM period_date) = :year
                AND model_version = :model_version
                ORDER BY prediction_revenue DESC
            """)
            
            result = await db.execute(query, {
                "month": target_month,
                "year": target_year,
                "model_version": model_version
            })
            
            data = result.fetchall()
            
            # Convert to list of dictionaries
            predictions = []
            for row in data:
                predictions.append({
                    "gl_dss_prod_cat": row.gl_dss_prod_cat,
                    "prediction_revenue": float(row.prediction_revenue),
                    "percentage_difference": round(float(row.percentage_difference) * 100, 2) if row.percentage_difference else 0,
                    "prediction_direction": row.prediction_direction,
                    "absolute_difference": float(row.absolute_difference) if row.absolute_difference else 0,
                    "period_date": (
                        row.period_date.isoformat()
                        if isinstance(row.period_date, (date, datetime)) else row.period_date
                    ),
                    "previous_revenue": float(row.previous_revenue) if row.previous_revenue else 0,
                    "model_version": row.model_version
                })

            
            return predictions
            
        except Exception as e:
            logger.error(f"Error getting all next month predictions: {e}")
            raise
    
    @staticmethod
    async def get_prediction_summary(
        db: AsyncSession, 
        model_version: str,
        period: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Mendapatkan summary dari 5 produk dengan real_revenue tertinggi bulan kemarin
        dan prediction_revenue bulan ini dari tabel predictive_analytics
        """
        try:

            current_date = datetime.now()
            current_month = current_date.month
            current_year = current_date.year
            
            if current_month == 1:
                last_month = 12
                last_year = current_year - 1
            else:
                last_month = current_month - 1
                last_year = current_year
            
            top_products_query = text("""
                WITH product_revenue AS (
                    SELECT 
                        CASE 
                            WHEN gl_dss_prod_cat IN ('Voice-Voice POTS-Lapeks', 'Voice-Voice POTS-Non', 'Value Added IMS App Service-Voice POTS-Lapeks') THEN 'VOICE-POTS'
                            WHEN gl_dss_prod_cat IN ('Voice-SIP Trunk-Lapeks', 'Voice-SIP Trunk-Non') THEN 'SIP TRUNK'
                            WHEN gl_dss_prod_cat IN ('Call Center-Call Center-Lapeks', 'Call Center-Call Center-Non') THEN 'CALL CENTER'
                            WHEN gl_dss_prod_cat IN ('Interconnection-Interconnection-Lapeks', 'Interconnection-Interconnection-Non') THEN 'INTERCONNECTION'
                            WHEN gl_dss_prod_cat IN ('SMS A2P-SMS A2P-Lapeks') THEN 'SMS A2P'
                            WHEN gl_dss_prod_cat IN ('WIFI VAS & ADD ON OTHERS CONNECTIVITY', 'DIGITAL ADVERTISING', 'WIFI DIGITAL ADVERTISING') THEN 'WIFI VAS & ADD ON OTHERS'
                            WHEN gl_dss_prod_cat IN ('WHOLESALE PORT', 'INTERNATIONAL ROAMING') THEN 'WIFI WHOLESALE ISP'
                            WHEN gl_dss_prod_cat = 'HIGH SPEED INTERNET' THEN 'HSI B2C'
                            WHEN gl_dss_prod_cat = 'HSI B2B' THEN 'HSI B2B'
                            WHEN gl_dss_prod_cat = 'ASTINET' THEN 'ASTINET'
                            WHEN gl_dss_prod_cat = 'IP TRANSIT' THEN 'IP TRANSIT'
                            WHEN gl_dss_prod_cat = 'METRO ETHERNET' THEN 'METRO ETHERNET'
                            WHEN gl_dss_prod_cat = 'SL DOMESTIK' THEN 'SL DOMESTIK'
                            WHEN gl_dss_prod_cat = 'GLOBAL LINK' THEN 'GLOBAL LINK'
                            WHEN gl_dss_prod_cat = 'VPN IP' THEN 'VPN IP'
                            WHEN gl_dss_prod_cat = 'MANAGED SDWAN SERVICE' THEN 'MANAGED SDWAN SERVICE'
                            WHEN gl_dss_prod_cat = 'COLLOCATION DC' THEN 'COLLOCATION DC'
                            WHEN gl_dss_prod_cat = 'WIFI MANAGED SERVICE' THEN 'WIFI MANAGED SERVICE'
                            WHEN gl_dss_prod_cat = 'WIFI VOUCHER B2B2C' THEN 'WIFI VOUCHER B2B2C'
                            ELSE NULL
                        END as mapped_product_category,
                        SUM(real_revenue) as total_real_revenue
                    FROM satgas_ai.v_gla_all 
                    WHERE EXTRACT(MONTH FROM periode_rev) = :last_month
                    AND EXTRACT(YEAR FROM periode_rev) = :last_year
                    AND real_revenue IS NOT NULL
                    GROUP BY 
                        CASE 
                            WHEN gl_dss_prod_cat IN ('Voice-Voice POTS-Lapeks', 'Voice-Voice POTS-Non', 'Value Added IMS App Service-Voice POTS-Lapeks') THEN 'VOICE-POTS'
                            WHEN gl_dss_prod_cat IN ('Voice-SIP Trunk-Lapeks', 'Voice-SIP Trunk-Non') THEN 'SIP TRUNK'
                            WHEN gl_dss_prod_cat IN ('Call Center-Call Center-Lapeks', 'Call Center-Call Center-Non') THEN 'CALL CENTER'
                            WHEN gl_dss_prod_cat IN ('Interconnection-Interconnection-Lapeks', 'Interconnection-Interconnection-Non') THEN 'INTERCONNECTION'
                            WHEN gl_dss_prod_cat IN ('SMS A2P-SMS A2P-Lapeks') THEN 'SMS A2P'
                            WHEN gl_dss_prod_cat IN ('WIFI VAS & ADD ON OTHERS CONNECTIVITY', 'DIGITAL ADVERTISING', 'WIFI DIGITAL ADVERTISING') THEN 'WIFI VAS & ADD ON OTHERS'
                            WHEN gl_dss_prod_cat IN ('WHOLESALE PORT', 'INTERNATIONAL ROAMING') THEN 'WIFI WHOLESALE ISP'
                            WHEN gl_dss_prod_cat = 'HIGH SPEED INTERNET' THEN 'HSI B2C'
                            WHEN gl_dss_prod_cat = 'HSI B2B' THEN 'HSI B2B'
                            WHEN gl_dss_prod_cat = 'ASTINET' THEN 'ASTINET'
                            WHEN gl_dss_prod_cat = 'IP TRANSIT' THEN 'IP TRANSIT'
                            WHEN gl_dss_prod_cat = 'METRO ETHERNET' THEN 'METRO ETHERNET'
                            WHEN gl_dss_prod_cat = 'SL DOMESTIK' THEN 'SL DOMESTIK'
                            WHEN gl_dss_prod_cat = 'GLOBAL LINK' THEN 'GLOBAL LINK'
                            WHEN gl_dss_prod_cat = 'VPN IP' THEN 'VPN IP'
                            WHEN gl_dss_prod_cat = 'MANAGED SDWAN SERVICE' THEN 'MANAGED SDWAN SERVICE'
                            WHEN gl_dss_prod_cat = 'COLLOCATION DC' THEN 'COLLOCATION DC'
                            WHEN gl_dss_prod_cat = 'WIFI MANAGED SERVICE' THEN 'WIFI MANAGED SERVICE'
                            WHEN gl_dss_prod_cat = 'WIFI VOUCHER B2B2C' THEN 'WIFI VOUCHER B2B2C'
                            ELSE NULL
                        END
                    HAVING 
                        CASE 
                            WHEN gl_dss_prod_cat IN ('Voice-Voice POTS-Lapeks', 'Voice-Voice POTS-Non', 'Value Added IMS App Service-Voice POTS-Lapeks') THEN 'VOICE-POTS'
                            WHEN gl_dss_prod_cat IN ('Voice-SIP Trunk-Lapeks', 'Voice-SIP Trunk-Non') THEN 'SIP TRUNK'
                            WHEN gl_dss_prod_cat IN ('Call Center-Call Center-Lapeks', 'Call Center-Call Center-Non') THEN 'CALL CENTER'
                            WHEN gl_dss_prod_cat IN ('Interconnection-Interconnection-Lapeks', 'Interconnection-Interconnection-Non') THEN 'INTERCONNECTION'
                            WHEN gl_dss_prod_cat IN ('SMS A2P-SMS A2P-Lapeks') THEN 'SMS A2P'
                            WHEN gl_dss_prod_cat IN ('WIFI VAS & ADD ON OTHERS CONNECTIVITY', 'DIGITAL ADVERTISING', 'WIFI DIGITAL ADVERTISING') THEN 'WIFI VAS & ADD ON OTHERS'
                            WHEN gl_dss_prod_cat IN ('WHOLESALE PORT', 'INTERNATIONAL ROAMING') THEN 'WIFI WHOLESALE ISP'
                            WHEN gl_dss_prod_cat = 'HIGH SPEED INTERNET' THEN 'HSI B2C'
                            WHEN gl_dss_prod_cat = 'HSI B2B' THEN 'HSI B2B'
                            WHEN gl_dss_prod_cat = 'ASTINET' THEN 'ASTINET'
                            WHEN gl_dss_prod_cat = 'IP TRANSIT' THEN 'IP TRANSIT'
                            WHEN gl_dss_prod_cat = 'METRO ETHERNET' THEN 'METRO ETHERNET'
                            WHEN gl_dss_prod_cat = 'SL DOMESTIK' THEN 'SL DOMESTIK'
                            WHEN gl_dss_prod_cat = 'GLOBAL LINK' THEN 'GLOBAL LINK'
                            WHEN gl_dss_prod_cat = 'VPN IP' THEN 'VPN IP'
                            WHEN gl_dss_prod_cat = 'MANAGED SDWAN SERVICE' THEN 'MANAGED SDWAN SERVICE'
                            WHEN gl_dss_prod_cat = 'COLLOCATION DC' THEN 'COLLOCATION DC'
                            WHEN gl_dss_prod_cat = 'WIFI MANAGED SERVICE' THEN 'WIFI MANAGED SERVICE'
                            WHEN gl_dss_prod_cat = 'WIFI VOUCHER B2B2C' THEN 'WIFI VOUCHER B2B2C'
                            ELSE NULL
                        END IS NOT NULL
                    ORDER BY total_real_revenue DESC
                    LIMIT 5
                )
                SELECT 
                    mapped_product_category as gl_dss_prod_cat,
                    total_real_revenue
                FROM product_revenue
            """)
            
            result = await db.execute(top_products_query, {
                "last_month": last_month,
                "last_year": last_year
            })
            
            top_products = result.fetchall()
            
            if not top_products:
                return {
                    "message": "Tidak ada data real_revenue bulan kemarin",
                    "data": [],
                    "summary": {
                        "total_real_revenue_last_month": 0,
                        "total_prediction_revenue_current_month": 0,
                        "average_percentage_change": 0
                    }
                }
            
            
            normalized_categories = []
            for product in top_products:
                original_category = product.gl_dss_prod_cat
                normalized_category = original_category.replace(" ", "_")
                normalized_categories.append(normalized_category)
            
            predictions_query = text("""
                SELECT 
                    gl_dss_prod_cat,
                    prediction_revenue,
                    percentage_difference,
                    prediction_direction
                FROM satgas_ai.predictive_analytics 
                WHERE gl_dss_prod_cat = ANY(:product_categories)
                AND EXTRACT(MONTH FROM period_date) = :current_month
                AND EXTRACT(YEAR FROM period_date) = :current_year
                AND model_version = :model_version
            """)
            
            result = await db.execute(predictions_query, {
                "product_categories": normalized_categories,
                "current_month": current_month,
                "current_year": current_year,
                "model_version": model_version
            })
            
            predictions = result.fetchall()
            
            combined_data = []
            total_real_revenue_last_month = 0
            total_prediction_revenue_current_month = 0
            percentage_changes = []
            
            for i, product in enumerate(top_products):
                original_category = product.gl_dss_prod_cat
                normalized_category = normalized_categories[i]
                
                product_data = {
                    "gl_dss_prod_cat_original": original_category,
                    "gl_dss_prod_cat_normalized": normalized_category,
                    "real_revenue_last_month": float(product.total_real_revenue),
                    "prediction_revenue_current_month": 0,
                    "percentage_difference": None,
                    "prediction_direction": None,
                    "percentage_change": 0
                }
                
                for pred in predictions:
                    if pred.gl_dss_prod_cat == normalized_category:
                        product_data["prediction_revenue_current_month"] = float(pred.prediction_revenue)
                        product_data["percentage_difference"] = None
                        product_data["prediction_direction"] = pred.prediction_direction
                        
                        if product.total_real_revenue > 0:
                            percentage_change = ((pred.prediction_revenue - product.total_real_revenue) / product.total_real_revenue) * 100
                            product_data["percentage_change"] = round(percentage_change, 2)
                            percentage_changes.append(percentage_change)
                        else:
                            product_data["percentage_change"] = None
                        break
                
                combined_data.append(product_data)
                total_real_revenue_last_month += product_data["real_revenue_last_month"]
                total_prediction_revenue_current_month += product_data["prediction_revenue_current_month"]
            
            valid_percentage_changes = [pc for pc in percentage_changes if pc is not None]
            avg_percentage_change = sum(valid_percentage_changes) / len(valid_percentage_changes) if valid_percentage_changes else 0
            
            return {
                "data": combined_data,
                "summary": {
                    "total_real_revenue_last_month": round(total_real_revenue_last_month, 2),
                    "total_prediction_revenue_current_month": round(total_prediction_revenue_current_month, 2),
                    "average_percentage_change": round(avg_percentage_change, 2),
                    "last_month": f"{last_year}-{last_month:02d}",
                    "current_month": f"{current_year}-{current_month:02d}"
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting prediction summary: {e}")
            raise

    @staticmethod
    async def get_all_next_month_predictions_with_fallback(
        db: AsyncSession, 
        target_period: str,
        preferred_model_version: str = None
    ) -> Dict[str, Any]:
        try:
            # Parse period dari format "YYYY-MM"
            try:
                year, month = target_period.split("-")
                target_year = int(year)
                target_month = int(month)
            except ValueError:
                raise ValueError("Invalid period format. Use YYYY-MM format")
            
            # Jika tidak ada preferred_model_version, gunakan yang terbaru
            if not preferred_model_version:
                preferred_model_version = await PredictiveAnalyticsRepository.get_latest_model_version(db)
                if not preferred_model_version:
                    raise ValueError("No model version available")
            
            # Coba dapatkan data dari model version yang diinginkan
            query = text("""
                SELECT 
                    gl_dss_prod_cat,
                    prediction_revenue,
                    percentage_difference,
                    prediction_direction,
                    absolute_difference,
                    period_date,
                    previous_revenue,
                    model_version
                FROM satgas_ai.predictive_analytics 
                WHERE EXTRACT(MONTH FROM period_date) = :month
                AND EXTRACT(YEAR FROM period_date) = :year
                AND model_version = :model_version
                ORDER BY prediction_revenue DESC
            """)
            
            result = await db.execute(query, {
                "month": target_month,
                "year": target_year,
                "model_version": preferred_model_version
            })
            
            data = result.fetchall()
            
            # Jika data ditemukan, return dengan model version yang diminta
            if data:
                predictions = []
                for row in data:
                    predictions.append({
                        "gl_dss_prod_cat": row.gl_dss_prod_cat,
                        "prediction_revenue": float(row.prediction_revenue),
                        "percentage_difference": round(float(row.percentage_difference) * 100, 2) if row.percentage_difference else 0,
                        "prediction_direction": row.prediction_direction,
                        "absolute_difference": float(row.absolute_difference) if row.absolute_difference else 0,
                        "period_date": (
                            row.period_date.isoformat()
                            if isinstance(row.period_date, (date, datetime)) else row.period_date
                        ),
                        "previous_revenue": float(row.previous_revenue) if row.previous_revenue else 0,
                        "model_version": row.model_version
                    })
                
                return {
                    "predictions": predictions,
                    "model_version": preferred_model_version,
                    "fallback_used": False
                }
            
            # Jika data tidak ditemukan, cari di model version sebelumnya
            logger.info(f"No data found for period {target_period} in model version {preferred_model_version}, searching in previous versions...")
            
            # Dapatkan semua model version yang tersedia, urutkan dari yang terbaru
            version_query = text("""
                SELECT DISTINCT model_version 
                FROM satgas_ai.predictive_analytics 
                ORDER BY model_version DESC
            """)
            
            version_result = await db.execute(version_query)
            available_versions = [row.model_version for row in version_result.fetchall()]
            
            # Cari di model version sebelumnya
            for version in available_versions:
                if version == preferred_model_version:
                    continue  # Skip yang sudah dicoba
                
                logger.info(f"Trying model version {version} for period {target_period}")
                
                result = await db.execute(query, {
                    "month": target_month,
                    "year": target_year,
                    "model_version": version
                })
                
                data = result.fetchall()
                
                if data:
                    predictions = []
                    for row in data:
                        predictions.append({
                            "gl_dss_prod_cat": row.gl_dss_prod_cat,
                            "prediction_revenue": float(row.prediction_revenue),
                            "percentage_difference": round(float(row.percentage_difference) * 100, 2) if row.percentage_difference else 0,
                            "prediction_direction": row.prediction_direction,
                            "absolute_difference": float(row.absolute_difference) if row.absolute_difference else 0,
                            "period_date": (
                                row.period_date.isoformat()
                                if isinstance(row.period_date, (date, datetime)) else row.period_date
                            ),
                            "previous_revenue": float(row.previous_revenue) if row.previous_revenue else 0,
                            "model_version": row.model_version
                        })
                    
                    logger.info(f"Found data in fallback model version {version}")
                    return {
                        "predictions": predictions,
                        "model_version": version,
                        "fallback_used": True,
                        "fallback_info": f"Data not available in latest model version {preferred_model_version}, using version {version}"
                    }
            
            # Jika tidak ada data sama sekali untuk periode tersebut
            return {
                "predictions": [],
                "model_version": None,
                "fallback_used": False,
                "error": f"No data available for period {target_period} in any model version"
            }
            
        except Exception as e:
            logger.error(f"Error getting predictions with fallback: {e}")
            raise

    @staticmethod
    async def get_anomaly_detection(db: AsyncSession) -> Dict[str, Any]:
        """
        Mendapatkan anomali terdeteksi berdasarkan perubahan terbesar dari notifikasi bulan terbaru
        Fallback ke SummaryInsight jika tabel notifications tidak tersedia
        
        Returns:
        - anomaly_data: Data anomali yang terdeteksi
        - period: Periode bulan terbaru
        - total_anomalies: Jumlah anomali yang terdeteksi
        """
        try:
            # Cek apakah tabel notifications ada
            table_check_query = text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'notifications'
                )
            """)
            
            table_exists_result = await db.execute(table_check_query)
            table_exists = table_exists_result.scalar()
            
            if not table_exists:
                logger.warning("Table 'notifications' does not exist, checking for alternative schemas...")
                
                # Cek schema lain yang mungkin
                schema_check_query = text("""
                    SELECT DISTINCT table_schema 
                    FROM information_schema.tables 
                    WHERE table_name = 'notifications'
                """)
                
                schema_result = await db.execute(schema_check_query)
                available_schemas = [row.table_schema for row in schema_result.fetchall()]
                
                if not available_schemas:
                    logger.info("Notifications table not found, using SummaryInsight as fallback")
                    return await PredictiveAnalyticsRepository._get_anomaly_from_summary_insight(db)
                
                # Gunakan schema pertama yang ditemukan
                schema_name = available_schemas[0]
                logger.info(f"Found notifications table in schema: {schema_name}")
            else:
                schema_name = "public"
            
            # Query untuk mendapatkan notifikasi bulan terbaru dengan perubahan terbesar
            query = text(f"""
                SELECT DISTINCT ON (n.title)
                    n.title,
                    n.message,
                    n.insight,
                    n.created_at,
                    n.updated_at,
                    EXTRACT(YEAR FROM n.created_at) as year,
                    EXTRACT(MONTH FROM n.created_at) as month
                FROM {schema_name}.notifications n
                WHERE n.is_active = true
                AND n.title LIKE '%Revenue%'
                ORDER BY n.title, n.created_at DESC
                LIMIT 10
            """)
            
            result = await db.execute(query)
            notifications = result.fetchall()
            
            if not notifications:
                logger.info("No notifications found, using SummaryInsight as fallback")
                return await PredictiveAnalyticsRepository._get_anomaly_from_summary_insight(db)
            
            # Ekstrak informasi anomali dari notifikasi
            anomalies = []
            for notif in notifications:
                title = notif.title
                message = notif.message
                
                # 1) Jenis perubahan (penurunan/peningkatan)
                if re.search(r'penurunan', title, flags=re.IGNORECASE):
                    change_type = "decrease"
                    change_direction = "down"
                elif re.search(r'peningkatan', title, flags=re.IGNORECASE):
                    change_type = "increase"
                    change_direction = "up"
                else:
                    change_type = "unknown"
                    change_direction = "stable"
                
                # 2) Persentase perubahan
                percentage_match = re.search(r'sebesar\s+(\d+(?:[\.,]\d+)?)%', title, flags=re.IGNORECASE)
                percentage = float(percentage_match.group(1).replace(',', '.')) if percentage_match else 0
                

                service_type = "Unknown"
                if "pada service" in title.lower():
                    service_match = re.search(r'pada\s+service\s+([^(]+?)(?:\s*\([^)]+\)|\s+pada\s+bulan)', title, flags=re.IGNORECASE)
                    if service_match:
                        service_type = service_match.group(1).strip()
                    else:
                        service_match = re.search(r'pada\s+service\s+([^(]+?)(?=\s+pada\s+bulan|\s*$)', title, flags=re.IGNORECASE)
                        if service_match:
                            service_type = service_match.group(1).strip()
                elif "pada produk" in title.lower():
                    service_match = re.search(r'pada\s+produk\s+([^(]+?)(?:\s*\([^)]+\)|\s+pada\s+bulan)', title, flags=re.IGNORECASE)
                    if service_match:
                        service_type = service_match.group(1).strip()
                
                # 4) Product type (dalam kurung)
                product_match = re.search(r'\(([^)]+)\)', title)
                product_type = product_match.group(1) if product_match else None
                
                # 5) Periode (bulan tahun)
                period_match = re.search(r'pada\s+bulan\s+(\w+\s+\d{4})', title, flags=re.IGNORECASE)
                period = period_match.group(1) if period_match else "Unknown Period"
                
                # 6) Total perubahan revenue (nominal)
                amount_match = re.search(r'Total\s+perubahan\s+revenue\s+adalah\s+Rp\s*([\d\.,]+)', title, flags=re.IGNORECASE)
                total_amount = amount_match.group(1) if amount_match else None
                
                # Tentukan severity berdasarkan persentase
                if abs(percentage) >= 50:
                    severity = "high"
                elif abs(percentage) >= 25:
                    severity = "medium"
                else:
                    severity = "low"
                
                anomaly_info = {
                    "title": title,
                    "service_type": service_type,
                    "product_type": product_type,
                    "percentage_change": percentage,
                    "change_type": change_type,
                    "change_direction": change_direction,
                    "severity": severity,
                    "period": period,
                    "total_amount": total_amount,
                    "created_at": notif.created_at.isoformat() if notif.created_at else None,
                    "updated_at": notif.updated_at.isoformat() if notif.updated_at else None
                }
                
                anomalies.append(anomaly_info)
            
            # Urutkan berdasarkan persentase perubahan (terbesar ke terkecil)
            anomalies.sort(key=lambda x: abs(x["percentage_change"]), reverse=True)
            
            # Ambil anomali terbesar untuk summary
            top_anomaly = anomalies[0] if anomalies else None
            
            # Tentukan periode bulan terbaru
            if anomalies:
                latest_month = max(anomalies, key=lambda x: x["created_at"] if x["created_at"] else "")
                period = latest_month["period"]
            else:
                period = None
            
            return {
                "anomaly_data": anomalies,
                "period": period,
                "summary": top_anomaly
            }
            
        except Exception as e:
            logger.error(f"Error getting anomaly detection: {e}")
            raise


    @staticmethod
    async def _get_anomaly_from_summary_insight(db: AsyncSession) -> Dict[str, Any]:
        """
        Fallback method untuk mendapatkan anomali dari tabel SummaryInsight
        """
        try:
            # Query untuk mendapatkan data dari SummaryInsight
            query = text("""
                SELECT 
                    summary_text,
                    insight,
                    product_type,
                    service_type,
                    month,
                    year,
                    created_at
                FROM dcsbe.summary_insight 
                WHERE isdel = false
                AND summary_text LIKE '%revenue%'
                AND summary_text LIKE '%sebesar%'
                ORDER BY created_at DESC
                LIMIT 10
            """)
            
            result = await db.execute(query)
            summary_insights = result.fetchall()
            
            if not summary_insights:
                return {
                    "anomaly_data": [],
                    "period": None,
                    "message": "Tidak ada data anomali yang ditemukan di SummaryInsight"
                }
            
            # Ekstrak informasi anomali dari SummaryInsight
            anomalies = []
            for insight in summary_insights:
                summary_text = insight.summary_text
                
                # 1) Jenis perubahan
                if re.search(r"penurunan", summary_text, flags=re.IGNORECASE):
                    change_type = "decrease"
                    change_direction = "down"
                elif re.search(r"peningkatan", summary_text, flags=re.IGNORECASE):
                    change_type = "increase"
                    change_direction = "up"
                else:
                    change_type = "unknown"
                    change_direction = "stable"
                
                # 2) Persentase perubahan (mendukung koma/desimal)
                perc_match = re.search(r"(?:sebesar\s*)?(\d+(?:[\.,]\d+)?)%", summary_text, flags=re.IGNORECASE)
                percentage = float(perc_match.group(1).replace(',', '.')) if perc_match else 0
                
                # 3) Service type - gunakan regex yang sama seperti di get_data_from_tabel
                service_type = "Unknown"
                service_match = re.search(r'Layanan\s+([^(]+?)(?:\s+\([^)]+\)|\s+mengalami|\s+dengan)', summary_text, flags=re.IGNORECASE)
                if service_match:
                    service_type = service_match.group(1).strip()
                else:
                    service_match = re.search(r'Layanan\s+([^(]+?)(?=\s+pada\s+bulan|\s*$)', summary_text, flags=re.IGNORECASE)
                    if service_match:
                        service_type = service_match.group(1).strip()
                
                # 4) Product type (dalam kurung)
                product_match = re.search(r'\(([^)]+)\)', summary_text)
                product_type = product_match.group(1) if product_match else None
                
                # 5) Bulan dan tahun (Indonesia/English)
                month_regex = r"(Januari|January|Februari|February|Maret|March|April|Mei|May|Juni|June|Juli|July|Agustus|August|September|Oktober|October|November|Desember|December)\s+(\d{4})"
                month_year_match = re.search(month_regex, summary_text, flags=re.IGNORECASE)
                if month_year_match:
                    month_name = month_year_match.group(1)
                    year_val = month_year_match.group(2)
                    month_map = {
                        'januari':'January','februari':'February','maret':'March','april':'April','mei':'May','juni':'June','juli':'July','agustus':'August','september':'September','oktober':'October','november':'November','desember':'December'
                    }
                    month_lower = month_name.lower()
                    month_name = month_map.get(month_lower, month_name.capitalize())
                else:
                    try:
                        month_num = int(insight.month)
                        month_name = datetime(1900, month_num, 1).strftime("%B") if month_num else "Unknown"
                    except (ValueError, TypeError):
                        month_name = "Unknown"
                    year_val = insight.year
                
                period = f"{month_name} {year_val}"
                
                # 6) Nominal total perubahan revenue
                amount_match = re.search(r"Total\s+perubahan\s+revenue\s+adalah\s+Rp\s*([\d\.,]+)", summary_text, flags=re.IGNORECASE)
                total_amount = amount_match.group(1) if amount_match else None
                
                # Tentukan severity berdasarkan persentase
                if abs(percentage) >= 50:
                    severity = "high"
                elif abs(percentage) >= 25:
                    severity = "medium"
                else:
                    severity = "low"
                
                # Buat title yang konsisten dengan format notifikasi
                title = f"Terjadi {'Peningkatan' if change_type == 'increase' else 'Penurunan'} Revenue {service_type}"
                if product_type:
                    title += f" ({product_type})"
                title += f" sebesar {percentage}% - {period}"
                
                anomaly_info = {
                    "title": title,
                    "service_type": service_type,
                    "product_type": product_type,
                    "percentage_change": percentage,
                    "change_type": change_type,
                    "change_direction": change_direction,
                    "severity": severity,
                    "period": period,
                    "total_amount": total_amount,
                    "message": summary_text,
                    "created_at": insight.created_at.isoformat() if insight.created_at else None,
                    "updated_at": None
                }
                
                anomalies.append(anomaly_info)
            
            # Urutkan berdasarkan persentase perubahan (terbesar ke terkecil)
            anomalies.sort(key=lambda x: abs(x["percentage_change"]), reverse=True)
            
            # Ambil anomali terbesar untuk summary
            top_anomaly = anomalies[0] if anomalies else None
            
            # Tentukan periode bulan terbaru
            if anomalies:
                period = anomalies[0]["period"]
            else:
                period = None
            
            return {
                "anomaly_data": anomalies,
                "period": period,
                "summary": {
                    "top_anomaly": top_anomaly
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting anomaly from SummaryInsight: {e}")
            raise
