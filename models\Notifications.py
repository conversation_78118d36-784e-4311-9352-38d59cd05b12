from sqlalchemy import Integer, Column, String, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateTime
from sqlalchemy.orm import relationship
from models import Base
from models.RolePermission import RolePermission
from models.Module import Module
from models.Role import Role


class Notifications(Base):
    __tablename__ = "notifications"

    id = Column("id", Integer, primary_key=True, nullable=False, index=True)
    title = Column("title", String, nullable=False)
    user_id = Column("user_id", String(36), Foreign<PERSON>ey("user.id"), nullable=True, index=True)
    insight = Column("insight", String, nullable=False)
    message = Column("message", String, nullable=False)
    is_read = Column("is_read", Boolean, default=False)
    is_active = Column("is_active", Boolean, default=True)
    created_at = Column("created_at", DateTime(timezone=True))
    updated_at = Column("updated_at", DateTime(timezone=True))