import os
from core.utils import str_to_bool

if os.environ.get("ENVIRONTMENT") != "prod":
    from dotenv import load_dotenv

    load_dotenv()

# Environtment
ENVIRONTMENT = os.environ.get("ENVIRONTMENT", "development")

# Database
DB_USER = os.environ.get("DB_USER", "rahasia")
DB_PASS = os.environ.get("DB_PASS", "rahasia")
DB_HOST = os.environ.get("DB_HOST", "rahasia")
DB_PORT = int(os.environ.get("DB_PORT", 5432))
DB_NAME = os.environ.get("DB_NAME", "rahasia")

# CORS
CORS_ALLOWED_ORIGINS = os.environ.get("CORS_ALLOWED_ORIGINS", "").split(",")

# APACHE KAFKA
KAFKA_SERVER = os.environ.get("KAFKA_SERVER")
KAFKA_TOPIC = os.environ.get("KAFKA_TOPIC")
RESPONSES_TOPIC = os.environ.get("RESPONSES_TOPIC")

# Supabase
SUPABASE_URL = os.environ.get("SUPABASE_URL")
SUPABASE_KEY = os.environ.get("SUPABASE_KEY")
SUPABASE_KEY_SERVICE = os.environ.get("SUPABASE_KEY_SERVICE")

# JWT conf
JWT_PREFIX = os.environ.get("JWT_PREFIX", "Bearer")
SECRET_KEY = os.environ.get("SECRET_KEY", "rahasia")
SECRET_KEY_CHAINLIT = os.environ.get("SECRET_KEY_CHAINLIT", "rahasia")
ALGORITHM = os.environ.get("ALGORITHM", "rahasia")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.environ.get("ACCESS_TOKEN_EXPIRE_MINUTES", 45))

# Timezone
TZ = os.environ.get("TZ", "Asia/Jakarta")

# Email
MAIL_USERNAME = os.environ.get("MAIL_USERNAME", "")
MAIL_PASSWORD = os.environ.get("MAIL_PASSWORD", "")
MAIL_FROM = os.environ.get("MAIL_FROM", "<EMAIL>")
MAIL_PORT = int(os.environ.get("MAIL_PORT", "465"))
MAIL_SERVER = os.environ.get("MAIL_SERVER", "")
MAIL_FROM_NAME = os.environ.get("MAIL_FROM_NAME", "<EMAIL>")
MAIL_TLS = str_to_bool(os.environ.get("MAIL_TLS", "False"))
MAIL_SSL = str_to_bool(os.environ.get("MAIL_SSL", "True"))
USE_CREDENTIALS = str_to_bool(os.environ.get("USE_CREDENTIALS", "True"))

# Frontend Domain
FE_DOMAIN = os.environ.get("FE_DOMAIN", "")

# backend url
BACKEND_URL = os.environ.get("BACKEND_URL", "")

# local path
LOCAL_PATH = os.environ.get("LOCAL_PATH", "./uploads")

# Minio
MINIO_ENDPOINT = os.environ.get("MINIO_ENDPOINT", "")
MINIO_ACCESS_KEY = os.environ.get("MINIO_ACCESS_KEY", "")
MINIO_SECRET_KEY = os.environ.get("MINIO_SECRET_KEY", "")
MINIO_SECURE = str_to_bool(os.environ.get("MINIO_SECURE", "False"))
MINIO_BUCKET = os.environ.get("MINIO_BUCKET", "ticketing")

# Sentry
SENTRY_DSN = os.environ.get("SENTRY_DSN", None)
SENTRY_TRACES_SAMPLE_RATES = float(os.environ.get("SENTRY_TRACES_SAMPLE_RATES", 1.0))

# Redis
REDIS_HOST = os.environ.get("REDIS_HOST", None)
REDIS_PORT = int(os.environ.get("REDIS_PORT", "0"))
REDIS_DB = int(os.environ.get("REDIS_DB", "0"))

BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# File Storage adapter
FILE_STORAGE_ADAPTER = os.environ.get("FILE_STORAGE_ADAPTER", "minio")
if not FILE_STORAGE_ADAPTER in ["local", "minio", "obs"]:
    raise Exception(
        "Invalid FILE_STORAGE_ADAPTER, FILE_STORAGE_ADAPTER should local or minio"
    )

UPLOAD_DIR = os.path.join(BASE_DIR, "uploads")
if not os.path.exists(UPLOAD_DIR):
    os.makedirs(UPLOAD_DIR)
ENCRYPTION_KEY = os.environ.get("ENCRYPTION_KEY", "uhuhuhuhu")
DEFAULT_SCHEMA = os.environ.get("DEFAULT_SCHEMA", None)
DEFAULT_ROLE_PASSWORD = os.environ.get("DEFAULT_ROLE_PASSWORD", None)
DEFAULT_DB_USERNAME = os.environ.get("DEFAULT_DB_USERNAME", None)

SUPER_ROLE_DB = os.environ.get("SUPER_ROLE_DB", None)
SUPPER_ROLE_PASSWORD = os.environ.get("SUPPER_ROLE_PASSWORD", None)
SUPER_DB_HOST = os.environ.get("SUPER_DB_HOST", None)
SUPER_DB_NAME = os.environ.get("SUPER_DB_NAME", None)
APPSNAME = os.environ.get("APPSNAME", None)
APPSTOKEN = os.environ.get("APPSTOKEN", None)
COOKIE = os.environ.get("COOKIE", None)
DEFAULT_SCHEMA = os.environ.get("DEFAULT_SCHEMA", "default")

# Third-party API Configuration
THIRD_PARTY_API_BASE_URL = os.environ.get(
    "THIRD_PARTY_API_BASE_URL", "https://ml-dev.apps.kpaasjt21.telkom.co.id"
)
THIRD_PARTY_API_USERNAME = os.environ.get("THIRD_PARTY_API_USERNAME", "")
THIRD_PARTY_API_PASSWORD = os.environ.get("THIRD_PARTY_API_PASSWORD", "")

TELKOM_AUTH_BASE_URL = os.environ.get("TELKOM_AUTH_BASE_URL", "")
TELKOM_AUTH_VALIDATE_URL = os.environ.get("TELKOM_AUTH_VALIDATE_URL", "")
TELKOM_AUTH_VALIDATE_MITRA_URL = os.environ.get("TELKOM_AUTH_VALIDATE_MITRA_URL", "")

# ICA Configuration
ICA_BASE_URL = os.environ.get("ICA_BASE_URL", "")
ICA_USERNAME = os.environ.get("ICA_USERNAME", "")
ICA_PASSWORD = os.environ.get("ICA_PASSWORD", "")
ICA_AUTH_TOKEN = os.environ.get("ICA_AUTH_TOKEN", "")
ICA_TOOL_ID = os.environ.get("ICA_TOOL_ID", "")
ICA_CHATBOT_ID = os.environ.get("ICA_CHATBOT_ID", "")

# RabbitMQ
RABBITMQ_URL = os.environ.get("RABBITMQ_URL", "")
RABBITMQ_QUEUE_NAME = os.environ.get("RABBITMQ_QUEUE_NAME", "")
SERVICE_NAME = os.environ.get("SERVICE_NAME", "")

# Vector DB
VECTOR_DB_URL = os.environ.get("VECTOR_DB_URL", "")
VECTOR_DB_API_KEY = os.environ.get("VECTOR_DB_API_KEY", "")
VECTOR_COLLECTION_NAME = os.environ.get("VECTOR_COLLECTION_NAME", "")

# Apilogy
APILOGY_KEY = os.environ.get("APILOGY_KEY", "")

# OBS Configuration
OBS_SERVER = os.environ.get("OBS_SERVER", "")
OBS_ACCESS_KEY = os.environ.get("OBS_ACCESS_KEY", "")
OBS_SECRET_KEY = os.environ.get("OBS_SECRET_KEY", "")
OBS_BUCKET = os.environ.get("OBS_BUCKET", "")
# OBS_REGION = os.environ.get("OBS_REGION", "")

# BigSocial API Configuration
BIGSOCIAL_API_URL = os.environ.get("BIGSOCIAL_API_URL", "https://stage-api-bigsocial.bigbox.ai/api/corcomm/media-online")
BIGSOCIAL_AUTH = os.environ.get("BIGSOCIAL_AUTH", "")

# REFRESH TOKEN
REFRESH_TOKEN_EXPIRE_MINUTES = int(os.environ.get("REFRESH_TOKEN_EXPIRE_MINUTES", 1440))
REFRESH_SECRET_KEY = os.environ.get("REFRESH_SECRET_KEY", "")