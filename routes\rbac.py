import traceback
from typing import List, Optional
from fastapi import APIRouter, Depends, Request, BackgroundTasks, UploadFile, File, Form, Request
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from core.responses import (
    common_response,
    Ok,
    CudResponse,
    BadRequest,
    Unauthorized,
    NotFound,
    InternalServerError,
)
from models import get_db
from core.security import check_user_permission, get_user_from_jwt_token, generate_jwt_token_from_user, get_user_permissions
from core.security import (
    get_user_from_jwt_token,
    oauth2_scheme,
)
from schemas.common import (
    BadRequestResponse,
    UnauthorizedResponse,
    NotFoundResponse,
    InternalServerErrorResponse,
    CudResponseSchema,
)

from schemas.rbac import (
    AssignRoleRequest,
    ListRoleResponse,
    ListUserRole,
    ListUserRoleResponse,
    ListUserWoRole,
    ListUserWoRoleResponse,
    RoleManagementSchema,
    UpdatePermissionRequest,
    UpdatePermissionResponse,
    UpdateMultiplePermissionRequest,
    UpdateMultiplePermissionResponse,
    AddRoleRequest,
    DeleteRoleRequest,
)
from schemas.auth import EditUserRequest, SignUpRequest

import repository.rbac as rbacRepo

router = APIRouter(tags=["Role Management"])
@router.get(
    "/role-management",
    response_model=RoleManagementSchema,  # Pastikan model responsnya benar
    responses={
        200: {"model": RoleManagementSchema},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def role_management(
    db: AsyncSession = Depends(get_db), 
    token: str = Depends(oauth2_scheme)

):
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        permission_user = check_user_permission(db, user, "view", "RBAC")
        if not permission_user:
            return common_response(Unauthorized())
        data = await rbacRepo.get_role_management(db)
        return common_response(
            Ok(data=data)
        )
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in role_management endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())

@router.get(
    "/list/user",
    response_model=ListUserWoRole,  # Pastikan model responsnya benar
    responses={
        200: {"model": ListUserWoRoleResponse},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def ls_user_wo_role(
    db: AsyncSession = Depends(get_db), 
    token: str = Depends(oauth2_scheme),
    page: int = 1,
    page_size: int = 10,
    src: Optional[str] = None,
):
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        permission_user = check_user_permission(db, user, "view", "RBAC")
        if not permission_user:
            return common_response(Unauthorized())
        data, count, page_count = await rbacRepo.get_users_without_roles(
            db,
            page=page,
            page_size=page_size,
            src=src
        )
        return common_response(
            Ok(
            data=data,
            meta={
                "count": count,
                "page_count": page_count,
                "page_size": page_size,
                "page": page,
            }
            )
        )
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in role_management endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())

@router.get(
    "/list/role-option",
    response_model=ListRoleResponse,  # Pastikan model responsnya benar
    responses={
        200: {"model": ListRoleResponse},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def ls_role_option(
    db: AsyncSession = Depends(get_db), 
    token: str = Depends(oauth2_scheme),
    src: Optional[str] = None,
):
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        # permission_user = check_user_permission(db, user, "view", "RBAC")
        # if not permission_user:
        #     return common_response(Unauthorized())
        data = await rbacRepo.get_role(
            db,
            src=src
        )
        return common_response(
            Ok(
            data=data,
            # meta={
            #     "count": count,
            #     "page_count": page_count,
            #     "page_size": page_size,
            #     "page": page,
            # }
            )
        )
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in role_management endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())

@router.get(
    "/list/user-role/{id_role}",
    response_model=ListUserRole,
    responses={
        200: {"model": ListUserRoleResponse},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def ls_user_role_route(
    id_role: int,
    db: AsyncSession = Depends(get_db), 
    token: str = Depends(oauth2_scheme),
    src: Optional[str] = None,
    page: Optional[int] = 1,
    page_size: Optional[int] = 10,
):
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        # permission_user = check_user_permission(db, user, "view", "RBAC")
        # if not permission_user:
        #     return common_response(Unauthorized())
        data, count, page_count = await rbacRepo.ls_user_role(
            db,
            src=src,
            page=page,
            page_size=page_size,
            id_role=id_role
        )
        return common_response(
            Ok(
            data=data,
            meta={
                "count": count,
                "page_count": page_count,
                "page_size": page_size,
                "page": page,
            }
            )
        )
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in role_management endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())

@router.delete(
    "/list/user-role/{email}",
    response_model=ListUserRole,
    responses={
        200: {"model": ListUserRoleResponse},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def delete_role_in_user(
    email: str,
    db: AsyncSession = Depends(get_db), 
    token: str = Depends(oauth2_scheme),
):
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        # permission_user = check_user_permission(db, user, "view", "RBAC")
        # if not permission_user:
        #     return common_response(Unauthorized())
        data  = await rbacRepo.delete_role_from_user_repo(
            db=db,
            email=email
        )
        return common_response(
            Ok(
                message="Role removed from user successfully",
            )
        )
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in role_management endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())


@router.put(
    "/update-multiple-permission",
    response_model=UpdateMultiplePermissionResponse,
    responses={
        200: {"model": UpdateMultiplePermissionResponse},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def update_multiple_permission(
    request: List[UpdatePermissionRequest],
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme)):
    try:
        updated_permissions = []
        for permission in request:
            data = await rbacRepo.update_permission(
                db=db,
                role_id=permission.role_id,
                permissions=permission.permissions,
            )
            updated_permissions.append(data)
            
        return common_response(
            CudResponse( message="Permissions updated successfully")
        )
    except ValueError as e:
        return common_response(BadRequest(message=str(e)))
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in update_multiple_permission endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())

@router.put(
    "/assign-role",
    responses={
        201: {"model": CudResponseSchema},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def assign_role(
    payload: AssignRoleRequest,
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme)):
    try:

        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        
        data = await rbacRepo.assign_role_to_user(db=db, payload=payload)
            
        return common_response(
            CudResponse( message="User role updated successfully")
        )
    except ValueError as e:
        return common_response(BadRequest(message=str(e)))
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in update_multiple_permission endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())

@router.post(
    "/add-role",
    responses={
        201: {"model": CudResponseSchema},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def add_role(
    payload: AddRoleRequest,
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme)):
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        
        permission_user = check_user_permission(db, user, "create", "RBAC")
        if not permission_user:
            return common_response(Unauthorized())
                
        data = await rbacRepo.add_role(db=db, payload=payload, user_id=user.id)
            
        return common_response(
            CudResponse(
                data=data,
                message="Role berhasil ditambahkan"
            )
        )
    except ValueError as e:
        return common_response(BadRequest(message=str(e)))
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in add_role endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())

@router.delete(
    "/delete-role",
    responses={
        200: {"model": CudResponseSchema},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def delete_role(
    payload: DeleteRoleRequest,
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme)):
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        
        # Check permission untuk menghapus role
        permission_user = check_user_permission(db, user, "delete", "RBAC")
        if not permission_user:
            return common_response(Unauthorized())
        
        data = await rbacRepo.delete_role(db=db, role_id=payload.role_id, user_id=user.id)
            
        return common_response(
            CudResponse(
                data=data,
                message="Role berhasil dihapus"
            )
        )
    except ValueError as e:
        return common_response(BadRequest(message=str(e)))
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in delete_role endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())

@router.get(
    "/permissions",
    responses={
        200: {"model": CudResponseSchema},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def get_permissions(
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme)):
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        permission_user = check_user_permission(db, user, "view", "RBAC")
        if not permission_user:
            return common_response(Unauthorized())
        data = await rbacRepo.get_permissions(db)
        return common_response(
            Ok(data=data)
        )
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in get_permissions endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())

# User Management Routes
@router.get(
    "/user-management",
    responses={
        200: {"model": CudResponseSchema},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def user_management(
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    page: int = 1,
    page_size: int = 10,
    src: Optional[str] = None,
):
    """
    Get list of all users with pagination and search functionality
    """
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        permission_user = check_user_permission(db, user, "view", "RBAC")
        if not permission_user:
            return common_response(Unauthorized())
        
        data, count, page_count = await rbacRepo.list_user_management(
            db, page=page, page_size=page_size, src=src
        )
        return common_response(
            Ok(
                data=data,
                meta={
                    "count": count,
                    "page_count": page_count,
                    "page_size": page_size,
                    "page": page,
                }
            )
        )
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in user_management endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())

@router.get(
    "/user-management/{user_id}",
    responses={
        200: {"model": CudResponseSchema},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def get_user_detail(
    user_id: str,
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme),
):
    """
    Get detailed information of a specific user
    """
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        permission_user = check_user_permission(db, user, "view", "RBAC")
        if not permission_user:
            return common_response(Unauthorized())
        
        user_detail = await rbacRepo.get_user_detail(db, user_id=user_id)
        if not user_detail:
            return common_response(NotFound(message="User not found"))
        
        return common_response(Ok(data=user_detail))
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in get_user_detail endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())

@router.put(
    "/user-management/{user_id}",
    responses={
        200: {"model": CudResponseSchema},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def update_user(
    user_id: str,
    payload: EditUserRequest,
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme),
):
    """
    Update user information
    """
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        permission_user = check_user_permission(db, user, "edit", "RBAC")
        if not permission_user:
            return common_response(Unauthorized())
        
        updated_user = await rbacRepo.update_user_management(
            db, user_id=user_id, request=payload
        )
        if not updated_user:
            return common_response(NotFound(message="User not found"))
        
        return common_response(CudResponse(message="User updated successfully"))
    except ValueError as e:
        return common_response(BadRequest(message=str(e)))
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in update_user endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())

@router.delete(
    "/user-management/{user_id}",
    responses={
        200: {"model": CudResponseSchema},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def delete_user_management(
    user_id: str,
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme),
):
    """
    Soft delete a user (set isact to False)
    """
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        permission_user = check_user_permission(db, user, "delete", "RBAC")
        if not permission_user:
            return common_response(Unauthorized())
        
        success = await rbacRepo.delete_user_management(db, user_id=user_id)
        if not success:
            return common_response(NotFound(message="User not found"))
        
        return common_response(CudResponse(message="User deleted successfully"))
    except ValueError as e:
        return common_response(BadRequest(message=str(e)))
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in delete_user_management endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())

@router.post(
    "/user-management",
    responses={
        201: {"model": CudResponseSchema},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def create_user_management(
    payload: SignUpRequest,
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme),
):
    """
    Create a new user
    """
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        permission_user = check_user_permission(db, user, "create", "RBAC")
        if not permission_user:
            return common_response(Unauthorized())
        
        await rbacRepo.create_user_management(db, request=payload)
        return common_response(CudResponse(message="User created successfully"))
    except ValueError as e:
        return common_response(BadRequest(message=str(e)))
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in create_user_management endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())

