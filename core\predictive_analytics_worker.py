import asyncio
import logging
from services.predictive_analytics_service import PredictiveAnalyticsService

logger = logging.getLogger(__name__)


async def run_predictive_analytics_retrain():
    """
    Worker untuk men<PERSON><PERSON><PERSON> proses retrain dan prediksi predictive analytics
    """
    try:
        logger.info("Starting Predictive Analytics Retrain Worker")
        
        # Inisialisasi service
        service = PredictiveAnalyticsService()
        
        # Jalankan proses retrain dan prediksi
        service.run_full_retrain_and_prediction()
        
        logger.info("Predictive Analytics Retrain Worker completed successfully")
        
    except Exception as e:
        logger.error(f"Error in Predictive Analytics Retrain Worker: {e}")
        raise


def run_predictive_analytics_retrain_sync():
    """
    Versi synchronous dari worker untuk compatibility dengan scheduler
    """
    try:
        logger.info("Starting Predictive Analytics Retrain Worker (Sync)")
        
        # Inisialisasi service
        service = PredictiveAnalyticsService()
        
        # Jalankan proses retrain dan prediksi
        service.run_full_retrain_and_prediction()
        
        logger.info("Predictive Analytics Retrain Worker (Sync) completed successfully")
        
    except Exception as e:
        logger.error(f"Error in Predictive Analytics Retrain Worker (Sync): {e}")
        raise
