from sqlalchemy import <PERSON><PERSON>ESTAMP, Column, Numeric, String, Integer, DateTime, ForeignKey, Boolean, Text, and_, func
from sqlalchemy.orm import relationship
from models import Base
from models.UserRole import UserRole
from models.RolePermission import RolePermission  # Ensure this is correctly imported


class UserKey(Base):
    __tablename__ = "user_key"

    id = Column(Integer, primary_key=True, nullable=False, index=True)
    userid = Column(String, nullable=False, index=True)
    secret_key = Column(String, nullable=True)
    created_by = Column(ForeignKey("user.id"), nullable=True)
    created_at = Column(DateTime(timezone=True))
    status = Column(Boolean, default=False)