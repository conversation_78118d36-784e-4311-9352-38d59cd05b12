# Predictive Analytics System

## Overview
Sistem Predictive Analytics ini dirancang untuk melakukan retrain model dan prediksi revenue secara otomatis menggunakan algoritma Prophet. Sistem ini akan ber<PERSON>lan secara scheduled setiap tahun sekali untuk menghasilkan model baru dan prediksi revenue untuk 11 bulan ke depan.

## Fitur Utama

### 1. Automated Retrain
- Retrain model secara otomatis setiap tanggal 1 bulan pukul 02:00
- Menggunakan data historis dari tabel `v_gla_all`
- Mendukung multiple produk dengan konfigurasi yang berbeda
- Model version otomatis update setiap bulan (format: vYYYY.MM)

### 2. Model Management
- Model disimpan dalam format `.pkl` di direktori `storage/models/`
- Setiap model memiliki timestamp untuk versioning
- Model version otomatis update setiap bulan (format: vYYYY.MM)
- Model lama akan diganti dengan model baru

### 3. Prediction Storage
- Hasil prediksi disimpan ke tabel `predictive_analytics`
- Prediksi lama akan dihapus dan diganti dengan prediksi baru
- Mendukung multiple produk dalam satu tabel

## Struktur File

### Repository
- `repository/predictive_analytics.py` - Repository untuk operasi database predictive analytics

### Routes
- `routes/predictive_analytics.py` - API endpoints untuk predictive analytics

### Service
- `services/predictive_analytics_service.py` - Service untuk retrain dan prediksi model

### Worker
- `core/predictive_analytics_worker.py` - Worker untuk scheduler

### Models
- `models/VGlaAll.py` - Model untuk tabel v_gla_all
- `models/PredictiveAnalytics.py` - Model untuk tabel predictive_analytics

### Configuration
- `config/model_parameters.py` - Konfigurasi parameter model untuk setiap produk

## Struktur Database

### Tabel v_gla_all (Schema: satgas_ai)
Berisi data historis revenue dari tahun 2019-2025 dengan kolom:
- `gl_dss_prod_cat`: Kategori produk
- `periode_rev`: Periode revenue
- `real_revenue`: Revenue aktual
- `regional`, `witel_name`: Informasi lokasi
- Dan kolom lainnya

### Tabel predictive_analytics (Schema: satgas_ai)
Berisi hasil prediksi dengan kolom:
- `gl_dss_prod_cat`: Kategori produk
- `period_date`: Tanggal prediksi
- `prediction_revenue`: Revenue yang diprediksi
- `prediction_direction`: Arah prediksi (up/down/same)
- `absolute_difference`: Selisih absolut dengan revenue sebelumnya
- `percentage_difference`: Selisih dalam persentase
- `model_version`: Versi model yang digunakan

## Produk yang Didukung

### 1. SMS A2P
- **Filter**: `sms a2p`
- **Seasonality**: Yearly (12 bulan) + Monthly (30.5 hari)
- **Growth**: Linear
- **Mode**: Multiplicative

### 2. IP Transit
- **Filter**: `ip transit`
- **Seasonality**: Semester (182.5 hari)
- **Growth**: Linear
- **Mode**: Multiplicative

### 3. Voice POTS
- **Filter**: `voice pots`
- **Seasonality**: 4-monthly (122 hari) + Monthly (30.5 hari)
- **Growth**: Linear
- **Mode**: Multiplicative

## Cara Menambahkan Produk Baru

### 1. Edit File Konfigurasi
Buka file `config/model_parameters.py` dan tambahkan konfigurasi baru:

```python
PRODUCT_CONFIGS['nama_produk_baru'] = {
    'filter_keyword': 'keyword untuk filter',
    'model_params': {
        'growth': 'linear',
        'yearly_seasonality': 10,
        'changepoint_prior_scale': 0.05,
        'seasonality_prior_scale': 15,
        'changepoint_range': 0.85,
        'seasonality_mode': 'multiplicative',
        'interval_width': 0.9,
        'n_changepoints': 15
    },
    'seasonality_configs': [
        {'name': 'yearly', 'period': 12, 'fourier_order': 12},
        {'name': 'quarterly', 'period': 91.25, 'fourier_order': 5}
    ]
}
```

### 2. Parameter Model yang Tersedia

#### Growth
- `linear`: Pertumbuhan linear
- `logistic`: Pertumbuhan logistik
- `flat`: Tidak ada pertumbuhan

#### Seasonality
- `yearly_seasonality`: Seasonality tahunan (True/False/angka)
- `weekly_seasonality`: Seasonality mingguan
- `daily_seasonality`: Seasonality harian

#### Changepoint
- `changepoint_prior_scale`: Fleksibilitas trend (0.001 - 0.5)
- `changepoint_range`: Range changepoint (0.8 - 0.95)
- `n_changepoints`: Jumlah changepoint (0 - 100)

#### Seasonality Mode
- `additive`: Seasonality aditif
- `multiplicative`: Seasonality multiplikatif

## Scheduler

### Automated Retrain
- **Cron**: `0 2 1 * *` (Setiap 1 bulan pukul 02:00)
- **Fungsi**: `scheduled_predictive_analytics_retrain()`
- **Lokasi**: `main.py`

### Manual Testing
Endpoint untuk testing manual:
```
GET /predictive-analytics/test-retrain
```

### API Endpoints

#### 1. Current Month Predictions
```
GET /predictive-analytics/current-month
```
Mengembalikan:
- Total prediction revenue bulan ini
- Total prediction revenue bulan depan
- Persentase perubahan antara bulan ini dan bulan depan
- Detail prediksi untuk setiap produk
- Model version terbaru yang digunakan

#### 2. Monthly Predictions
```
GET /predictive-analytics/monthly/{year}/{month}
```
Mengembalikan:
- Prediksi revenue untuk bulan dan tahun tertentu
- Total prediction revenue
- Rata-rata percentage difference
- Detail prediksi untuk setiap produk
- Model version terbaru yang digunakan

#### 3. Next Month All Products (Halaman Utama)
```
GET /predictive-analytics/next-month-all
```
**Halaman utama Predictive Analytics** yang berisi:
- Semua prediksi bulan depan untuk semua gl_dss_prod_cat
- Total revenue bulan depan
- Rata-rata percentage difference
- Trend summary (up/down/stable)
- Count untuk setiap trend
- Model version terbaru yang digunakan

**Contoh Response:**
```json
{
  "status": "success",
  "data": {
    "period": "2025-09",
    "model_version": "v2025.08",
    "summary": {
      "total_revenue": 1500000.00,
      "total_products": 3,
      "average_percentage_difference": 5.25,
      "trend_summary": "up",
      "up_count": 2,
      "down_count": 1,
      "stable_count": 0
    },
    "predictions": [
      {
        "gl_dss_prod_cat": "sms a2p",
        "prediction_revenue": 800000.00,
        "percentage_difference": 8.5,
        "prediction_direction": "up",
        "absolute_difference": 62500.00,
        "period_date": "2025-09-01",
        "previous_revenue": 737500.00,
        "model_version": "v2025.08"
      },
      {
        "gl_dss_prod_cat": "ip transit",
        "prediction_revenue": 500000.00,
        "percentage_difference": 3.2,
        "prediction_direction": "up",
        "absolute_difference": 15500.00,
        "period_date": "2025-09-01",
        "previous_revenue": 484500.00,
        "model_version": "v2025.08"
      },
      {
        "gl_dss_prod_cat": "voice pots",
        "prediction_revenue": 200000.00,
        "percentage_difference": -2.1,
        "prediction_direction": "down",
        "absolute_difference": 4300.00,
        "period_date": "2025-09-01",
        "previous_revenue": 204300.00,
        "model_version": "v2025.08"
      }
    ]
  }
}
```

#### 4. Prediction Summary
```
GET /predictive-analytics/summary
```
Mengembalikan:
- Summary dari semua prediksi bulan depan
- Total revenue, total products
- Average percentage difference
- Trend summary dan count

## Workflow Proses

### 1. Data Retrieval
- Mengambil data dari tabel `v_gla_all`
- Filter berdasarkan keyword produk
- Data diurutkan berdasarkan periode

### 2. Data Cleaning
- Drop kolom yang tidak diperlukan
- Hapus baris dengan data kosong
- Konversi periode ke format datetime
- Group by bulanan
- Handle missing values

### 3. Outlier Detection & Handling
- Menggunakan STL decomposition
- Modified Z-score untuk deteksi outlier
- Auto-replacement dengan mean/median

### 4. Model Training
- Split data train/test (3 bulan terakhir sebagai test)
- Konfigurasi Prophet sesuai parameter
- Fit model dengan data training
- Simpan model ke file `.pkl`

### 5. Prediction
- Load model dari file
- Generate future dates (11 bulan ke depan)
- Lakukan prediksi
- Hitung metrik (difference, direction, percentage)

### 6. Database Storage
- Hapus prediksi lama untuk produk yang sama
- Insert prediksi baru ke tabel `predictive_analytics`

## Monitoring & Logging

### Log Files
- Semua proses di-log dengan level INFO/ERROR
- Log tersimpan di `file.log`

### Error Handling
- Try-catch untuk setiap tahap proses
- Log error detail untuk debugging
- Continue processing untuk produk lain jika satu produk gagal

## Dependencies

### Required Packages
```bash
pip install prophet statsmodels scipy joblib pandas numpy
```

### Optional Packages
```bash
pip install matplotlib seaborn plotly tqdm
```

## Troubleshooting

### Common Issues

#### 1. Model Training Failed
- Cek koneksi database
- Pastikan data tidak kosong
- Cek parameter model

#### 2. Prediction Failed
- Pastikan model file ada
- Cek format data input
- Verifikasi struktur tabel

#### 3. Database Connection Error
- Cek environment variables
- Verifikasi koneksi database
- Cek schema permissions

### Debug Mode
Untuk debugging, gunakan endpoint manual:
```
GET /test-predictive-analytics-retrain
```

## Performance Considerations

### Data Processing
- Chunk processing untuk data besar
- Progress bar untuk monitoring
- Memory efficient operations

### Model Storage
- Model disimpan dalam format kompresi
- Timestamp untuk versioning
- Cleanup model lama (opsional)

### Database Operations
- Batch insert untuk efisiensi
- Transaction management
- Index optimization

## Future Enhancements

### 1. Model Versioning
- Model comparison
- A/B testing
- Rollback capability

### 2. Advanced Analytics
- Model performance metrics
- Cross-validation results
- Feature importance

### 3. Real-time Updates
- Streaming data processing
- Incremental model updates
- Real-time predictions

### 4. Dashboard Integration
- Prediction visualization
- Model performance monitoring
- Alert system

## Support & Maintenance

### Regular Maintenance
- Monitor model performance
- Update parameter configurations
- Clean up old model files
- Database optimization

### Backup & Recovery
- Backup model files
- Database backup
- Configuration backup
- Disaster recovery plan
