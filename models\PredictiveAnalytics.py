from sqlalchemy import Column, Integer, String, DateTime, Numeric, Date
from sqlalchemy.sql import func
from models import Base


class PredictiveAnalytics(Base):
    __tablename__ = "predictive_analytics"
    __table_args__ = {'schema': 'satgas_ai'}

    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    gl_dss_prod_cat = Column(String(100), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    previous_revenue = Column(Numeric(15, 2), nullable=True)
    period_date = Column(Date, nullable=False)
    actual_revenue = Column(Numeric(15, 2), nullable=True)
    prediction_revenue = Column(Numeric(15, 2), nullable=False)
    prediction_direction = Column(String(10), nullable=True, default='up')
    absolute_difference = Column(Numeric(15, 2), nullable=True)
    percentage_difference = Column(Numeric(8, 4), nullable=True)
    model_version = Column(String(50), nullable=True, default='v1.0')
