FROM python:3.11.2-slim-buster
# FROM python:3.10-buster
# FROM python:3.11-buster

WORKDIR /app

# RUN pip install docling

COPY ./requirements.txt .
# COPY ./.env . 
RUN pip install -r requirements.txt
# RUN pip install -r requirements.txt --proxy=http://**************:9356

RUN pip install watchfiles
# RUN pip install watchfiles --proxy=http://**************:9356


EXPOSE 8000
# Copy EasyOCR models to default cache path
COPY .EasyOCR /tmp/.EasyOCR

COPY . .

USER root
RUN mkdir -p /root/.cache && chmod -R 777 /root/.cache
RUN chmod -R 777 /app

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]