from sqlalchemy import Column, String, Integer, DateTime, Numeric, Text
from sqlalchemy.sql import func
from models import Base


class VGlaAll(Base):
    __tablename__ = "v_gla_all"
    __table_args__ = {'schema': 'satgas_ai'}

    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    gl_dss_prod_grp = Column(Text, nullable=True)
    gl_dss_prod_cat = Column(Text, nullable=True)
    gl_account = Column(Text, nullable=True)
    gl_desc = Column(Text, nullable=True)
    kode_cc = Column(Text, nullable=True)
    cfu = Column(Text, nullable=True)
    divisi = Column(Text, nullable=True)
    ubis = Column(Text, nullable=True)
    segmen = Column(Text, nullable=True)
    segmen_desc = Column(Text, nullable=True)
    subsegmen_desc = Column(Text, nullable=True)
    regional = Column(Text, nullable=True)
    kode_regional = Column(Text, nullable=True)
    witel = Column(Text, nullable=True)
    witel_name = Column(Text, nullable=True)
    tahun = Column(Text, nullable=True)
    periode = Column(Text, nullable=True)
    bulan = Column(Text, nullable=True)
    periode_rev = Column(DateTime(timezone=True), nullable=True)
    real_revenue = Column(Numeric(38, 20), nullable=True)
    target_revenue = Column(Numeric(38, 20), nullable=True)
    tgl_upd = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())


    product_mapping = {
        "HSI B2B": ["HSI B2B"],
        "ASTINET": ["ASTINET"],
        "IP TRANSIT": ["IP TRANSIT"],
        "METRO ETHERNET": ["METRO ETHERNET"],
        "SL DOMESTIK": ["SL DOMESTIK"],
        "GLOBAL LINK": ["GLOBAL LINK"],
        "VPN IP": ["VPN IP"],
        "MANAGED SDWAN SERVICE": ["MANAGED SDWAN SERVICE"],
        "COLLOCATION DC": ["COLLOCATION DC"],
        "WIFI MANAGED SERVICE": ["WIFI MANAGED SERVICE"],
        "WIFI VOUCHER B2B2C": ["WIFI VOUCHER B2B2C"],
        "HSI B2C": ["HIGH SPEED INTERNET"],
        "INTERCONNECTION": [
            "Interconnection-Interconnection-Lapeks",
            "Interconnection-Interconnection-Non"
        ],
        "SMS A2P": ["SMS A2P-SMS A2P-Lapeks"],
        "VOICE-POTS": [
            "Voice-Voice POTS-Lapeks",
            "Voice-Voice POTS-Non",
            "Value Added IMS App Service-Voice POTS-Lapeks"
        ],
        "SIP TRUNK": [
            "Voice-SIP Trunk-Lapeks",
            "Voice-SIP Trunk-Non"
        ],
        "CALL CENTER": [
            "Call Center-Call Center-Lapeks",
            "Call Center-Call Center-Non"
        ],
        "WIFI VAS & ADD ON OTHERS": [
            "WIFI VAS & ADD ON OTHERS CONNECTIVITY",
            "DIGITAL ADVERTISING",
            "WIFI DIGITAL ADVERTISING"
        ],
        "WIFI WHOLESALE ISP": [
            "WHOLESALE PORT",
            "INTERNATIONAL ROAMING"
        ]
    }