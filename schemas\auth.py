from typing import List, Optional, TypedDict
from pydantic import BaseModel, EmailStr

class MetaResponse(BaseModel):
    count:int
    page_count:int
    page_size:int
    page:int

class VerifMfa(BaseModel):
    secret: str
    token: str

class CreateSuccessResponse(BaseModel):
    meta: MetaResponse
    data: None
    status: str
    code: int
    message: str

class Organization(BaseModel):
    id: int
    name: str

class LoginRequest(BaseModel):
    nik: str
    password: str

class RefreshTokenRequest(BaseModel):
    refresh_token: str

class ResetMfa(BaseModel):
    nik: str

class LoginSuccess(BaseModel):
    # user_id: str
    status_mfa: Optional[bool]= False
    nik: str
    token: Optional[str] = None
    refresh_token: Optional[str] = None
    secret: Optional[str] = None
    qr_code: Optional[str] = None

class RefreshSuccess(BaseModel):
    nik: str
    token: str
    refresh_token: str

class LoginSuccessResponse(BaseModel):
    meta: MetaResponse
    data: LoginSuccess
    status: str
    code: int
    message: str

class MeSuccess(BaseModel):
    id: str
    email: str
    username: str
    is_active: bool
    refreshed_token: str

    class RoleDetail(BaseModel):
        id: int
        nama: str

        class GroupDetail(BaseModel):
            id: int
            nama: str

        group: Optional[GroupDetail]

    role: RoleDetail

    NIK: Optional[str]
    signature_path: Optional[str]

class SignupRequest(BaseModel):
    password:str
    username:str
    photo:str
    name:str
    email:str
    phone:str

class MeSuccessResponse(BaseModel):
    meta: MetaResponse
    data: MeSuccess
    status: str
    code: int
    message: str
    
class EditPassRequest(BaseModel):
    email:str
    password: str
    confirm_password: str

class RegisSuccessResponse(BaseModel):
    message:str
class CadSuccessResponse(BaseModel):
    message:str

class ListUserRequest(BaseModel):
    page: int = 1,
    page_size: int = 10
class MeRequest(BaseModel):
    token: str

class OtpRequest(BaseModel):
    otp: str
    email: str

class LoginTokenRequest(BaseModel):
    login_token: str

class ChangePasswordRequest(BaseModel):
    token: str
    password: str

class SignUpRequest(BaseModel):
    nik : str
    name: str
    role_id: int


class PermissionsResponse(BaseModel):
    class DetailPermission(BaseModel):
        id: int
        permission: str

        class DetailModule(BaseModel):
            id: int
            nama: str

        module: DetailModule

    results: List[DetailPermission]


class MenuResponse(BaseModel):
    class MenuDetail(BaseModel):
        id: int
        url: str
        name: str
        icon: str
        order: int
        is_has_child: bool

        class SubMenuDetail(BaseModel):
            id: int
            url: str
            name: str
            icon: str
            is_has_child: bool
            order: int
            sub_menu: list

        sub_menu: List[SubMenuDetail]

    results: List[MenuDetail]

class MenuDict(TypedDict):
    id: int
    url: str
    menu_name: str
    icon: str
    is_has_child: bool
    is_active: bool
    order: int
    sub_menu: List[dict]  # MenuDict

class ForgotPasswordSendEmailResponse(BaseModel):
    message: str = "success kirim email ganti password, silahkan cek email anda"
class ForgotPasswordSendEmailRequest(BaseModel):
    email: str

class ForgotPasswordChangePasswordResponse(BaseModel):
    message: str = "success menganti password anda"

class ForgotPasswordChangePasswordRequest(BaseModel):
    token: str
    password: str

class EditUserRequest(BaseModel):
    name: Optional[str] = None
    role_id: Optional[int] = None

class RoleOption(BaseModel):
    id: int
    name: str
    role: str

class RoleOptionsResponse(BaseModel):
    results: List[RoleOption]