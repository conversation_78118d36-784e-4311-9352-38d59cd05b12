<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1d9b5857-267f-4357-9aeb-ee99d4574ba0" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/core/myworker.py" beforeDir="false" afterPath="$PROJECT_DIR$/core/myworker.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/routes/auth.py" beforeDir="false" afterPath="$PROJECT_DIR$/routes/auth.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 0
}]]></component>
  <component name="ProjectId" id="31rHT3HD2zNWKVQ7se3FZlkA254" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "development",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/WORK/TELKOM AI/New folder/be-dcs-dashboard"
  }
}]]></component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="1d9b5857-267f-4357-9aeb-ee99d4574ba0" name="Changes" comment="" />
      <created>1756275275952</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1756275275952</updated>
    </task>
    <servers />
  </component>
</project>