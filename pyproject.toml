[tool.poetry]
name = "auth-service"
version = "0.1.0"
description = "pdp patuh auth service"
authors = ["pdp patuh"]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = {extras = ["standard"], version = "^0.115.8"}
aiokafka = "^0.12.0"
sentry-sdk = "^2.21.0"
python-dateutil = "^2.9.0.post0"
fastapi-mail = "^1.4.2"
uvicorn = "^0.34.0"
pytz = "^2025.1"
sqlalchemy = "^2.0.38"
supabase = "^2.13.0"
bcrypt = "^4.2.1"
python-jose = "^3.3.0"
service = "^0.6.0"
redis = "^5.2.1"
pytest-cov = "^6.0.0"
marshmallow = "^3.26.1"
psycopg2-binary = "^2.9.10"
cryptography = "^44.0.2"
pycryptodome = "^3.22.0"
asyncpg = "^0.30.0"
minio = "^7.2.15"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
