from sqlalchemy import (
    Column,
    String,
    ForeignKey,
    Integer,
    UUID,
    TIMESTAMP,
    func,
    Boolean,
    DateTime,
)
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base
import uuid
from models import Base
from models.UserRole import UserRole
from sqlalchemy.future import select
from datetime import date, timedelta
from sqlalchemy.sql import func


class User(Base):
    __tablename__ = "user"

    id = Column(
        String(36), primary_key=True, default=lambda: str(uuid.uuid4()), index=True
    )
    created_by = Column(String(36), nullable=False)
    updated_by = Column(String(36), nullable=False)
    email = Column(String, unique=True, nullable=False)
    name = Column(String, nullable=False)
    photo = Column(String, nullable=True)
    gender = Column(Integer, nullable=True)
    nik = Column(String, nullable=True)
    phone = Column(String, nullable=False)
    address = Column(String, nullable=False)
    password = Column(String, nullable=False)
    first_login = Column(String, nullable=False)
    birth_date = Column(String, nullable=False)
    created_at = Column(TIMESTAMP, server_default=func.now())
    updated_at = Column(TIMESTAMP)
    isact = Column(Boolean, default=True)
    status = Column(Boolean, default=True)
    id_seq = Column(Integer, nullable=True)
    id_user = Column(String(10), nullable=True)
    key = Column(String, nullable=True)
    key_exp = Column(TIMESTAMP, nullable=True)
    mfa_failed_attempts = Column(Integer, default=0)
    mfa_suspended_until = Column(TIMESTAMP, nullable=True)
    mfa_last_failed_attempt = Column(TIMESTAMP, nullable=True)
    login_failed_attempts = Column(Integer, default=0)
    login_suspended_until = Column(TIMESTAMP, nullable=True)
    refresh_token = Column(String, nullable=True)
    login_last_failed_attempt = Column(TIMESTAMP, nullable=True)


    # Many to Many
    roles = relationship("Role", secondary=UserRole, back_populates="users")
    files = relationship(
        "File", back_populates="uploader", cascade="all, delete-orphan"
    )
