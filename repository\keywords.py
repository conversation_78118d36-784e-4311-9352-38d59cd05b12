import traceback
from typing import Optional, List
from sqlalchemy import select, and_
from models.Keywords import Keywords
from sqlalchemy.ext.asyncio import AsyncSession
from schemas.keywords import KeywordCreate, KeywordUpdate, KeywordResponse
from datetime import datetime


async def get_all_keywords(db: AsyncSession) -> List[dict]:
    """Mendapatkan semua keywords"""
    try:
        result = await db.execute(select(Keywords))
        keywords = result.scalars().all()
        
        ls_data = []
        for keyword in keywords:
            ls_data.append(
                KeywordResponse(
                    id=str(keyword.id),
                    keyword=keyword.keyword,
                    description=keyword.description,
                    isact=keyword.isact,
                    created_at=keyword.created_at,
                    updated_at=keyword.updated_at
                ).model_dump(mode='json')  
            )
        return ls_data
    except Exception as e:
        traceback.print_exc()
        raise ValueError(f"Error in get_all_keywords: {str(e)}")


async def get_active_keywords(db: AsyncSession) -> List[dict]:
    """Mendapatkan keywords yang aktif"""
    try:
        result = await db.execute(select(Keywords).where(Keywords.isact == True))
        keywords = result.scalars().all()
        
        ls_data = []
        for keyword in keywords:
            ls_data.append(
                KeywordResponse(
                    id=str(keyword.id),
                    keyword=keyword.keyword,
                    description=keyword.description,
                    isact=keyword.isact,
                    created_at=keyword.created_at,
                    updated_at=keyword.updated_at
                ).model_dump(mode='json')  
            )
        return ls_data
    except Exception as e:
        traceback.print_exc()
        raise ValueError(f"Error in get_active_keywords: {str(e)}")


async def create_keyword(db: AsyncSession, keyword_data: KeywordCreate) -> dict:
    """Membuat keyword baru"""
    try:
        existing = await db.execute(
            select(Keywords).where(Keywords.keyword == keyword_data.keyword)
        )
        if existing.scalar():
            raise ValueError("Keyword sudah ada")
        
        keyword = Keywords(
            keyword=keyword_data.keyword,
            description=keyword_data.description,
            isact=keyword_data.isact,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db.add(keyword)
        await db.commit()
        await db.refresh(keyword)
        
        return KeywordResponse(
            id=str(keyword.id),
            keyword=keyword.keyword,
            description=keyword.description,
            isact=keyword.isact,
            created_at=keyword.created_at,
            updated_at=keyword.updated_at
        ).model_dump(mode='json')
    except Exception as e:
        traceback.print_exc()
        raise ValueError(f"Error in create_keyword: {str(e)}")


async def update_keyword(db: AsyncSession, keyword_id: str, keyword_data: KeywordUpdate) -> dict:
    """Mengupdate keyword"""
    try:
        result = await db.execute(select(Keywords).where(Keywords.id == keyword_id))
        keyword = result.scalar()
        
        if not keyword:
            raise ValueError("Keyword tidak ditemukan")
        
        # Update fields
        update_data = keyword_data.model_dump(exclude_unset=True)
        
        if 'keyword' in update_data:
            existing = await db.execute(
                select(Keywords).where(
                    and_(
                        Keywords.keyword == update_data['keyword'],
                        Keywords.id != keyword_id
                    )
                )
            )
            if existing.scalar():
                raise ValueError("Keyword sudah ada")
        
        for key, value in update_data.items():
            setattr(keyword, key, value)
        
        await db.commit()
        await db.refresh(keyword)
        
        return KeywordResponse(
            id=str(keyword.id),
            keyword=keyword.keyword,
            description=keyword.description,
            isact=keyword.isact,
            created_at=keyword.created_at,
            updated_at=keyword.updated_at
        ).model_dump(mode='json') 
    except Exception as e:
        traceback.print_exc()
        raise ValueError(f"Error in update_keyword: {str(e)}")


async def delete_keyword(db: AsyncSession, keyword_id: str) -> bool:
    """Menghapus keyword"""
    try:
        result = await db.execute(select(Keywords).where(Keywords.id == keyword_id))
        keyword = result.scalar()
        
        if not keyword:
            raise ValueError("Keyword tidak ditemukan")
        
        await db.delete(keyword)
        await db.commit()
        return True
    except Exception as e:
        traceback.print_exc()
        raise ValueError(f"Error in delete_keyword: {str(e)}")


async def get_keyword_by_id(db: AsyncSession, keyword_id: str) -> Optional[Keywords]:
    """Mendapatkan keyword berdasarkan ID"""
    try:
        result = await db.execute(select(Keywords).where(Keywords.id == keyword_id))
        return result.scalar()
    except Exception as e:
        traceback.print_exc()
        raise ValueError(f"Error in get_keyword_by_id: {str(e)}")


async def get_keyword_by_name(db: AsyncSession, keyword_name: str) -> Optional[Keywords]:
    """Mendapatkan keyword berdasarkan nama"""
    try:
        result = await db.execute(select(Keywords).where(Keywords.keyword == keyword_name))
        return result.scalar()
    except Exception as e:
        traceback.print_exc()
        raise ValueError(f"Error in get_keyword_by_name: {str(e)}")

