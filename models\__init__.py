import logging
import os
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker, AsyncSession
from sqlalchemy import create_engine
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy.exc import SQLAlchemyError
if os.environ.get("ENVIRONTMENT") != "prod":
    from dotenv import load_dotenv
from sqlalchemy import MetaData
 
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
from settings import (
    DB_USER,
    DB_PASS,
    DB_HOST,
    DB_PORT,
    DB_NAME,
    DEFAULT_SCHEMA,
)

DATABASE_URL = f"postgresql+asyncpg://{DB_USER}:{DB_PASS}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# Set metadata dengan schema default
metadata = MetaData(schema=DEFAULT_SCHEMA)
Base = declarative_base(metadata=metadata)
# Base = declarative_base()

engine = create_async_engine(
    DATABASE_URL,
    pool_size=30,
    max_overflow=40,
    pool_recycle=1800,
    pool_timeout=30,
    echo=False,
)

sync_engine = create_engine(DATABASE_URL.replace('+asyncpg', '')) 
sync_session = sessionmaker(bind=sync_engine)

async_session = async_sessionmaker(
    bind=engine,
    expire_on_commit=False,
    autoflush=False,
    autocommit=False,
    class_=AsyncSession,
)

async def get_db():
    async with async_session() as session:
        try:
            yield session
        except SQLAlchemyError as e:
            logger.error(f"Database error: {e}")
            await session.rollback()
            raise
        finally:
            await session.close()
