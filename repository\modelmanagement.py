from fastapi import File, UploadFile
import httpx
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import or_, select, func, update, delete
from sqlalchemy.orm import selectinload
from typing import Optional, List

from core.file import upload_file_from_path_to_minio, upload_file_to_minio_directly
from models.Files import File as Files
from models.Role import Role
from models.UserRole import UserRole
from models.User import User
from schemas.modelmanagement import ListUploadedFile
from schemas.rbac import ListUserWoRole
from settings import (
    ICA_AUTH_TOKEN,
    ICA_BASE_URL,
    ICA_PASSWORD,
    ICA_TOOL_ID,
    ICA_USERNAME,
    SERVICE_NAME,
)

ADMIN_ROLE = 1

def datetime_to_str(dt: Optional[str]) -> str:
    """Convert datetime to string in ISO format."""
    if dt:
        return dt.isoformat()
    return ""


def filesize_to_str(filesize: int) -> str:
    """Convert filesize in bytes to a human-readable string."""
    if filesize < 1024:
        return f"{filesize} B"
    elif filesize < 1024**2:
        return f"{filesize / 1024:.2f} KB"
    elif filesize < 1024**3:
        return f"{filesize / (1024**2):.2f} MB"
    else:
        return f"{filesize / (1024**3):.2f} GB"


async def get_uploaded_files(
    db: AsyncSession,
    user_id: Optional[str] = None,
    page: int = 1,
    page_size: int = 10,
):
    limit = page_size
    offset = (page - 1) * limit

    is_admin = False
    if user_id:
        admin_check_query = (
            select(UserRole)
            .where(UserRole.c.emp_id == user_id)
            .where(UserRole.c.role_id == ADMIN_ROLE)
        )
        result_admin = await db.execute(select(admin_check_query.exists()))
        is_admin = result_admin.scalar()
        print(f"\n\nAdmin check for user {user_id}: {is_admin}\n\n")

    base_query = select(Files).where(Files.isact == True)

    if is_admin:
        pass
    else:
        base_query = base_query.where(Files.user_id == user_id)
        base_query = base_query.where(Files.service != "external_crawling")

    count_query = select(func.count()).select_from(base_query.subquery())
    count_result = await db.execute(count_query)
    num_data = count_result.scalar_one_or_none() or 0

    data_query = (
        base_query.order_by(Files.created_at.desc())
        .options(selectinload(Files.uploader))  
        .offset(offset)
        .limit(limit)
    )
    result = await db.execute(data_query)

    files = result.unique().scalars().all()
    
    num_page = (num_data + limit - 1) // limit if limit > 0 else 1

    files_data = [
        ListUploadedFile(
            id=item.id,
            filename=item.filename,
            filesize=item.filesize,
            filetype=item.filetype,
            processing_status=item.processing_status,
            service=item.service,
            created_at=datetime_to_str(item.created_at),
            user_id=item.user_id,
            name=item.uploader.name if item.uploader else '-',
            message=item.message,
        ).model_dump()
        for item in files
    ]

    return files_data, num_data, num_page

async def save_uploaded_file(
    db: AsyncSession,
    directory: str,
    filename: str,
    filesize: int,
    filetype: str,
    user_id: str,
):
    new_file = Files(
        directory=directory,
        filename=filename,
        filesize=filesize,
        filetype=filetype,
        processing_status="queued",
        service=SERVICE_NAME,
        user_id=user_id,
        isact=True,
    )
    db.add(new_file)
    await db.commit()
    await db.refresh(new_file)
    return new_file.id


async def update_file_status(db: AsyncSession, file_id: int, status: str) -> bool:
    result = await db.execute(select(Files).where(Files.id == file_id))
    file = result.scalar_one_or_none()

    if not file:
        return {"success": False, "message": f"File with ID {file_id} not found"}

    query = update(Files).where(Files.id == file_id).values(processing_status=status)
    await db.execute(query)
    await db.commit()

    return True
