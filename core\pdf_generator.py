import base64
import io
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.colors import black, blue, red
import re
from typing import Optional

class MarkdownToPDF:
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self.setup_custom_styles()
    
    def setup_custom_styles(self):
        """Setup custom styles for different markdown elements"""
        # Heading styles
        self.styles.add(ParagraphStyle(
            name='CustomHeading1',
            parent=self.styles['Heading1'],
            fontSize=18,
            spaceAfter=12,
            spaceBefore=12,
            textColor=black,
            fontName='Helvetica-Bold'
        ))
        
        self.styles.add(ParagraphStyle(
            name='CustomHeading2',
            parent=self.styles['Heading2'],
            fontSize=16,
            spaceAfter=10,
            spaceBefore=10,
            textColor=black,
            fontName='Helvetica-Bold'
        ))
        
        self.styles.add(ParagraphStyle(
            name='CustomHeading3',
            parent=self.styles['Heading3'],
            fontSize=14,
            spaceAfter=8,
            spaceBefore=8,
            textColor=black,
            fontName='Helvetica-Bold'
        ))
        
        # Custom body style
        self.styles.add(ParagraphStyle(
            name='CustomBody',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=6,
            alignment=0,  # Left alignment
            fontName='Helvetica'
        ))
        
        # List item style
        self.styles.add(ParagraphStyle(
            name='ListItem',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=3,
            leftIndent=20,
            fontName='Helvetica'
        ))

    def markdown_to_elements(self, markdown_text: str):
        """Convert markdown text to reportlab elements"""
        elements = []
        
        # Split by lines and process each
        lines = markdown_text.split('\n')
        i = 0
        
        while i < len(lines):
            line = lines[i].strip()
            
            if not line:
                elements.append(Spacer(1, 6))
                i += 1
                continue
            
            # Process headings
            if line.startswith('### '):
                # H3
                heading_text = line[4:].strip()
                # Remove ** formatting
                heading_text = heading_text.replace('**', '')
                elements.append(Paragraph(heading_text, self.styles['CustomHeading3']))
                elements.append(Spacer(1, 6))
            elif line.startswith('## '):
                # H2
                heading_text = line[3:].strip()
                heading_text = heading_text.replace('**', '')
                elements.append(Paragraph(heading_text, self.styles['CustomHeading2']))
                elements.append(Spacer(1, 6))
            elif line.startswith('# '):
                # H1
                heading_text = line[2:].strip()
                heading_text = heading_text.replace('**', '')
                elements.append(Paragraph(heading_text, self.styles['CustomHeading1']))
                elements.append(Spacer(1, 6))
            elif line.startswith('- ') or line.startswith('* '):
                # List item
                list_text = line[2:].strip()
                # Handle bold text in list items
                list_text = self.format_bold_text(list_text)
                elements.append(Paragraph(f"• {list_text}", self.styles['ListItem']))
            elif line.startswith('   - ') or line.startswith('   * '):
                # Nested list item
                list_text = line[5:].strip()
                list_text = self.format_bold_text(list_text)
                elements.append(Paragraph(f"    ◦ {list_text}", self.styles['ListItem']))
            elif re.match(r'^\d+\.', line):
                # Numbered list
                list_text = re.sub(r'^\d+\.\s*', '', line)
                list_text = self.format_bold_text(list_text)
                elements.append(Paragraph(list_text, self.styles['ListItem']))
            else:
                # Regular paragraph
                # Handle bold text
                formatted_text = self.format_bold_text(line)
                elements.append(Paragraph(formatted_text, self.styles['CustomBody']))
            
            i += 1
        
        return elements
    
    def format_bold_text(self, text: str) -> str:
        """Convert **text** to <b>text</b> for reportlab"""
        # Handle bold text
        text = re.sub(r'\*\*(.*?)\*\*', r'<b>\1</b>', text)
        return text
    
    def generate_pdf(self, title: str, markdown_content: str) -> str:
        """Generate PDF from markdown content and return as base64 string"""
        
        # Create a BytesIO buffer
        buffer = io.BytesIO()
        
        # Create the PDF document
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        
        # Build the story (content)
        story = []
        
        # Add title
        if title:
            story.append(Paragraph(title, self.styles['CustomHeading1']))
            story.append(Spacer(1, 12))
        
        # Convert markdown to elements
        markdown_elements = self.markdown_to_elements(markdown_content)
        story.extend(markdown_elements)
        
        # Build the PDF
        doc.build(story)
        
        # Get the PDF data
        pdf_data = buffer.getvalue()
        buffer.close()
        
        # Convert to base64
        pdf_base64 = base64.b64encode(pdf_data).decode('utf-8')
        
        return pdf_base64

def generate_notification_pdf(title: str, insight_content: str) -> str:
    """
    Helper function to generate PDF from notification data
    
    Args:
        title: Notification title
        insight_content: Markdown content from insight column
        
    Returns:
        Base64 encoded PDF string
    """
    pdf_generator = MarkdownToPDF()
    return pdf_generator.generate_pdf(title, insight_content)
