from sqlalchemy import Column, Foreign<PERSON><PERSON>, String, Integer, TIMESTAMP, func, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
import uuid
from models import Base


class File(Base):
    __tablename__ = "files"

    id = Column(
        String(36), primary_key=True, default=lambda: str(uuid.uuid4()), index=True
    )
    directory = Column(String, nullable=False)
    filename = Column(String, nullable=False)
    filesize = Column(Integer, nullable=False)
    filetype = Column(String, nullable=False)
    processing_status = Column(String, nullable=True)
    service = Column(String, nullable=True)
    user_id = Column(String(36), ForeignKey("user.id"), nullable=True)
    created_at = Column(TIMESTAMP, server_default=func.now(), nullable=False)
    updated_at = Column(TIMESTAMP, onupdate=func.now())
    isact = Column(Boolean, default=True)
    message = Column(String, nullable=True)

    uploader = relationship("User", back_populates="files")
