import os
from typing import Dict, <PERSON>, Optional, Tu<PERSON>, Union, Any
from settings import APILOGY_KEY
# from langchain_docling import <PERSON>ling<PERSON>oader
# from docling.chunking import HybridChunker
from llama_index.core import SimpleDirectoryReader
from llama_index.core.node_parser import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ter, SemanticSplitterNodeParser
from llama_index.embeddings.openai import OpenAIEmbedding
from fastembed import LateInteractionTextEmbedding, SparseTextEmbedding
from qdrant_client.models import PointStruct
import logging

docling_logger = logging.getLogger("be-dcs-dashboard.docling")


class DoclingProcessor:
    """Core document linguistics processing utilities"""

    SUPPORTED_TEXT_EXTENSIONS = {".txt", ".md", ".csv", ".json", ".xml", ".html"}
    SUPPORTED_DOC_EXTENSIONS = {
        ".pdf",
        ".doc",
        ".docx",
        ".ppt",
        ".pptx",
        ".xls",
        ".xlsx",
    }

    @staticmethod
    def extract_text(file_path: str) -> Tuple[List[str], Dict[str, Any]]:
        """
        Extract text content from a document file

        Args:
            file_path: Path to the document file

        Returns:
            Tuple of (extracted_text, metadata)
        """
        file_ext = os.path.splitext(file_path)[1].lower()
        metadata = {"extension": file_ext, "size": os.path.getsize(file_path)}

        try:
            if file_ext in DoclingProcessor.SUPPORTED_TEXT_EXTENSIONS:
                return DoclingProcessor._extract_text_file(file_path, metadata)
            elif file_ext == ".pdf":
                return DoclingProcessor._extract_pdf(file_path, metadata)
            elif file_ext in {".doc", ".docx"}:
                return DoclingProcessor._extract_word(file_path, metadata)
            elif file_ext in {".ppt", ".pptx"}:
                return DoclingProcessor._extract_powerpoint(file_path, metadata)
            elif file_ext in {".xls", ".xlsx"}:
                return DoclingProcessor._extract_excel(file_path, metadata)
            else:
                docling_logger.warning(f"Unsupported file format: {file_ext}")
                return "", metadata
        except Exception as e:
            docling_logger.error(f"Error extracting text from {file_path}: {str(e)}")
            return "", metadata

    @staticmethod
    def _extract_text_file(
        file_path: str, metadata: Dict[str, Any]
    ) -> Tuple[List[str], Dict[str, Any]]:
        """Extract text from plain text files"""
        documents = SimpleDirectoryReader(
            input_files=[file_path],
        ).load_data()

        return documents, metadata

    @staticmethod
    def _extract_pdf(
        file_path: str, metadata: Dict[str, Any]
    ) -> Tuple[List[str], Dict[str, Any]]:
        """Extract text from PDF files"""
        documents = SimpleDirectoryReader(
            input_files=[file_path],
        ).load_data()

        if not documents:
            docling_logger.warning(f"No documents extracted from {file_path}")
            return [], metadata

        return documents, metadata

    @staticmethod
    def _extract_word(
        file_path: str, metadata: Dict[str, Any]
    ) -> Tuple[str, Dict[str, Any]]:
        """Extract text from Word documents"""
        documents = SimpleDirectoryReader(
            input_files=[file_path],
        ).load_data()

        return documents, metadata

    @staticmethod
    def _extract_powerpoint(
        file_path: str, metadata: Dict[str, Any]
    ) -> Tuple[str, Dict[str, Any]]:
        """Extract text from PowerPoint presentations"""
        documents = SimpleDirectoryReader(
            input_files=[file_path],
        ).load_data()

        return documents, metadata

    @staticmethod
    def _extract_excel(
        file_path: str, metadata: Dict[str, Any]
    ) -> Tuple[str, Dict[str, Any]]:
        """Extract text from Excel spreadsheets"""
        documents = SimpleDirectoryReader(
            input_files=[file_path],
        ).load_data()

        return documents, metadata

    @staticmethod
    def semantic_chunker(
        documents: List[str],
    ) -> List[str]:
        """
        Chunk documents semantically using SentenceSplitter

        Args:
            documents: List of document strings

        Returns:
            List of text chunks
        """
        from core.apilogy_embedding import ApilogyEmbedding

        embed_model = ApilogyEmbedding(model="nomic-embed-text-v2-moe")
        node_parser = SemanticSplitterNodeParser(
            buffer_size=1, breakpoint_percentile_threshold=95, embed_model=embed_model
        )
        nodes = node_parser.get_nodes_from_documents(documents)

        return [node.text for node in nodes]

    @staticmethod
    def hybrid_chunker(
        file_path: str
    ) -> List[dict]:
        """
        Chunk documents using hybrid chunking strategy and generate embeddings.

        Returns:
            List of dicts: [{'chunk': str, 'embedding': list}]
        """
        from docling_core.transforms.chunker.tokenizer.huggingface import HuggingFaceTokenizer
        from transformers import AutoTokenizer
        from docling.chunking import HybridChunker
        from docling.document_converter import DocumentConverter
        from core.apilogy_embedding import ApilogyEmbedding

        EMBED_MODEL_ID = "nomic-ai/nomic-embed-text-v2-moe"
        filename = os.path.basename(file_path)
        docling_logger.info(f"Using embedding model: {EMBED_MODEL_ID}")

        print("\nInitializing HuggingFaceTokenizer...\n")

        # print("Masa error di tokenizer")
        tokenizer = HuggingFaceTokenizer(
            tokenizer=AutoTokenizer.from_pretrained(EMBED_MODEL_ID)
        )
        print("\nHuggingFaceTokenizer initialized successfully\n")

        # file_path = documents[0].metadata.get("file_path")

        print(f"\nConverting document from file: {file_path}\n")
        # Convert the document to a DL document

        doc = DocumentConverter().convert(source=file_path).document
        chunker = HybridChunker(tokenizer=tokenizer)
        chunk_iter = chunker.chunk(dl_doc=doc)
        chunks = list(chunk_iter)
        docs = [chunker.contextualize(chunk=i) for i in chunks]
        
        # bm25_embeddings = list(SparseTextEmbedding("Qdrant/bm25").embed(docs))
        # late_embeddings = list(LateInteractionTextEmbedding("colbert-ir/colbertv2.0").embed(docs))

        # dense_embeddings = []
        # embed_model = ApilogyEmbedding(model="nomic-embed-text-v2-moe")
        # for d in docs:
        #     dense_embeddings.append(embed_model.get_text_embedding(d))

        # return {
        #     "chunks": chunks,
        #     "docs": docs,
        #     "dense_embeddings": dense_embeddings,
        #     "bm25_embeddings": bm25_embeddings,
        #     "late_interaction_embeddings": late_embeddings,
        #     "filename": filename
        # }

        return docs

    @staticmethod
    def store_hybrid_to_qdrant(
        processed_data: Dict[str, Any],
        collection_name: str,
        doc_type: str = "lapeks_insight",
        client=None,
    ) -> None:
        """
        Store hybrid embeddings to Qdrant (dense + sparse + late interaction)

        Args:
            processed_data: Dict hasil dari proses hybrid_chunker + embedding
            collection_name: Nama koleksi Qdrant
            doc_type: Jenis dokumen (default: "lapeks_insight")
            client: Optional QdrantService
        """
        import asyncio
        import threading
        import time
        from datetime import datetime, timezone
        from qdrant_client.models import PointStruct
        from qdrant_client.http.models import (
            Distance,
            VectorParams,
            SparseVectorParams,
            MultiVectorConfig,
            MultiVectorComparator,
            HnswConfigDiff,
        )
        from services.qdrant_client import QdrantService
        from settings import VECTOR_COLLECTION_NAME

        MAX_RETRIES = 3
        BATCH_SIZE = 100
        RETRY_DELAY = 2
        MODEL_NAME = "nomic-embed-text-v2-moe"

        should_close = False
        if client is None:
            try:
                from main import qdrant_service
                client = qdrant_service
                if client is None:
                    client = QdrantService()
                    should_close = True
                    if not client.connect():
                        docling_logger.error("Failed to connect to Qdrant")
                        return
            except (ImportError, AttributeError):
                client = QdrantService()
                should_close = True
                if not client.connect():
                    docling_logger.error("Failed to connect to Qdrant")
                    return

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            docling_logger.info(
                f"Created new event loop for thread {threading.current_thread().name}"
            )

        # try:
        #     # --- Create collection if not exists with hybrid config ---
        #     sample_dense = processed_data["dense_embeddings"][0]
        #     sample_colbert = processed_data["late_interaction_embeddings"][0]

        #     try:
        #         existing = await asyncio.to_thread(client.client.get_collections)
        #         existing_names = [col.name for col in existing.collections]

        #         if collection_name not in existing_names:
        #             await client.client.create_collection(
        #                 collection_name=collection_name,
        #                 vectors_config={
        #                     MODEL_NAME: VectorParams(
        #                         size=len(sample_dense),
        #                         distance=Distance.COSINE,
        #                     ),
        #                     "colbertv2.0": VectorParams(
        #                         size=len(sample_colbert[0]),
        #                         distance=Distance.COSINE,
        #                         hnsw_config=HnswConfigDiff(m=0),
        #                         multivector_config=MultiVectorConfig(
        #                             comparator=MultiVectorComparator.MAX_SIM
        #                         ),
        #                     ),
        #                 },
        #                 sparse_vectors_config={
        #                     "bm25": SparseVectorParams()
        #                 }
        #             )
        #             docling_logger.info(f"Hybrid collection '{collection_name}' created")
        #         else:
        #             docling_logger.info(f"Collection '{collection_name}' already exists")
        #     except Exception as e:
        #         docling_logger.error(f"Error creating hybrid collection '{collection_name}': {e}")
        #         raise

            docling_logger.info("Building points to store...")
            points = []
            for idx, (dense, bm25, late, doc, chunk) in enumerate(zip(
                processed_data["dense_embeddings"],
                processed_data["bm25_embeddings"],
                processed_data["late_interaction_embeddings"],
                processed_data["docs"],
                processed_data["chunks"]
            )):
                page_no = None
                if chunk.meta.doc_items and chunk.meta.doc_items[0].prov:
                    page_no = chunk.meta.doc_items[0].prov[0].page_no

                point = PointStruct(
                    id=idx,
                    vector={
                        MODEL_NAME: dense,
                        "bm25": bm25.as_object(),
                        "colbertv2.0": late,
                    },
                    payload={
                        "document": doc,
                        "doc_type": doc_type,
                        "filename": processed_data["filename"],
                        "page_number": page_no,
                        "upload_timestamp": datetime.now(timezone.utc).isoformat()
                    }
                )
                points.append(point)

            total_points = len(points)
            for i in range(0, total_points, BATCH_SIZE):
                batch = points[i:i + BATCH_SIZE]
                batch_num = i // BATCH_SIZE + 1
                total_batches = (total_points + BATCH_SIZE - 1) // BATCH_SIZE

                for attempt in range(1, MAX_RETRIES + 1):
                    try:
                        docling_logger.info(
                            f"Upserting hybrid batch {batch_num}/{total_batches} to collection '{collection_name}' (attempt {attempt}/{MAX_RETRIES})"
                        )
                        loop.run_until_complete(
                            asyncio.wait_for(
                                client.upsert_points(collection_name, batch),
                                timeout=180,
                            )
                        )
                        docling_logger.info(f"Batch {batch_num} upserted successfully")
                        break
                    except asyncio.TimeoutError:
                        if attempt < MAX_RETRIES:
                            docling_logger.warning(
                                f"Timeout batch {batch_num}. Retrying..."
                            )
                            time.sleep(RETRY_DELAY)
                        else:
                            docling_logger.error(
                                f"Failed to upsert batch {batch_num} after {MAX_RETRIES} attempts"
                            )
                            raise
                    except Exception as e:
                        docling_logger.error(f"Error in batch {batch_num}: {e}")
                        raise

            docling_logger.info(f"Stored {len(points)} hybrid points to Qdrant collection '{collection_name}'")
            print(f"\n\nStored {len(points)} hybrid points to Qdrant collection '{collection_name}'\n\n")
        except Exception as e:
            docling_logger.error(f"Error storing hybrid embeddings: {e}")
        finally:
            if should_close:
                client.close_connection()


    
    @staticmethod
    def store_to_qdrant(
        nodes: List[str], collection_name: str, client=None, vector_size: int = 768
    ) -> None:
        """
        Store processed nodes to Qdrant vector database

        Args:
            nodes: List of text nodes to store (plain text, not embeddings)
            collection_name: Name of the Qdrant collection
            client: Optional existing QdrantService instance
            vector_size: Size of the embedding vectors (default: 768)
        """
       
        from services.qdrant_client import QdrantService
        from core.apilogy_embedding import ApilogyEmbedding
        import asyncio
        import threading
        import time

        MAX_RETRIES = 3
        BATCH_SIZE = 100
        RETRY_DELAY = 2

        print(f"\n\nNodes: {nodes}\n\n")

        should_close = False
        if client is None:
            try:
                from main import qdrant_service

                client = qdrant_service
                if client is None:
                    client = QdrantService()
                    should_close = True
                    if not client.connect():
                        docling_logger.error("Failed to connect to Qdrant")
                        return
            except (ImportError, AttributeError):
                client = QdrantService()
                should_close = True
                if not client.connect():
                    docling_logger.error("Failed to connect to Qdrant")
                    return

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            docling_logger.info(
                f"Created new event loop for thread {threading.current_thread().name}"
            )

        # Embed nodes so that we can store them in Qdrant
        try:
            embed_model = ApilogyEmbedding(model="nomic-embed-text-v2-moe")

            print("\n\nStarting embedding nodes...\n\n")
            print(f"Embedding model: {embed_model.model}\n\n")

            # bm25_embeddings = list(SparseTextEmbedding("Qdrant/bm25").embed(nodes))
            # late_embeddings = list(LateInteractionTextEmbedding("colbert-ir/colbertv2.0").embed(nodes))


            # Embed nodes to create points
            print("\nStarting embedding nodes...\n")
            points = []
            for i, node in enumerate(nodes):
                embedding = embed_model.get_text_embedding(node)
                points.append({"id": i, "vector": embedding, "payload": {"text": node}})

            for attempt in range(1, MAX_RETRIES + 1):
                try:
                    docling_logger.info(
                        f"Creating collection '{collection_name}' (attempt {attempt}/{MAX_RETRIES})"
                    )
                    loop.run_until_complete(
                        asyncio.wait_for(
                            client.create_collection(
                                collection_name=collection_name,
                                vector_size=len(points[0]["vector"])
                                if points
                                else vector_size,
                                distance="cosine",
                            ),
                            timeout=180,  # 3 minutes timeout for collection creation
                        )
                    )
                    docling_logger.info(f"Collection '{collection_name}' ready")
                    break
                except asyncio.TimeoutError:
                    if attempt < MAX_RETRIES:
                        docling_logger.warning(
                            f"Timeout creating collection '{collection_name}'. Retrying in {RETRY_DELAY}s..."
                        )
                        time.sleep(RETRY_DELAY)
                    else:
                        docling_logger.error(
                            f"Failed to create collection '{collection_name}' after {MAX_RETRIES} attempts"
                        )
                        raise
                except Exception as e:
                    if "already exists" in str(e):
                        docling_logger.info(f"Collection '{collection_name}' already exists")
                        break
                    docling_logger.info(f"Note about collection '{collection_name}': {e}")
                    # Don't retry if it's not a timeout error
                    break

            # Process points in batches
            total_points = len(points)
            for i in range(0, total_points, BATCH_SIZE):
                batch = points[i : i + BATCH_SIZE]
                batch_num = i // BATCH_SIZE + 1
                total_batches = (total_points + BATCH_SIZE - 1) // BATCH_SIZE

                # Try to upsert with retries
                for attempt in range(1, MAX_RETRIES + 1):
                    try:
                        docling_logger.info(
                            f"Upserting batch {batch_num}/{total_batches} to collection '{collection_name}' (attempt {attempt}/{MAX_RETRIES})"
                        )
                        loop.run_until_complete(
                            asyncio.wait_for(
                                client.upsert_points(collection_name, batch),
                                timeout=180,
                            )
                        )
                        docling_logger.info(
                            f"Successfully upserted batch {batch_num}/{total_batches}"
                        )
                        break
                    except asyncio.TimeoutError:
                        if attempt < MAX_RETRIES:
                            docling_logger.warning(
                                f"Timeout upserting batch {batch_num}. Retrying in {RETRY_DELAY}s..."
                            )
                            time.sleep(RETRY_DELAY)
                        else:
                            docling_logger.error(
                                f"Failed to upsert batch {batch_num} after {MAX_RETRIES} attempts"
                            )
                            raise
                    except Exception as e:
                        docling_logger.error(f"Error upserting batch {batch_num}: {e}")
                        raise

            docling_logger.info(
                f"Successfully stored {len(nodes)} nodes to Qdrant collection '{collection_name}'"
            )
        except Exception as e:
            docling_logger.error(f"Error storing nodes to collection '{collection_name}': {e}")
        finally:
            if should_close:
                client.close_connection()