from typing import Optional, List, Dict, Any
from pytz import timezone
from sqlalchemy import or_, select, func, update, delete
from core.utils import generate_token, generate_token_custom
from models.ForgotPassword import ForgotPassword
from models.Menu import Menu
from models.Permission import Permission
from models.Role import Role
from models.UserRole import UserR<PERSON>
from models.User import User
from models.UserToken import UserToken
from schemas.auth import (
    LoginSuccessResponse,
    LoginRequest,
    MenuDict,
    SignUpRequest,
    SignupRequest,
    EditPassRequest,
    EditUserRequest,
    OtpRequest,
)
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from core.security import (
    generate_hash_password,
    get_user_permissions,
    validated_user_password
)
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timedelta
from schemas.auth import (
    LoginSuccess,
    Organization
)
from core.mail import send_reset_password_email
import secrets
import string
import traceback
import httpx
import socket

from settings import APPSNAME, APPSTOKEN, <PERSON>OK<PERSON>, TZ, TELKOM_AUTH_VALIDATE_URL, TELKOM_AUTH_VALIDATE_MITRA_URL


async def reset_mfa_user(db: AsyncSession, nik:str):
    try:
        user_query = select(User).filter(User.nik == nik, User.isact == True)
        user_result = await db.execute(user_query)
        user = user_result.scalar()
        
        if not user:
            return {"success": False, "message": "Reset MFA gagal , NIK tidak ditemukan"}
        
        # Reset MFA key
        await db.execute(update(User).where(User.nik == nik, User.isact == True).values(key=None))
        await db.commit()
        return {"success": True, "message": "MFA berhasil direset"}
    except Exception as e:
        print("Error in reset_mfa_user:", e)
        traceback.print_exc()
        return {"success": False, "message": "Terjadi kesalahan saat reset MFA"}


async def check_expired_mfa(db: AsyncSession, user_id: str):
    try:
        query = select(User).filter(
            User.id == user_id,
            User.isact == True
        )
        result = await db.execute(query)
        user = result.scalar()

        if user is None:
            raise ValueError("User not found or inactive")

        # Jika user.key tidak ada, MFA dianggap expired
        if not user.key:
            return True

        # Jika user.key_exp tidak ada, MFA dianggap expired
        if not user.key_exp:
            return True

        # Cek apakah waktu sekarang sudah melewati waktu expired
        now = datetime.now(timezone(TZ))
        if now.replace(tzinfo=None) > user.key_exp:
            return True

        # Jika semua valid, MFA belum expired
        return False

    except Exception as e:
        print("Error in check_expired_mfa:", e)
        traceback.print_exc()
        raise ValueError("Failed to check expired MFA")


async def get_user_by_nik(
    db: AsyncSession,
    nik: str,
) -> Optional[User]:
    try:
        query = select(User).filter(
            User.nik == nik,
            User.isact == True
        )
        result = await db.execute(query)
        user = result.scalar()
        if user is None:
            raise ValueError("Akses Ditolak, akun anda belum memiliki akses, silakan hubungi administrator.")
        return user.id
    except ValueError as ve:
        raise ve
    except Exception as e:
        traceback.print_exc()
        raise ValueError(str(e))

async def add_success_user(
    db: AsyncSession,
    username: str,
):
    try:
        print("Adding user:", username)
        exist_data = await db.execute(
            select(User).filter(User.name == username, User.email == username, User.nik == username, User.isact == True)
        )
        exist_data = exist_data.scalar_one_or_none()
        print("Existing user data:", exist_data)
        if exist_data is not None:
            print("User already exists, skipping creation.")
            return exist_data.id
        result = await db.execute(
            select(Role)
            .filter(Role.id == 1, Role.isact == True)
        )
        role = result.scalar()
        data =  User(
            name=username,
            email=username,
            nik=username,
        )
        data.roles.append(role)
        db.add(data)
        await db.commit()
        await db.refresh(data)
        return data.id
    except ValueError as ve:
        raise ve
    except Exception as e:
        traceback.print_exc()
        raise ValueError(str(e))

async def change_user_password_by_token(
    db: AsyncSession, token: str, new_password: str
) -> Optional[User]:
    query = select(ForgotPassword).where(ForgotPassword.token == token)
    result = await db.execute(query)
    forgot_password = result.scalar()
    if forgot_password == None:
        return None

    if (forgot_password.created_date + timedelta(minutes=10)) < datetime.now():
        return False

    user_id = forgot_password.user_id
    user = await db.execute(select(User).filter(User.id == user_id))
    user = user.scalar()
    user.password = generate_hash_password(password=new_password)
    db.add(user)
    await db.execute(delete(ForgotPassword).where(ForgotPassword.user_id == user.id))
    await db.commit()
    return user

async def generate_token_forgot_password(db: AsyncSession, user: User) -> str:
    try:
        """
        generate token -> save user and token to database -> return generated token
        """
        token = generate_token_custom()
        forgot_password = ForgotPassword(user_id=user.id, token=token, created_date = datetime.now())
        db.add(forgot_password)
        await db.commit()
        return token
    except Exception as e:
        print("Error generate token forgot password", e)
        traceback.print_exc()
        raise ValueError("Failed to generate token forgot password")

async def logout_user(db:AsyncSession, user:User, token:str):
    try:
        result = await db.execute(
            select(UserToken).filter(
                UserToken.emp_id == user.id,
                UserToken.token == token,
                UserToken.isact == True
            )
        )
        exist_data = result.scalar()
        # print("exist data", exist_data)
        if exist_data is not None:
            exist_data.isact = False
            db.add(exist_data)
            await db.commit()
        else:
            raise ValueError("User session not found")
        return "oke"
    except Exception as e:
        print("Error logout_user",e)
        raise ValueError("Logout Failed")


#function to resend otp for forget  passworw
def expand_menu_tree_with_permissions(
    db: Session, root_menu: List[Menu], permissions: List[Permission]
) -> List[MenuDict]:
    if len(root_menu) == 0:
        return []
    else:
        return [
            {
                "id": y.id,
                "url": y.url,
                "name": y.name,
                "icon": y.icon,
                "is_has_child": y.is_has_child,
                "isact": y.isact,
                "is_show": y.is_show,
                "order": y.order_id if y.order_id != None else 0,
                "sub_menu": expand_menu_tree_with_permissions(
                    db=db, root_menu=y.child, permissions=permissions
                ),
            }
            for y in sorted(root_menu, key=lambda d: d.id)
            if y.isact == True
            and (
                y.permission_id in [x.id for x in permissions]
                # or y.permission_id == None
            )
        ]
def prune_menu_tree(trees: List[MenuDict]) -> List[MenuDict]:
    pruned_tree = []
    for tree in trees:
        if tree["is_has_child"] and len(tree["sub_menu"]) == 0:
            continue
        elif tree["is_has_child"] and len(tree["sub_menu"]) > 0:
            tree["sub_menu"] = prune_menu_tree(tree["sub_menu"])
        pruned_tree.append(tree)
    return pruned_tree
def sort_menu_tree_by_order(trees: List[MenuDict]) -> List[MenuDict]:
    return [
        {
            "id": y["id"],
            "title": y["name"],
            "path": y["url"],
            "icon": y["icon"],
            "is_show": y["is_show"],
            # "is_has_child": y["is_has_child"],
            # "is_active": y["is_active"],
            # "order": y["order"],
            "sub": sort_menu_tree_by_order(y["sub_menu"]) if len(y["sub_menu"]) > 0 else False,
        }
        for y in sorted(trees, key=lambda d: d["order"])
    ]
async def generate_menu_tree_for_user(db: Session, user: User) -> List[MenuDict]:
    try:
        permissions = get_user_permissions(db=db, user=user)
        query = select(Menu).options(
                selectinload(Menu.child)
            ).where(Menu.parent_id == None).order_by(Menu.id.asc())
        result = await db.execute(query)
        root_menu: List[Menu] = result.scalars().all()
        menu_tree = expand_menu_tree_with_permissions(
            db=db, root_menu=root_menu, permissions=permissions
        )
        menu_tree = prune_menu_tree(menu_tree)
        menu_tree = sort_menu_tree_by_order(menu_tree)
        print("menu tree", menu_tree)
        return menu_tree
    except Exception as e:
        traceback.print_exc()
        print("Error generate menu tree for user", e)
        raise ValueError("Failed to generate menu tree for user")


async def get_key_user(db:AsyncSession, user_id: str):
    try:
        query = select(User.key).filter(User.id == user_id)
        result = await db.execute(query)
        return result.scalar()
    except Exception as e:
        print("Error get_key_user:", e)
        traceback.print_exc()
        raise ValueError("Failed to get user key")

async def login_user(db, username, password, ip_address=None):
    try:
        if not ip_address:
            # Get the current server's IP address
            hostname = socket.gethostname()
            ip_address = socket.gethostbyname(hostname)
        url = TELKOM_AUTH_VALIDATE_URL
        headers = {
            "Content-Type": "application/json",
            "AppsName": APPSNAME,
            "AppsToken": APPSTOKEN,
            "Cookie": COOKIE
        }
        payload = {
            "username": username,
            "password": password,
            "ip_address": ip_address
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, json=payload)
            data = response.json()
            
            if data.get("code") == 200:
                id_user = await get_user_by_nik(db=db, nik=username)
                print("id user", id_user)
                data["id"] = id_user
                return data
            elif data.get("note") == "Account not found, please check your account combination.":
                print("Account not found in validate, trying validatemitra...")
                url_mitra = TELKOM_AUTH_VALIDATE_MITRA_URL
                
                response_mitra = await client.post(url_mitra, headers=headers, json=payload)
                data_mitra = response_mitra.json()
                
                if data_mitra.get("code") == 200:
                    id_user = await get_user_by_nik(db=db, nik=username)
                    print("id user from validatemitra", id_user)
                    data_mitra["id"] = id_user
                    return data_mitra
                else:
                    print("Account also not found in validatemitra")
                    return False
            else:
                return False
    except ValueError as ve:
        raise ve
    except Exception as e:
        print("Error in login_user:", e)
        traceback.print_exc()
        raise ValueError("Login failed due to an error")

async def login_user_hybrid(db, username, password, ip_address=None):
    """
    Hybrid login function that supports both auth.telkom.co.id and local database authentication
    """
    try:
        user = await get_user_by_email(db, username, exclude_soft_delete=True)
        
        if user:
            if user.password and validated_user_password(user.password, password):
                return {
                    "code": 200,
                    "id": user.id,
                    "username": username,
                    "auth_type": "local"
                }
        
        if not ip_address:
            hostname = socket.gethostname()
            ip_address = socket.gethostbyname(hostname)
            
        url = TELKOM_AUTH_VALIDATE_URL
        headers = {
            "Content-Type": "application/json",
            "AppsName": APPSNAME,
            "AppsToken": APPSTOKEN,
            "Cookie": COOKIE
        }
        payload = {
            "username": username,
            "password": password,
            "ip_address": ip_address
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, json=payload)
            data = response.json()
            if data.get("code") == 200:
                # Auth.telkom.co.id authentication successful
                id_user = await get_user_by_nik(db=db, nik=username)
                print("id user from telkom auth", id_user)
                data["id"] = id_user
                data["auth_type"] = "telkom"
                return data
            elif data.get("note") == "Account not found, please check your account combination.":
                print("Account not found in validate, trying validatemitra...")
                url_mitra = TELKOM_AUTH_VALIDATE_MITRA_URL
                
                response_mitra = await client.post(url_mitra, headers=headers, json=payload)
                data_mitra = response_mitra.json()
                
                if data_mitra.get("code") == 200:
                    id_user = await get_user_by_nik(db=db, nik=username)
                    print("id user from validatemitra", id_user)
                    data_mitra["id"] = id_user
                    data_mitra["auth_type"] = "telkom_mitra"
                    return data_mitra
                else:
                    print("Account also not found in validatemitra")
                    return False
            else:
                return False
                
    except ValueError as ve:
        raise ve
    except Exception as e:
        print("Error in login_user_hybrid:", e)
        traceback.print_exc()
        raise ValueError("Login failed due to an error")

async def login_user_local(db, username, password):
    """
    Local database authentication only
    """
    try:
        # Try to find user by email or nik
        user = await get_user_by_email(db, username, exclude_soft_delete=True)
        
        if not user:
            # Try to find by nik if not found by email
            user = await get_user_by_nik(db, username)
            if isinstance(user, str):  # get_user_by_nik returns user.id as string
                user = await get_user_by_id(db, user)
        
        if user and user.password and validated_user_password(user.password, password):
            return {
                "code": 200,
                "id": user.id,
                "username": username,
                "auth_type": "local"
            }
        else:
            return False
            
    except Exception as e:
        print("Error in login_user_local:", e)
        traceback.print_exc()
        return False

async def create_user_session(db: Session, user_id: str, token:str) -> str:
    try:
        exist_data = await db.execute(
            select(UserToken).filter(
                UserToken.emp_id == user_id,
                UserToken.token == token
            )
        )
        exist_data = exist_data.scalar()
        if exist_data is not None:
            exist_data.is_active = True
            db.add(exist_data)
            await db.commit()
        else:
            user_token = UserToken(emp_id=user_id, token=token)
            db.add(user_token)
            await db.commit()
        return 'succes'
    except Exception as e:
        print(f"Error creating user session: \n {e}")
        raise ValueError("Failed to create user session")
        

async def forgot_password(db, email):
    try:
        # Query the user from the User model using SQLAlchemy
        query = select(User).filter(User.email == email)
        result = await db.execute(query)
        user_obj = await result.scalar()

        if not user_obj:
            raise ValueError("User with this email not found.")

        user_id = user_obj.id
        user_email = user_obj.email
        random_token = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(6))
        expiry_time = datetime.now() + timedelta(minutes=10)


        await send_reset_password_email( 
            email_to=user_email,
            body={
                "email": user_email,
                "token": random_token,
            }
        )
        return True

    except ValueError as ve:
        raise ve
    except Exception as e:
        print(f"Error in forgot_password for {email}: {e}")
        traceback.print_exc()
        raise ValueError("Failed to process password reset request. Please try again later.")


async def get_user_by_email(
    db: AsyncSession, email: str, exclude_soft_delete: bool = False
) -> Optional[User]:
    try:
        if exclude_soft_delete == True:
            query = select(User).filter(User.email == email, User.isact == True)
        else:
            query = select(User).filter(User.email == email)
        results = await db.execute(query)
        user = results.scalar()
        return user
    except Exception as e:
        print("Error login : ",e)
        traceback.print_exc()
        return None

async def check_user_password(db: AsyncSession, email: str, password: str) -> Optional[User]:
    try:
        user = await get_user_by_email(db, email=email)
        if user is None:
            return None
        else:
            if validated_user_password(user.password, password):
                return user
        return None
    except Exception as e:
        print("Error in check_user_password:", e)
        traceback.print_exc()
        return None

    
async def list_user(
    db: AsyncSession,
    page: int = 1,
    page_size: int = 10,
    src: Optional[str] = None,
):
    try:
        limit = page_size
        offset = (page - 1) * limit

        query = select(User.id, User.name).filter(User.isact == True)
        query_count = select(func.count(User.id)).filter(User.isact == True)

        if src:
            query = query.filter(User.name.ilike(f"%{src}%"))
            query_count = query_count.filter(User.name.ilike(f"%{src}%"))

        query = query.order_by(User.created_at.desc()).limit(limit).offset(offset)

        result = await db.execute(query)
        rows = result.all()  

        data = [{"id": row.id, "name": row.name} for row in rows]

        count_result = await db.execute(query_count)
        num_data = count_result.scalar()  
        num_page = (num_data + limit - 1) // limit

        return (data, num_data, num_page)

    except Exception as e:
        raise ValueError(e)

async def get_user_by_id(
    db: AsyncSession,
    user_id: str,
) -> Optional[User]:
    try:
        query = select(User).filter(
            User.id == user_id,
            User.isact == True
        ).options(
            selectinload(User.roles)
        )
        
        result = await db.execute(query)
        user = await result.scalar_one_or_none()
        
        return user

    except Exception as e:
        raise ValueError(e)

async def edit_user(
    db: AsyncSession,
    user_id: str,
    request: EditUserRequest,
) -> Optional[User]:
    try:
        user = await get_user_by_id(db=db, user_id=user_id)
        if not user:
            return None

        if request.name is not None:
            user.name = request.name
        if request.phone is not None:
            user.phone = request.phone
        if request.address is not None:
            user.address = request.address
        if request.isact is not None:
            user.isact = request.isact

        if request.role_id is not None:
            user.roles = []
            
            role_query = select(Role).filter(Role.id == request.role_id, Role.isact == True)
            role_result = await db.execute(role_query)
            new_role = await role_result.scalar_one_or_none()
            
            if new_role:
                user.roles.append(new_role)

        db.add(user)
        await db.commit()
        await db.refresh(user)
        
        return user

    except Exception as e:
        traceback.print_exc()
        raise ValueError(str(e))

async def get_role_options(
    db: AsyncSession,
) -> List[Dict[str, Any]]:
    try:
        query = select(Role).filter(
            Role.isact == True
        ).order_by(Role.id.asc())

        result = await db.execute(query)
        roles = await result.scalars()

        role_options = [
            {
                "id": role.id,
                "name": role.name,
                "role" : role.group
            }
            for role in roles
        ]

        return role_options

    except Exception as e:
        traceback.print_exc()
        raise ValueError(str(e))

async def sign_up (
    db: AsyncSession,
    request: SignUpRequest,
):
    try:
        result = await db.execute(
            select(Role)
            .filter(Role.id == 10, Role.isact == True)
        )
        role = result.scalar()
        data =  User(
            email=request.email,
            name=request.email,
            nik=request.email,
        )
        data.roles.append(role)
        db.add(data)
        await db.commit()
        return True
    except Exception as e:
        traceback.print_exc()
        raise ValueError(str(e))
