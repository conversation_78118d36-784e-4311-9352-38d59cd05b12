from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, Boolean
from models import Base
import uuid

class LogSystem(Base):
    __tablename__ = "log_system"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), index=True)
    username = Column(String, nullable=False)
    action = Column(String, nullable=True)
    action_type = Column(String, nullable=True)
    status = Column(String, nullable=False)
    ip_address = Column(String, nullable=True)
    created_at = Column(DateTime(timezone=True), nullable=True)
