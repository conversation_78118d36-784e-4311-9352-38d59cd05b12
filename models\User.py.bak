from sqlalchemy import Column, Integer, Numeric, String, TIMESTAMP
from sqlalchemy.ext.declarative import declarative_base
from models import Base

class User(Base):
    __tablename__ = 'user'
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), nullable=False)
    email = Column(String(255), nullable=False, unique=True)
    password = Column(String(255), nullable=False)