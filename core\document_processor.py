from typing import List, Optional, Dict, Any, Literal
from datetime import datetime, timezone
from docling_core.transforms.chunker.tokenizer.huggingface import HuggingFaceTokenizer
from docling.document_converter import DocumentConverter
from docling.chunking import HybridChunker
from fastembed import LateInteractionTextEmbedding, SparseTextEmbedding
from transformers import AutoTokenizer
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, models, PointStruct, Filter, FieldCondition, MatchValue
from openai import OpenAI
import logging

from settings import (
    VECTOR_COLLECTION_NAME,
    VECTOR_DB_URL,
    APILOGY_KEY
)

import logging

document_processor_logger = logging.getLogger("be-dcs-dashboard.document_processor")

OPENAI_API_KEY = "dummy-key"
OPENAI_BASE_URL = "https://telkom-ai-dag.api.apilogy.id/Text_Embedding/0.0.1/v1"
EMBED_MODEL_ID = "nomic-ai/nomic-embed-text-v2-moe"
MODEL_NAME = "nomic-embed-text-v2-moe"
MAX_TOKENS = 512
# COLLECTION_NAME = "nomic-dcs-hybrid-search"
BATCH_SIZE = 25
QDRANT_TIMEOUT = 120

class DocumentProcessor:
    def __init__(self):
        self._initialize_clients()
        self._initialize_models()
        self._ensure_collection()
    
    def _initialize_clients(self):
        """Initialize Qdrant and OpenAI clients"""
        self.qdrant_client = QdrantClient(
            url=VECTOR_DB_URL,
            timeout=QDRANT_TIMEOUT
        )
        
        self.openai_client = OpenAI(
            api_key=OPENAI_API_KEY,
            base_url=OPENAI_BASE_URL,
            default_headers={"x-api-key": APILOGY_KEY}
        )
    
    def _initialize_models(self):
        """Initialize tokenizer and embedding models"""
        self.tokenizer = HuggingFaceTokenizer(
            tokenizer=AutoTokenizer.from_pretrained(pretrained_model_name_or_path="./local_tokenizer"),
            max_tokens=MAX_TOKENS
        )
        self.dense_embedding_model = self.openai_client.embeddings
        self.bm25_embedding_model = SparseTextEmbedding(model_name="Qdrant/bm25", cache_dir="./models_ml/bm25")
        self.late_interaction_embedding_model = LateInteractionTextEmbedding(model_name="colbert-ir/colbertv2.0",cache_dir='./models_ml/colbert')
        
        self.chunker = HybridChunker(tokenizer=self.tokenizer)
        self.doc_converter = DocumentConverter()
    
    def _ensure_collection(self):
        """Create collection if it doesn't exist"""
        try:
            collections = self.qdrant_client.get_collections().collections
            collection_names = [col.name for col in collections]
            
            if VECTOR_COLLECTION_NAME not in collection_names:
                self._create_collection()
                document_processor_logger.info(f"Created collection: {VECTOR_COLLECTION_NAME}")
            else:
                document_processor_logger.info(f"Collection {VECTOR_COLLECTION_NAME} already exists")
        except Exception as e:
            document_processor_logger.error(f"Error checking/creating collection: {str(e)}")
            raise
    
    def _create_collection(self):
        """Create Qdrant collection with hybrid search configuration"""
        # Get sample embeddings to determine sizes
        sample_text = "sample"
        sample_dense = self.dense_embedding_model.create(
            input=sample_text,
            model=MODEL_NAME,
            encoding_format="float"
        ).data[0].embedding
        
        sample_late = list(self.late_interaction_embedding_model.embed([sample_text]))[0]
        
        self.qdrant_client.create_collection(
            VECTOR_COLLECTION_NAME,
            vectors_config={
                MODEL_NAME: models.VectorParams(
                    size=len(sample_dense),
                    distance=models.Distance.COSINE,
                ),
                "colbertv2.0": models.VectorParams(
                    size=len(sample_late[0]),
                    distance=models.Distance.COSINE,
                    multivector_config=models.MultiVectorConfig(
                        comparator=models.MultiVectorComparator.MAX_SIM,
                    ),
                    hnsw_config=models.HnswConfigDiff(m=0)
                ),
            },
            sparse_vectors_config={
                "bm25": models.SparseVectorParams(
                    modifier=models.Modifier.IDF
                )
            }
        )
    
    def check_file_exists(self, filename: str) -> bool:
        """Check if a file with the same name already exists in the collection"""
        try:
            # Search for points with the same filename
            search_result = self.qdrant_client.scroll(
                collection_name=VECTOR_COLLECTION_NAME,
                scroll_filter=Filter(
                    must=[
                        FieldCondition(
                            key="filename",
                            match=MatchValue(value=filename)
                        )
                    ]
                ),
                limit=1,  # We only need to know if at least one exists
                with_payload=False,
                with_vectors=False
            )
            
            # If we found any points, the file exists
            return len(search_result[0]) > 0
            
        except Exception as e:
            document_processor_logger.error(f"Error checking file existence: {str(e)}")
            # In case of error, we'll allow the upload to proceed
            return False
    
    def process_file(self, file_path: str, filename: str) -> Dict[str, Any]:
        """Process a single file and return chunks with metadata"""
        try:
            # Convert document
            doc = self.doc_converter.convert(source=file_path).document
            
            # Create chunks
            chunk_iter = self.chunker.chunk(dl_doc=doc)
            chunks = list(chunk_iter)
            
            # Contextualize chunks
            docs = [self.chunker.contextualize(chunk=chunk) for chunk in chunks]
            
            # Generate embeddings
            bm25_embeddings = list(self.bm25_embedding_model.embed(docs))
            late_interaction_embeddings = list(self.late_interaction_embedding_model.embed(docs))
            
            dense_embeddings = []
            for doc in docs:
                embedding = self.dense_embedding_model.create(
                    input=doc,
                    model=MODEL_NAME,
                    encoding_format="float"
                ).data[0].embedding
                dense_embeddings.append(embedding)
            
            print(f"\n\nProcessed {len(chunks)} chunks from file {filename}\n\n")
            print(f"\nChunks {chunks}\n")
            print(f"\ndense_embeddings {dense_embeddings} \n")
            print(f"\nbm25_embeddings {bm25_embeddings} \n")
            print(f"\nlate_interaction_embeddings {late_interaction_embeddings} \n")
            print(f"\nDone process file\n")
            return {
                "chunks": chunks,
                "docs": docs,
                "dense_embeddings": dense_embeddings,
                "bm25_embeddings": bm25_embeddings,
                "late_interaction_embeddings": late_interaction_embeddings,
                "filename": filename
            }
        
        except Exception as e:
            document_processor_logger.error(f"Error processing file {filename}: {str(e)}")
            raise
    
    def create_points(self, processed_data: Dict[str, Any], start_id: int, doc_type: str = "lapeks_insight") -> List[PointStruct]:
        """Create Qdrant points from processed data"""
        points = []
        
        for idx, (dense, bm25, late, doc, chunk) in enumerate(zip(
            processed_data["dense_embeddings"],
            processed_data["bm25_embeddings"],
            processed_data["late_interaction_embeddings"],
            processed_data["docs"],
            processed_data["chunks"]
        )):
            # Extract page number safely
            page_no = None
            if chunk.meta.doc_items and chunk.meta.doc_items[0].prov:
                page_no = chunk.meta.doc_items[0].prov[0].page_no
            
            point = PointStruct(
                id=start_id + idx,
                vector={
                    MODEL_NAME: dense,
                    "bm25": bm25.as_object(),
                    "colbertv2.0": late,
                },
                payload={
                    "document": doc,
                    "doc_type": doc_type,
                    "filename": processed_data["filename"],
                    "page_number": page_no,
                    "upload_timestamp": datetime.now(timezone.utc).isoformat()
                }
            )
            points.append(point)
        
        print(f"\n\nCreated {len(points)} points for file {processed_data['filename']}\n\n")
        return points
    
    def batch_upsert_points(self, points: List[PointStruct]) -> List[Dict[str, Any]]:
        """Upsert points to Qdrant in batches"""
        
        print(f"\n\nStarting batch upsert of {len(points)} points\n\n")
        results = []
        
        for i in range(0, len(points), BATCH_SIZE):
            batch = points[i:i + BATCH_SIZE]
            
            try:
                operation_info = self.qdrant_client.upsert(
                    collection_name=VECTOR_COLLECTION_NAME,
                    points=batch
                )
                results.append({
                    'batch_index': i // BATCH_SIZE,
                    'batch_size': len(batch),
                    'status': 'success'
                })
                document_processor_logger.info(f"Batch {i // BATCH_SIZE + 1} completed: {len(batch)} points")
                
                print(f"\n\nSuccefully upserted to Qdrant: {len(batch)} points\n\n")
                print(f"\n\nOperation qdrant: {operation_info}\n\n")
            except Exception as e:
                results.append({
                    'batch_index': i // BATCH_SIZE,
                    'batch_size': len(batch),
                    'error': str(e),
                    'status': 'failed'
                })
                document_processor_logger.error(f"Batch {i // BATCH_SIZE + 1} failed: {str(e)}")
        
        return results
    
    def get_next_id(self) -> int:
        """Get the next available ID for points"""
        try:
            # Get the current count of points in the collection
            collection_info = self.qdrant_client.get_collection(VECTOR_COLLECTION_NAME)
            return collection_info.points_count
        except Exception as e:
            document_processor_logger.error(f"Error getting next ID: {str(e)}")
            return 0
