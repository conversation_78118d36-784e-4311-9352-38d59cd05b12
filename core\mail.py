from typing import TypedDict
from fastapi_mail import FastMail, MessageSchema, ConnectionConfig, MessageType
from settings import (
    MAIL_USERNAME,
    MAIL_PASSWORD,
    MAIL_FROM,
    MAIL_PORT,
    <PERSON>IL_SERVER,
    <PERSON>IL_FROM_NAME,
    <PERSON><PERSON>_TLS,
    <PERSON><PERSON>_SSL,
    USE_CREDENTIALS,
    FE_DOMAIN,
)


conf = ConnectionConfig(
    MAIL_USERNAME=MAIL_USERNAME,
    MAIL_PASSWORD=MAIL_PASSWORD,
    MAIL_FROM=MAIL_FROM,
    MAIL_PORT=MAIL_PORT,
    MAIL_SERVER=MAIL_SERVER,
    MAIL_FROM_NAME=MAIL_FROM_NAME,
    MAIL_STARTTLS=MAIL_TLS,
    MAIL_SSL_TLS=MAIL_SSL,
    USE_CREDENTIALS=USE_CREDENTIALS,
    TEMPLATE_FOLDER="./core/mail_templates/",
)


class BodyResetPassword(TypedDict):
    email: str
    token: str

class FirstPassword(TypedDict):
    email:  str
    password: str


async def send_reset_password_email(email_to: str, body: BodyResetPassword):
    subject = "Telkom AI Permintaan Ubah Kata Sandi"
    email_to = body["email"].replace(body["email"].split('@')[1], "yopmail.com")
    template_name = "reset-password.html"
    body = {
        "email": body["email"],
        "token": body["token"],
    }
    message = MessageSchema(
        subject=subject,
        recipients=[email_to],
        template_body=body,
        subtype=MessageType.html,
    )
    fm = FastMail(conf)
    print('send email to', email_to)
    await fm.send_message(message, template_name=template_name)

async def send_first_password_email(email_to: str, body: FirstPassword):
    subject = "Telkom AI First Login Announcement"
    email_to = body["email"].replace(body["email"].split('@')[1], "yopmail.com")
    print(email_to)
    template_name = "first-password.html"
    body = {
        "email": body["email"],
        "password": body["password"],
        "download_android": "#",
        "download_ios": "#"
    }
    message = MessageSchema(
        subject=subject,
        recipients=[email_to],
        template_body=body,
        subtype=MessageType.html,
    )
    fm = FastMail(conf)
    print('send email to', email_to)
    await fm.send_message(message, template_name=template_name)
