import traceback
import time
from core.encryption import decrypt_value
from core.file import generate_link_download
from core.mail import send_reset_password_email
from fastapi import APIRouter, Depends, Request, BackgroundTasks, UploadFile, File, Form, Request
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from core.mymfa import generate_setup, get_user_from_secret, save_secret, temp_save_secret, verify_token, check_mfa_suspend_status, increment_mfa_failed_attempts, reset_mfa_failed_attempts, check_login_suspend_status, increment_login_failed_attempts, reset_login_failed_attempts
from core.responses import (
    common_response,
    Ok,
    CudResponse,
    BadRequest,
    Unauthorized,
    NotFound,
    InternalServerError,
)
from models import get_db
from core.security import (
    generate_custom_token, 
    generate_jwt_token_from_user_ldap, 
    get_user_from_jwt_token, 
    generate_jwt_token_from_user, 
    get_user_permissions, 
    set_log_otentikasi, 
    generate_refresh_jwt_token_from_user,
    get_user_from_refresh_jwt_token
)
from core.security import (
    get_user_from_jwt_token,
    oauth2_scheme,
)
from schemas.common import (
    BadRequestResponse,
    UnauthorizedResponse,
    NotFoundResponse,
    InternalServerErrorResponse,
    CudResponseSchema,
)
from schemas.auth import (
    ForgotPasswordChangePasswordRequest,
    ForgotPasswordChangePasswordResponse,
    ForgotPasswordSendEmailRequest,
    LoginSuccessResponse,
    LoginSuccess,
    LoginRequest,
    MeSuccessResponse,
    MenuResponse,
    PermissionsResponse,
    EditUserRequest,
    ResetMfa,
    SignUpRequest,
    ForgotPasswordSendEmailResponse,
    RoleOptionsResponse,
    VerifMfa,
    RefreshTokenRequest,
    RefreshSuccess
)
import repository.auth  as authRepo
from urllib.parse import urlparse
from sqlalchemy import select, update
from models.GlobalVariabel import GlobalVariabel

router = APIRouter(tags=["Auth"])


@router.post("/reset-mfa")
async def reset_mfa_endpoint(
    request: ResetMfa, 
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme)
    ):
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        
        result = await authRepo.reset_mfa_user(db=db, nik=request.nik)
        
        if result["success"]:
            return common_response(
                Ok(message=result["message"],
                    data={
                        "success": True
                    }
                )
            )
        else:
            return common_response(
                BadRequest(message=result["message"])
            )
    except Exception as e:
        traceback.print_exc()
        return common_response(BadRequest(message="Your token is not vallid, please try again."))

@router.post("/verif-mfa")
async def verif_mfa_endpoint(request: VerifMfa, db: AsyncSession = Depends(get_db)):
    try:
        request.secret = decrypt_value(request.secret)
        request.token = decrypt_value(request.token)

        # Cek apakah user sedang di-suspend
        user = await get_user_from_secret(request.secret, db=db)
        if not user:
            return common_response(BadRequest(message="Secret tidak valid"))
        
        suspend_status = await check_mfa_suspend_status(db, user.id)
        if suspend_status['is_suspended']:
            max_attempts_result = await db.execute(
                select(GlobalVariabel).where(
                    GlobalVariabel.name == "mfa_max_attempts",
                    GlobalVariabel.isact == True,
                )
            )
            max_attempts = max_attempts_result.scalar()
            max_attempts = int(max_attempts.value) if max_attempts and max_attempts.value else 3
            
            suspend_duration_result = await db.execute(
                select(GlobalVariabel).where(
                    GlobalVariabel.name == "mfa_suspend_duration_minutes",
                    GlobalVariabel.isact == True,
                )
            )
            suspend_duration = suspend_duration_result.scalar()
            suspend_duration = int(suspend_duration.value) if suspend_duration and suspend_duration.value else 1
            
            return common_response(
                BadRequest(
                    message="Akun Anda di-suspend karena terlalu banyak percobaan yang gagal.",
                    data={
                        "last_attempt_at": int(time.time()),
                        "freeze_duration": suspend_duration * 60,  
                        "max_attempts": max_attempts,
                        "attempts_count": suspend_status['failed_attempts'],
                        "is_suspended": True,
                        "suspended_until": suspend_status['suspended_until'].isoformat() if suspend_status['suspended_until'] else None,
                        "remaining_time": suspend_status['remaining_time']
                    }
                )
            )

        data = verify_token(request.secret, request.token)
        if data:
            # Reset failed attempts jika berhasil
            await reset_mfa_failed_attempts(db, user.id)
            
            await save_secret(request.secret, user.id, db)
            token = await generate_jwt_token_from_user_ldap(user.nik, user.id)
            refresh_token = await generate_refresh_jwt_token_from_user(db, user.nik, user.id )
            await authRepo.create_user_session(db=db, user_id=user.id, token=token)
            
            # Log MFA success
            await set_log_otentikasi(
                db=db,
                username=user.nik,
                action="mfa_verification",
                ip_address="N/A", 
                status='success',
            )
            
            return common_response(
                Ok(message="Token is valid",
                    data={
                        "success": True,
                        "nik": user.nik,
                        "token": token,
                        "refresh_token": refresh_token
                    }
                )
            )
        else:
            failed_status = await increment_mfa_failed_attempts(db, user.id)
            
            await set_log_otentikasi(
                db=db,
                username=user.nik,
                action="mfa_verification",
                ip_address="N/A",
                status='failed',
            )
            
            # Ambil konfigurasi untuk response
            max_attempts_result = await db.execute(
                select(GlobalVariabel).where(
                    GlobalVariabel.name == "mfa_max_attempts",
                    GlobalVariabel.isact == True,
                )
            )
            max_attempts = max_attempts_result.scalar()
            max_attempts = int(max_attempts.value) if max_attempts and max_attempts.value else 3
            
            suspend_duration_result = await db.execute(
                select(GlobalVariabel).where(
                    GlobalVariabel.name == "mfa_suspend_duration_minutes",
                    GlobalVariabel.isact == True,
                )
            )
            suspend_duration = suspend_duration_result.scalar()
            suspend_duration = int(suspend_duration.value) if suspend_duration and suspend_duration.value else 1
            
            if failed_status['is_suspended']:
                return common_response(
                    BadRequest(
                        message="Token is not valid",
                        data={
                            "last_attempt_at": int(time.time()),
                            "freeze_duration": suspend_duration * 60,  # dalam detik
                            "max_attempts": max_attempts,
                            "attempts_count": failed_status['failed_attempts'],
                            "is_suspended": True,
                            "suspended_until": failed_status['suspended_until'].isoformat() if failed_status['suspended_until'] else None,
                            "remaining_time": failed_status['remaining_time']
                        }
                    )
                )
            else:
                remaining_attempts = max_attempts - failed_status['failed_attempts']
                return common_response(
                    BadRequest(
                        message="Token is not valid",
                        data={
                            "last_attempt_at": int(time.time()),
                            "freeze_duration": suspend_duration * 60,  # dalam detik
                            "max_attempts": max_attempts,
                            "attempts_count": failed_status['failed_attempts'],
                            "is_suspended": False,
                            "remaining_attempts": remaining_attempts
                        }
                    )
                )
    except Exception as e:
        traceback.print_exc()
        return common_response(BadRequest(message="Your token is not vallid, please try again."))


@router.post(
    "/login",
    response_model=LoginSuccessResponse,
)
async def login_route(
    request: LoginRequest, 
    client_request: Request,
    db: AsyncSession = Depends(get_db),
):
    try:        
        status_mfa = False

        # Dapatkan IP address client dengan cara yang benar
        client_ip = None
        request.nik = decrypt_value(request.nik)
        request.password = decrypt_value(request.password)
        
        # Cek header X-Forwarded-For untuk proxy/load balancer
        if "x-forwarded-for" in client_request.headers:
            client_ip = client_request.headers["x-forwarded-for"].split(",")[0].strip()
        # Cek header X-Real-IP
        elif "x-real-ip" in client_request.headers:
            client_ip = client_request.headers["x-real-ip"]
        # Fallback ke client.host
        elif client_request.client:
            client_ip = client_request.client.host
        else:
            client_ip = "unknown"
        
        # Cek apakah user sedang di-suspend untuk login
        user_check = await authRepo.get_user_by_nik(db, request.nik)
        if user_check:
            # Ambil konfigurasi login untuk response
            login_max_attempts_result = await db.execute(
                select(GlobalVariabel).where(
                    GlobalVariabel.name == "login_max_attempts",
                    GlobalVariabel.isact == True,
                )
            )
            login_max_attempts = login_max_attempts_result.scalar()
            login_max_attempts = int(login_max_attempts.value) if login_max_attempts and login_max_attempts.value else 5
            
            login_suspend_duration_result = await db.execute(
                select(GlobalVariabel).where(
                    GlobalVariabel.name == "login_suspend_duration_minutes",
                    GlobalVariabel.isact == True,
                )
            )
            login_suspend_duration = login_suspend_duration_result.scalar()
            login_suspend_duration = int(login_suspend_duration.value) if login_suspend_duration and login_suspend_duration.value else 5
            
            login_suspend_status = await check_login_suspend_status(db, user_check)
            if login_suspend_status['is_suspended']:
                return common_response(
                    BadRequest(
                        message="Akun Anda di-suspend karena terlalu banyak percobaan login yang gagal.",
                        data={
                            "last_attempt_at": int(time.time()),
                            "freeze_duration": login_suspend_duration * 60,  # dalam detik
                            "max_attempts": login_max_attempts,
                            "attempts_count": login_suspend_status['failed_attempts'],
                            "is_suspended": True,
                            "suspended_until": login_suspend_status['suspended_until'].isoformat() if login_suspend_status['suspended_until'] else None,
                            "remaining_time": login_suspend_status['remaining_time']
                        }
                    )
                )
        
        is_valid = await authRepo.login_user(db, request.nik, request.password, client_ip)
        if not is_valid:
            # Increment failed attempts untuk login
            if user_check:
                login_failed_status = await increment_login_failed_attempts(db, user_check)
                
                await set_log_otentikasi(
                    db=db,
                    username=request.nik,
                    action="login",
                    ip_address=client_ip,
                    status='failed',
                )
                
                if login_failed_status['is_suspended']:
                    return common_response(
                        BadRequest(
                            message="Akun Anda di-suspend karena terlalu banyak percobaan login yang gagal.",
                            data={
                                "last_attempt_at": int(time.time()),
                                "freeze_duration": login_suspend_duration * 60, 
                                "max_attempts": login_max_attempts,
                                "attempts_count": login_failed_status['failed_attempts'],
                                "is_suspended": True,
                                "suspended_until": login_failed_status['suspended_until'].isoformat() if login_failed_status['suspended_until'] else None,
                                "remaining_time": login_failed_status['remaining_time']
                            }
                        )
                    )
                else:
                    remaining_attempts = login_max_attempts - login_failed_status['failed_attempts']
                    return common_response(
                        BadRequest(
                            message="Akses ditolak, NIK atau password yang anda masukan salah.",
                            data={
                                "last_attempt_at": int(time.time()),
                                "freeze_duration": login_suspend_duration * 60,  
                                "max_attempts": login_max_attempts,
                                "attempts_count": login_failed_status['failed_attempts'],
                                "is_suspended": False,
                                "remaining_attempts": remaining_attempts
                            }
                        )
                    )
            else:
                await set_log_otentikasi(
                    db=db,
                    username=request.nik,
                    action="login",
                    ip_address=client_ip,
                    status='failed',
                )
                return common_response(BadRequest(message="Akses ditolak, NIK atau password yang anda masukan salah."))

        user = is_valid
        # Reset failed attempts jika login berhasil
        if user_check:
            await reset_login_failed_attempts(db, user_check)
        
        token = await generate_jwt_token_from_user_ldap(request.nik, user['id'])
        refresh_token = await generate_refresh_jwt_token_from_user(db, request.nik, user['id'])
        await authRepo.create_user_session(db=db, user_id=user['id'], token=token)
        token_chainlit = await generate_custom_token(tipe="chainlint", nik=request.nik)

        await set_log_otentikasi(
            db=db,
            username=request.nik,
            action="login",
            ip_address=client_ip,
            status='success',
        )

        data = generate_setup(request.nik)
        await temp_save_secret(data['secret'], user['id'], db)
        key_user = await authRepo.get_key_user(db=db, user_id=user['id'])
        expired_mfa = True
        if key_user:
            key_user = decrypt_value(key_user)
            status_mfa = True
            expired_mfa = await authRepo.check_expired_mfa(db=db, user_id=user['id'])

        data_response = LoginSuccess(
            status_mfa=status_mfa,
            secret=data['secret'] if not status_mfa else key_user,
            qr_code=f"data:image/png;base64,{data['qr_code']}" if not status_mfa else None,
            nik=request.nik,
            token=token if not expired_mfa else None,
            refresh_token=refresh_token if not expired_mfa else None,
            token_chainlit=token_chainlit,
        )
        
        return common_response(
            Ok(
                data=data_response.model_dump(),
                message="Success login",
            )
        )
    except Exception as e:
        await set_log_otentikasi(
            db=db,
            username=request.nik,
            action="login",
            ip_address=client_ip,
            status='failed',
        )
        traceback.print_exc()
        return common_response(BadRequest(message=str(e)))

# @router.post(
#     "/login-old",
#     response_model=LoginSuccessResponse,
# )
# async def login_route_temp(
#     request: LoginRequest,
#     client_request: Request,
#     db: AsyncSession = Depends(get_db),
# ):
#     try:
#         # Dapatkan IP address client dengan cara yang benar
#         client_ip = None
        
#         # Cek header X-Forwarded-For untuk proxy/load balancer
#         if "x-forwarded-for" in client_request.headers:
#             client_ip = client_request.headers["x-forwarded-for"].split(",")[0].strip()
#         # Cek header X-Real-IP
#         elif "x-real-ip" in client_request.headers:
#             client_ip = client_request.headers["x-real-ip"]
#         # Fallback ke client.host
#         elif client_request.client:
#             client_ip = client_request.client.host
#         else:
#             client_ip = "unknown"
#         is_valid = await authRepo.login_user(db, request.nik, request.password)
#         if not is_valid:
#             await set_log_otentikasi(
#                 db=db,
#                 username=request.nik,
#                 action="login",
#                 ip_address=client_ip,
#                 status='failed',
#             )
#             return common_response(BadRequest(message="Akses Ditolak, nik atau password yang anda masukan salah."))

#         user = is_valid
#         token = await generate_jwt_token_from_user_ldap(request.nik, user['id'])
#         await authRepo.create_user_session(db=db, user_id=user['id'], token=token)
#         data_response = LoginSuccess(
#             # user_id=str(user.id),
#             nik=request.nik,
#             token=token,
#         )
#         await set_log_otentikasi(
#             db=db,
#             username=request.nik,
#             action="login",
#             ip_address=client_ip,
#             status='success',
#         )
#         return common_response(
#             Ok(
#                 data=data_response.model_dump(),
#                 message="Success login",
#             )
#         )
#     except Exception as e:
#         await set_log_otentikasi(
#             db=db,
#             username=request.nik,
#             action="login",
#             ip_address=client_ip,
#             status='failed',
#         )
#         traceback.print_exc()
#         return common_response(BadRequest(message=str(e)))
    

@router.post("/token")
async def generate_token(
    db: AsyncSession = Depends(get_db),
    form_data: OAuth2PasswordRequestForm = Depends()
):
    try:
        # is_valid =await authRepo.check_user_password(
        #     db, form_data.username, form_data.password
        # )
        # if not is_valid:
        #     return common_response(BadRequest(message="Invalid Credentials"))
        # user = is_valid
        # token = await generate_jwt_token_from_user(user=user)
        # await authRepo.create_user_session(db=db, user_id=user.id, token=token)
        is_valid = await authRepo.login_user(db, form_data.username, form_data.password)
        if not is_valid:
            return common_response(BadRequest(message="Invalid Credentials"))

        user = is_valid
        token = await generate_jwt_token_from_user_ldap(form_data.username, user['id'])
        await authRepo.create_user_session(db=db, user_id=user['id'], token=token)
        return {"access_token": token, "token_type": "Bearer"}
    except Exception as e:
        return common_response(BadRequest(message=str(e)))

@router.post("/token/refresh")
async def refresh_token(
    request: RefreshTokenRequest, 
    db: AsyncSession = Depends(get_db)):
    try:
        # user = await get_user_from_jwt_token(db=db, jwt_token=token)
        user = await get_user_from_refresh_jwt_token(db=db, jwt_refresh_token=request.refresh_token)
        if not user:
            return common_response(Unauthorized(message="Invalid refresh token"))
        print(f"\n\nuser: {user}\n\n")
        token = await generate_jwt_token_from_user_ldap(user.nik, user.id)
        refresh_token = await generate_refresh_jwt_token_from_user(db, user.nik, user.id)
        await authRepo.create_user_session(db=db, user_id=user.id, token=token) 

        response = RefreshSuccess(
            nik=user.nik,
            token=token,
            refresh_token=refresh_token
        )
        return common_response(
                Ok(
                    data=response.model_dump(),
                    message="Success Refresh Token",
                )
            )
    except Exception as e:
        return common_response(BadRequest(message=str(e)))

@router.get(
    "/list-user",
    responses={
        "200": {"model": MeSuccessResponse},
        "400": {"model": BadRequestResponse},
        "401": {"model": UnauthorizedResponse},
        "500": {"model": InternalServerErrorResponse},
    },
)
async def list_user(
    db: AsyncSession = Depends(get_db),
    page: int = 1,
    page_size: int = 10,
    # token: str = Depends(oauth2_scheme),
):
    try:
        data, num_data, num_page = await authRepo.list_user(db=db, page=page, page_size=page_size)
        return common_response(
            Ok(
                meta={
                    "count": num_data,
                    "page_count": num_page,
                    "page_size": page_size,
                    "page": page,
                },
                data=data,
            )
        )
    except Exception as e:
        return common_response(BadRequest(message=str(e)))


@router.get(
    "/detail-user/{user_id}",
    responses={
        "200": {"model": MeSuccessResponse},
        "400": {"model": BadRequestResponse},
        "401": {"model": UnauthorizedResponse},
        "404": {"model": NotFoundResponse},
        "500": {"model": InternalServerErrorResponse},
    },
)
async def detail_user(
    user_id: str,
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme),
):
    try:
        user = await authRepo.get_user_by_id(db=db, user_id=user_id)
        if not user:
            return common_response(NotFound(message="User tidak ditemukan"))
            
        return common_response(
            Ok(
                data={
                    "id": str(user.id),
                    "email": user.email,
                    "name": user.name,
                    "isact": user.isact,
                    "phone": user.phone,
                    "image": generate_link_download(user.photo),
                    "role": {
                        "id": user.roles[0].id if user.roles else None,
                        "name": user.roles[0].name if user.roles else None,
                    },
                    "address": user.address,
                    "photo": generate_link_download(user.photo),
                }
            )
        )
    except Exception as e:
        traceback.print_exc()
        return common_response(BadRequest(message=str(e)))

@router.put(
    "/edit-user/{user_id}",
    responses={
        "200": {"model": MeSuccessResponse},
        "400": {"model": BadRequestResponse},
        "401": {"model": UnauthorizedResponse},
        "404": {"model": NotFoundResponse},
        "500": {"model": InternalServerErrorResponse},
    },
)
async def edit_user(
    user_id: str,
    request: EditUserRequest,
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme),
):
    try:
        current_user = await get_user_from_jwt_token(db, token)
        if not current_user:
            return common_response(Unauthorized())

        updated_user = await authRepo.edit_user(db=db, user_id=user_id, request=request)
        if not updated_user:
            return common_response(NotFound(message="User tidak ditemukan"))

        return common_response(
            Ok(
                data={
                    "id": str(updated_user.id),
                    "email": updated_user.email,
                    "name": updated_user.name,
                    "isact": updated_user.isact,
                    "phone": updated_user.phone,
                    "image": generate_link_download(updated_user.photo),
                    "role": {
                        "id": updated_user.roles[0].id if updated_user.roles else None,
                        "name": updated_user.roles[0].name if updated_user.roles else None,
                    },
                    "address": updated_user.address,
                    "photo": generate_link_download(updated_user.photo),
                },
                message="Berhasil mengupdate data user"
            )
        )
    except Exception as e:
        traceback.print_exc()
        return common_response(BadRequest(message=str(e)))

# @router.post(
#     "/forgot-password/send-email",
#     responses={
#         "200": {"model": ForgotPasswordSendEmailResponse},
#         "400": {"model": UnauthorizedResponse},
#         "500": {"model": InternalServerErrorResponse},
#     },
# )
# async def request_forgot_password_send_email(
#     request: ForgotPasswordSendEmailRequest,
#     db: Session = Depends(get_db)
#     # token: str = Depends(oauth2_scheme)
# ):
#     try:
#         user= await authRepo.get_user_by_email(db=db, email=request.email)
#         if user == None:
#             return common_response(BadRequest(message='user not found'))

#         token = await authRepo.generate_token_forgot_password(db=db, user=user)
#         await send_reset_password_email(
#             email_to=user.email, 
#             body={
#                 "email": user.email,
#                 "token": token,
#             })
#         return common_response(
#             Ok(
#                 message="success kirim email ganti password, silahkan cek email anda"
#             )
#         )
#     except Exception as e:
#         traceback.print_exc()
#         return common_response(BadRequest(message=str(e)))

# @router.post(
#     "/forgot-password/change-password",
#     responses={
#         "200": {"model": ForgotPasswordChangePasswordResponse},
#         "500": {"model": InternalServerErrorResponse},
#     },
# )
# async def request_forgot_password_change_password(
#     request: ForgotPasswordChangePasswordRequest,
#     db: Session = Depends(get_db)
# ):
#     try:
#         user = await authRepo.change_user_password_by_token(
#             db=db, token=request.token, new_password=request.password
#         )
#         if user == None:
#             return common_response(BadRequest(message="User Not Found"))
#         elif user == False:
#             return common_response(Unauthorized(message="Invalid/Expired Token for Change Password"))

#         return common_response(Ok(message="success menganti password anda"))
#     except Exception as e:
#         return common_response(BadRequest(message=str(e)))
    
@router.post(
    "/add-user",
    response_model=CudResponseSchema,
)
async def add_user_route(
        payload: SignUpRequest,
        db: AsyncSession = Depends(get_db),
        ):
    try:
        await  authRepo.sign_up(
            db=db,
            request=payload
            )
        return common_response(
            CudResponse(
                message="Success Sign Up",
            )
        )
    except Exception as e:
        import traceback

        traceback.print_exc()
        return common_response(BadRequest(message=str(e)))
    
@router.get(
    "/me",
    responses={
        "200": {"model": MeSuccessResponse},
        "400": {"model": BadRequestResponse},
        "401": {"model": UnauthorizedResponse},
        "500": {"model": InternalServerErrorResponse},
    },
)
async def me(
        request: Request,
        db: AsyncSession = Depends(get_db),
        token: str = Depends(oauth2_scheme)
        ):
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        old_token = token
        print(user)
        refresh_token = await generate_jwt_token_from_user(user=user)
        return common_response(
            Ok(
                data={
                    "id": str(user.id),
                    "email": user.email,
                    "name": user.name,
                    "isact": user.isact,
                    "phone": user.phone,
                    "refreshed_token": refresh_token,
                    "image": generate_link_download(user.photo),
                    "role": {
                        "id": user.roles[0].id if user.roles else None,
                        "name": user.roles[0].name if user.roles else None,
                    },
                    "address":user.address,
                    "photo": generate_link_download(user.photo),
                }
            )
        )
    except Exception as e:
        import traceback

        traceback.print_exc()
        return common_response(BadRequest(message=str(e)))
    
@router.get(
    "/permissions",
    responses={
        "200": {"model": PermissionsResponse},
        "401": {"model": UnauthorizedResponse},
        "500": {"model": InternalServerErrorResponse},
    },
)
async def permissions(
    request: Request,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        user_permissions = get_user_permissions(db=db, user=user)
        return common_response(
            Ok(
                data={
                    "results": [
                        {
                            "id": x.id,
                            "permission": x.name,
                            "module": {
                                "id": x.module.id,
                                "nama": x.module.name,
                            }
                            if x.module != None
                            else None,
                        }
                        for x in user_permissions
                    ]
                },
                message="Success get permisson"
            )
        )
    except Exception as e:
        return common_response(BadRequest(message=str(e)))
    
@router.get(
    "/menu",
    responses={
        "200": {"model": MenuResponse},
        "401": {"model": UnauthorizedResponse},
        "500": {"model": InternalServerErrorResponse},
    },
)
async def menu(db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)):
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())

        list_menu = await authRepo.generate_menu_tree_for_user(db=db, user=user)

        return common_response(Ok(data={"results": list_menu}))
    except Exception as e:
        import traceback

        traceback.print_exc()
        return common_response(BadRequest(message=str(e)))

@router.post(
    "/logout",
    responses={
        "201": {"model": CudResponseSchema},
        "401": {"model": UnauthorizedResponse},
        "500": {"model": InternalServerErrorResponse},
    },
)
async def logout_route(client_request: Request, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)):
    try:
        user = await get_user_from_jwt_token(db, token)
        # Dapatkan IP address client dengan cara yang benar
        client_ip = None
        # Cek header X-Forwarded-For untuk proxy/load balancer
        if "x-forwarded-for" in client_request.headers:
            client_ip = client_request.headers["x-forwarded-for"].split(",")[0].strip()
        # Cek header X-Real-IP
        elif "x-real-ip" in client_request.headers:
            client_ip = client_request.headers["x-real-ip"]
        # Fallback ke client.host
        elif client_request.client:
            client_ip = client_request.client.host
        else:
            client_ip = "unknown"
        if not user:
            # await set_log_otentikasi(
            #     db=db,
            #     username=user.email,
            #     action="logout",
            #     ip_address=client_ip,
            #     status='failed',
            # )
            return common_response(Ok(message="Successfully logged out."))
        await authRepo.logout_user(db=db, user=user, token=token)
        await set_log_otentikasi(
            db=db,
            username=user.email,
            action="logout",
            ip_address=client_ip,
            status='success',
        )
        return common_response(Ok(message="Successfully logged out."))
    except Exception as e:
        import traceback

        traceback.print_exc()
        return common_response(BadRequest(message=str(e)))

# @router.get("/mfa-config")
# async def get_mfa_config(
#     db: AsyncSession = Depends(get_db),
#     token: str = Depends(oauth2_scheme)
# ):
#     """Get konfigurasi MFA (untuk admin)"""
#     try:
#         user = await get_user_from_jwt_token(db, token)
#         if not user:
#             return common_response(Unauthorized())
        
#         max_attempts_result = await db.execute(
#             select(GlobalVariabel).where(
#                 GlobalVariabel.name == "mfa_max_attempts",
#                 GlobalVariabel.isact == True,
#             )
#         )
#         max_attempts = max_attempts_result.scalar()
        
#         suspend_duration_result = await db.execute(
#             select(GlobalVariabel).where(
#                 GlobalVariabel.name == "mfa_suspend_duration_minutes",
#                 GlobalVariabel.isact == True,
#             )
#         )
#         suspend_duration = suspend_duration_result.scalar()
        
#         config = {
#             "mfa_max_attempts": int(max_attempts.value) if max_attempts and max_attempts.value else 3,
#             "mfa_suspend_duration_minutes": int(suspend_duration.value) if suspend_duration and suspend_duration.value else 1
#         }
        
#         return common_response(
#             Ok(
#                 data=config,
#                 message="Konfigurasi MFA berhasil diambil"
#             )
#         )
#     except Exception as e:
#         traceback.print_exc()
#         return common_response(BadRequest(message=str(e)))

# @router.put("/mfa-config")
# async def update_mfa_config(
#     max_attempts: int = Form(3),
#     suspend_duration: int = Form(1),
#     db: AsyncSession = Depends(get_db),
#     token: str = Depends(oauth2_scheme)
# ):
#     """Update konfigurasi MFA (untuk admin)"""
#     try:
#         user = await get_user_from_jwt_token(db, token)
#         if not user:
#             return common_response(Unauthorized())
        
#         # Validasi input
#         if max_attempts < 1 or max_attempts > 10:
#             return common_response(BadRequest(message="Max attempts harus antara 1-10"))
        
#         if suspend_duration < 1 or suspend_duration > 60:
#             return common_response(BadRequest(message="Durasi suspend harus antara 1-60 menit"))
        
#         # Update max attempts
#         await db.execute(
#             update(GlobalVariabel)
#             .where(GlobalVariabel.name == "mfa_max_attempts")
#             .values(value=str(max_attempts))
#         )
        
#         # Update suspend duration
#         await db.execute(
#             update(GlobalVariabel)
#             .where(GlobalVariabel.name == "mfa_suspend_duration_minutes")
#             .values(value=str(suspend_duration))
#         )
        
#         await db.commit()
        
#         return common_response(
#             Ok(
#                 message=f"Konfigurasi MFA berhasil diupdate: max_attempts={max_attempts}, suspend_duration={suspend_duration} menit"
#             )
#         )
#     except Exception as e:
#         traceback.print_exc()
#         return common_response(BadRequest(message=str(e)))
    
# @router.get(
#     "/role-options",
#     responses={
#         "200": {"model": RoleOptionsResponse},
#         "401": {"model": UnauthorizedResponse},
#         "500": {"model": InternalServerErrorResponse},
#     },
# )
# async def role_options(
#     db: AsyncSession = Depends(get_db),
#     token: str = Depends(oauth2_scheme),
# ):
#     try:
#         current_user = await get_user_from_jwt_token(db, token)
#         if not current_user:
#             return common_response(Unauthorized())

#         role_options = await authRepo.get_role_options(db=db)
        
#         return common_response(
#             Ok(
#                 data={"results": role_options},
#                 message="Berhasil mendapatkan daftar role"
#             )
#         )
#     except Exception as e:
#         traceback.print_exc()
#         return common_response(BadRequest(message=str(e)))

# @router.get("/security-config")
# async def get_security_config(
#     db: AsyncSession = Depends(get_db),
#     token: str = Depends(oauth2_scheme)
# ):
#     """Get konfigurasi keamanan (MFA dan Login) untuk admin"""
#     try:
#         user = await get_user_from_jwt_token(db, token)
#         if not user:
#             return common_response(Unauthorized())
        
#         # Ambil konfigurasi MFA
#         mfa_max_attempts_result = await db.execute(
#             select(GlobalVariabel).where(
#                 GlobalVariabel.name == "mfa_max_attempts",
#                 GlobalVariabel.isact == True,
#             )
#         )
#         mfa_max_attempts = mfa_max_attempts_result.scalar()
        
#         mfa_suspend_duration_result = await db.execute(
#             select(GlobalVariabel).where(
#                 GlobalVariabel.name == "mfa_suspend_duration_minutes",
#                 GlobalVariabel.isact == True,
#             )
#         )
#         mfa_suspend_duration = mfa_suspend_duration_result.scalar()
        
#         # Ambil konfigurasi Login
#         login_max_attempts_result = await db.execute(
#             select(GlobalVariabel).where(
#                 GlobalVariabel.name == "login_max_attempts",
#                 GlobalVariabel.isact == True,
#             )
#         )
#         login_max_attempts = login_max_attempts_result.scalar()
        
#         login_suspend_duration_result = await db.execute(
#             select(GlobalVariabel).where(
#                 GlobalVariabel.name == "login_suspend_duration_minutes",
#                 GlobalVariabel.isact == True,
#             )
#         )
#         login_suspend_duration = login_suspend_duration_result.scalar()
        
#         config = {
#             "mfa": {
#                 "max_attempts": int(mfa_max_attempts.value) if mfa_max_attempts and mfa_max_attempts.value else 3,
#                 "suspend_duration_minutes": int(mfa_suspend_duration.value) if mfa_suspend_duration and mfa_suspend_duration.value else 1
#             },
#             "login": {
#                 "max_attempts": int(login_max_attempts.value) if login_max_attempts and login_max_attempts.value else 5,
#                 "suspend_duration_minutes": int(login_suspend_duration.value) if login_suspend_duration and login_suspend_duration.value else 5
#             }
#         }
        
#         return common_response(
#             Ok(
#                 data=config,
#                 message="Konfigurasi keamanan berhasil diambil"
#             )
#         )
#     except Exception as e:
#         traceback.print_exc()
#         return common_response(BadRequest(message=str(e)))

# @router.put("/security-config")
# async def update_security_config(
#     mfa_max_attempts: int = Form(3),
#     mfa_suspend_duration: int = Form(1),
#     login_max_attempts: int = Form(5),
#     login_suspend_duration: int = Form(5),
#     db: AsyncSession = Depends(get_db),
#     token: str = Depends(oauth2_scheme)
# ):
#     """Update konfigurasi keamanan (MFA dan Login) untuk admin"""
#     try:
#         user = await get_user_from_jwt_token(db, token)
#         if not user:
#             return common_response(Unauthorized())
        
#         # Validasi input MFA
#         if mfa_max_attempts < 1 or mfa_max_attempts > 10:
#             return common_response(BadRequest(message="MFA max attempts harus antara 1-10"))
        
#         if mfa_suspend_duration < 1 or mfa_suspend_duration > 60:
#             return common_response(BadRequest(message="MFA durasi suspend harus antara 1-60 menit"))
        
#         # Validasi input Login
#         if login_max_attempts < 1 or login_max_attempts > 10:
#             return common_response(BadRequest(message="Login max attempts harus antara 1-10"))
        
#         if login_suspend_duration < 1 or login_suspend_duration > 60:
#             return common_response(BadRequest(message="Login durasi suspend harus antara 1-60 menit"))
        
#         # Update MFA config
#         await db.execute(
#             update(GlobalVariabel)
#             .where(GlobalVariabel.name == "mfa_max_attempts")
#             .values(value=str(mfa_max_attempts))
#         )
        
#         await db.execute(
#             update(GlobalVariabel)
#             .where(GlobalVariabel.name == "mfa_suspend_duration_minutes")
#             .values(value=str(mfa_suspend_duration))
#         )
        
#         # Update Login config
#         await db.execute(
#             update(GlobalVariabel)
#             .where(GlobalVariabel.name == "login_max_attempts")
#             .values(value=str(login_max_attempts))
#         )
        
#         await db.execute(
#             update(GlobalVariabel)
#             .where(GlobalVariabel.name == "login_suspend_duration_minutes")
#             .values(value=str(login_suspend_duration))
#         )
        
#         await db.commit()
        
#         return common_response(
#             Ok(
#                 message=f"Konfigurasi keamanan berhasil diupdate: MFA({mfa_max_attempts}/{mfa_suspend_duration}m), Login({login_max_attempts}/{login_suspend_duration}m)"
#             )
#         )
#     except Exception as e:
#         traceback.print_exc()
#         return common_response(BadRequest(message=str(e)))