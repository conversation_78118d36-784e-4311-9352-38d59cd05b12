import unittest
from unittest.mock import AsyncMock, MagicMock, patch
import json
from sqlalchemy.ext.asyncio import AsyncSession
from models.Notifications import Notifications
from models.User import User
from routes.notification import (
    notif_total_route,
    ls_notif_route,
    read_notification,
)

class TestNOTIFIKASI(unittest.IsolatedAsyncioTestCase):
    async def test_notif_total_route_success(self):
        """Test successful retrieval of total unread notifications"""
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_token = "valid_token"
        
        # Mock user
        mock_user = User(
            id="1", 
            name="testuser", 
            email="<EMAIL>",
            created_by="test",
            updated_by="test", 
            phone="123456789",
            address="test address",
            password="hashedpass",
            first_login="0",
            birth_date="1990-01-01"
        )
        
        # Mock query result for counting unread notifications
        mock_count_result = MagicMock()
        mock_count_result.scalar = MagicMock(return_value=5)
        mock_db.execute = AsyncMock(return_value=mock_count_result)
        
        # Mock get_user_from_jwt_token
        with patch('routes.notification.get_user_from_jwt_token', return_value=mock_user):
            result = await notif_total_route(db=mock_db, token=mock_token)
        
        # Assertions
        self.assertEqual(result.status_code, 200)
        response_data = result.__dict__["body"].decode()
        import json
        response_json = json.loads(response_data)
        self.assertEqual(response_json["code"], 200)
        self.assertEqual(response_json["data"]["total"], 5)
        mock_db.execute.assert_called_once()

    async def test_notif_total_route_unauthorized(self):
        """Test unauthorized access when user is not found"""
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_token = "invalid_token"
        
        # Mock get_user_from_jwt_token returning None
        with patch('routes.notification.get_user_from_jwt_token', return_value=None):
            result = await notif_total_route(db=mock_db, token=mock_token)
        
        # Assertions
        self.assertEqual(result.status_code, 401)
        response_data = result.__dict__["body"].decode()
        import json
        response_json = json.loads(response_data)
        self.assertEqual(response_json["message"], "Unauthorized")

    async def test_notif_total_route_exception(self):
        """Test internal server error when exception occurs"""
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_token = "valid_token"
        
        # Mock get_user_from_jwt_token to raise exception
        with patch('routes.notification.get_user_from_jwt_token', side_effect=Exception("Database error")):
            result = await notif_total_route(db=mock_db, token=mock_token)
        
        # Assertions
        self.assertEqual(result.status_code, 500)
        response_data = result.__dict__["body"].decode()
        import json
        response_json = json.loads(response_data)
        self.assertEqual(response_json["message"], "Internal Error")

    async def test_ls_notif_route_success(self):
        """Test successful retrieval of notification list"""
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_token = "valid_token"
        
        # Mock user
        mock_user = User(
            id="1", 
            name="testuser", 
            email="<EMAIL>",
            created_by="test",
            updated_by="test", 
            phone="123456789",
            address="test address",
            password="hashedpass",
            first_login="0",
            birth_date="1990-01-01"
        )
        
        # Mock notification data
        from datetime import datetime
        mock_notif_row1 = MagicMock()
        mock_notif_row1.id = 1
        mock_notif_row1.title = "Test Notification 1"
        mock_notif_row1.message = "Test message 1"
        mock_notif_row1.is_read = False
        mock_notif_row1.created_at = datetime.now()
        
        mock_notif_row2 = MagicMock()
        mock_notif_row2.id = 2
        mock_notif_row2.title = "Test Notification 2"
        mock_notif_row2.message = "Test message 2"
        mock_notif_row2.is_read = False
        mock_notif_row2.created_at = datetime.now()
        
        # Mock query result
        mock_query_result = MagicMock()
        mock_query_result.fetchall = MagicMock(return_value=[mock_notif_row1, mock_notif_row2])
        mock_db.execute = AsyncMock(return_value=mock_query_result)
        
        # Mock get_user_from_jwt_token
        with patch('routes.notification.get_user_from_jwt_token', return_value=mock_user):
            result = await ls_notif_route(db=mock_db, token=mock_token, page=1, page_size=10)
        
        # Assertions
        self.assertEqual(result.status_code, 200)
        response_data = result.__dict__["body"].decode()
        import json
        response_json = json.loads(response_data)
        self.assertEqual(response_json["code"], 200)
        self.assertEqual(len(response_json["data"]), 2)
        self.assertEqual(response_json["data"][0]["id"], 1)
        self.assertEqual(response_json["data"][0]["title"], "Test Notification 1")
        self.assertEqual(response_json["meta"]["count"], 2)
        self.assertEqual(response_json["meta"]["page"], 1)
        mock_db.execute.assert_called_once()

    async def test_ls_notif_route_unauthorized(self):
        """Test unauthorized access for notification list"""
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_token = "invalid_token"
        
        # Mock get_user_from_jwt_token returning None
        with patch('routes.notification.get_user_from_jwt_token', return_value=None):
            result = await ls_notif_route(db=mock_db, token=mock_token)
        
        # Assertions
        self.assertEqual(result.status_code, 401)
        response_data = result.__dict__["body"].decode()
        import json
        response_json = json.loads(response_data)
        self.assertEqual(response_json["message"], "Unauthorized")

    async def test_ls_notif_route_with_pagination(self):
        """Test notification list with custom pagination parameters"""
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_token = "valid_token"
        
        # Mock user
        mock_user = User(
            id="1", 
            name="testuser", 
            email="<EMAIL>",
            created_by="test",
            updated_by="test", 
            phone="123456789",
            address="test address",
            password="hashedpass",
            first_login="0",
            birth_date="1990-01-01"
        )
        
        # Mock empty result for page 2
        mock_query_result = MagicMock()
        mock_query_result.fetchall = MagicMock(return_value=[])
        mock_db.execute = AsyncMock(return_value=mock_query_result)
        
        # Mock get_user_from_jwt_token
        with patch('routes.notification.get_user_from_jwt_token', return_value=mock_user):
            result = await ls_notif_route(db=mock_db, token=mock_token, page=2, page_size=5)
        
        # Assertions
        self.assertEqual(result.status_code, 200)
        response_data = result.__dict__["body"].decode()
        import json
        response_json = json.loads(response_data)
        self.assertEqual(response_json["code"], 200)
        self.assertEqual(len(response_json["data"]), 0)
        self.assertEqual(response_json["meta"]["page"], 2)
        self.assertEqual(response_json["meta"]["page_size"], 10)  # Default page_size in response

    async def test_ls_notif_route_exception(self):
        """Test internal server error for notification list"""
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_token = "valid_token"
        
        # Mock get_user_from_jwt_token to raise exception
        with patch('routes.notification.get_user_from_jwt_token', side_effect=Exception("Database error")):
            result = await ls_notif_route(db=mock_db, token=mock_token)
        
        # Assertions
        self.assertEqual(result.status_code, 500)
        response_data = result.__dict__["body"].decode()
        import json
        response_json = json.loads(response_data)
        self.assertEqual(response_json["message"], "Internal Error")

    async def test_read_notification_success(self):
        """Test successful marking notification as read"""
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_token = "valid_token"
        notification_id = 1
        
        # Mock user
        mock_user = User(
            id="1", 
            name="testuser", 
            email="<EMAIL>",
            created_by="test",
            updated_by="test", 
            phone="123456789",
            address="test address",
            password="hashedpass",
            first_login="0",
            birth_date="1990-01-01"
        )
        
        # Mock notification
        mock_notification = Notifications(
            id=1,
            title="Test Notification",
            message="Test message",
            insight="Test insight",
            is_read=False,
            is_active=True
        )
        
        # Mock query result
        mock_query_result = MagicMock()
        mock_query_result.scalar_one_or_none = MagicMock(return_value=mock_notification)
        mock_db.execute = AsyncMock(return_value=mock_query_result)
        mock_db.commit = AsyncMock()
        mock_db.refresh = AsyncMock()
        
        # Mock get_user_from_jwt_token
        with patch('routes.notification.get_user_from_jwt_token', return_value=mock_user):
            result = await read_notification(id=notification_id, db=mock_db, token=mock_token)
        
        # Assertions
        self.assertEqual(result.status_code, 201)
        response_data = result.__dict__["body"].decode()
        import json
        response_json = json.loads(response_data)
        self.assertEqual(response_json["code"], 201)
        self.assertEqual(response_json["message"], "Notification marked as read successfully")
        self.assertTrue(mock_notification.is_read)
        mock_db.execute.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once_with(mock_notification)

    async def test_read_notification_unauthorized(self):
        """Test unauthorized access for read notification"""
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_token = "invalid_token"
        notification_id = 1
        
        # Mock get_user_from_jwt_token returning None
        with patch('routes.notification.get_user_from_jwt_token', return_value=None):
            result = await read_notification(id=notification_id, db=mock_db, token=mock_token)
        
        # Assertions
        self.assertEqual(result.status_code, 401)
        response_data = result.__dict__["body"].decode()
        import json
        response_json = json.loads(response_data)
        self.assertEqual(response_json["message"], "Unauthorized")

    async def test_read_notification_not_found(self):
        """Test read notification when notification doesn't exist"""
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_token = "valid_token"
        notification_id = 999
        
        # Mock user
        mock_user = User(
            id="1", 
            name="testuser", 
            email="<EMAIL>",
            created_by="test",
            updated_by="test", 
            phone="123456789",
            address="test address",
            password="hashedpass",
            first_login="0",
            birth_date="1990-01-01"
        )
        
        # Mock query result returning None
        mock_query_result = MagicMock()
        mock_query_result.scalar_one_or_none = MagicMock(return_value=None)
        mock_db.execute = AsyncMock(return_value=mock_query_result)
        
        # Mock get_user_from_jwt_token
        with patch('routes.notification.get_user_from_jwt_token', return_value=mock_user):
            result = await read_notification(id=notification_id, db=mock_db, token=mock_token)
        
        # Assertions
        self.assertEqual(result.status_code, 404)
        response_data = result.__dict__["body"].decode()
        import json
        response_json = json.loads(response_data)
        self.assertEqual(response_json["message"], "Not Found")
        mock_db.execute.assert_called_once()

    async def test_read_notification_exception(self):
        """Test internal server error for read notification"""
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_token = "valid_token"
        notification_id = 1
        
        # Mock get_user_from_jwt_token to raise exception
        with patch('routes.notification.get_user_from_jwt_token', side_effect=Exception("Database error")):
            result = await read_notification(id=notification_id, db=mock_db, token=mock_token)
        
        # Assertions
        self.assertEqual(result.status_code, 500)
        response_data = result.__dict__["body"].decode()
        import json
        response_json = json.loads(response_data)
        self.assertEqual(response_json["message"], "Internal Error")

if __name__ == '__main__':
    unittest.main()
