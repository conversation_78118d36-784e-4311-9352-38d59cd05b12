import httpx
import traceback
from typing import Op<PERSON>, Dict, Any
import asyncio
from settings import THIRD_PARTY_API_BASE_URL, THIRD_PARTY_API_USERNAME, THIRD_PARTY_API_PASSWORD, TZ
from settings import BIGSOCIAL_API_URL, B<PERSON>SOCIAL_AUTH, FILE_STORAGE_ADAPTER, SERVICE_NAME
from models import async_session
from models.Notifications import Notifications
from datetime import datetime, timedelta, date
from pytz import timezone
from models import sync_session
from models.SummaryInsight import SummaryInsight
from sqlalchemy import select
from core.pdf_generator import MarkdownToPDF
from core.file import upload_file
import base64
from fastapi import UploadFile
from io import BytesIO
from services.rabbitmq_producer import RabbitMQProducer
from core.logging_config import logger
from core.file import save_uploaded_file_to_db
import re

# Constants
DEFAULT_INSIGHT_MESSAGE = "Tidak dapat mendapatkan insight saat ini."
MONTH_YEAR_FORMAT = "%B %Y"

async def run_scheduled_task(func_name: str):
    if func_name == "get_notif_data":
        await get_notif_data()
    elif func_name == "get_bigsocial_crawl_and_save_pdf":
        await get_bigsocial_crawl_and_save_pdf()
    else:
        raise ValueError(f"Unknown function name: {func_name}")
    
def run_scheduled_task_1(func_name: str):
    if func_name == "get_notif_data":
        get_notif_data_1()
    elif func_name == "get_data_from_tabel":
        return get_data_from_tabel()
    elif func_name == "get_bigsocial_crawl_and_save_pdf":
        asyncio.run(get_bigsocial_crawl_and_save_pdf())
    else:
        raise ValueError(f"Unknown function name: {func_name}")
    

def get_data_from_tabel():
    try:       
        now = datetime.now()
        current_month_num = 1
        if now.month == 1:
            current_month_num = 12
            current_year = str(now.year - 1)
        else:
            current_month_num = now.month - 1
            current_year = str(now.year)
            
        current_month_name = datetime(1900, current_month_num, 1).strftime("%B")
        current_month_name = datetime.now().strftime("%B")
        current_year = str(datetime.now().year)
        
        with sync_session() as session:
            # Query untuk mengambil data dari SummaryInsight berdasarkan bulan dan tahun
            query = select(SummaryInsight).filter(
                SummaryInsight.month == str(f'0{current_month_num}' if current_month_num < 10 else current_month_num),
                SummaryInsight.year == current_year,
                SummaryInsight.isdel == False
            )
            
            result = session.execute(query)
            summary_insights = result.scalars().all()
            
            if not summary_insights:
                print(f"Tidak ada data ditemukan untuk bulan {current_month_name} {current_year}")
                return {
                    "month": current_month_name,
                    "year": current_year,
                    "data": [],
                    "message": f"Tidak ada data ditemukan untuk bulan {current_month_name} {current_year}"
                }
            
            from models.User import User
            users = session.execute(select(User).where(User.isact == True)).scalars().all()

            notifications_to_add = []
            notifications_to_update = []
            
            for insight in summary_insights:
                summary_text = insight.summary_text or ""

                # 1) Jenis perubahan
                if re.search(r"penurunan", summary_text, flags=re.IGNORECASE):
                    change_type = "penurunan"
                elif re.search(r"peningkatan", summary_text, flags=re.IGNORECASE):
                    change_type = "peningkatan"
                else:
                    change_type = "perubahan"

                # 2) Persentase perubahan (mendukung koma/desimal)
                perc_match = re.search(r"(?:sebesar\s*)?(\d+(?:[\.,]\d+)?)%", summary_text, flags=re.IGNORECASE)
                percentage = f"{perc_match.group(1)}%" if perc_match else "0%"

                # 3) Bulan dan tahun (Indonesia/English)
                month_regex = r"(Januari|January|Februari|February|Maret|March|April|Mei|May|Juni|June|Juli|July|Agustus|August|September|Oktober|October|November|Desember|December)\s+(\d{4})"
                month_year_match = re.search(month_regex, summary_text, flags=re.IGNORECASE)
                if month_year_match:
                    month_name = month_year_match.group(1)
                    year_val = month_year_match.group(2)
                    # Normalisasi bulan ke format English Capitalized untuk konsistensi
                    month_map = {
                        'januari':'January','februari':'February','maret':'March','april':'April','mei':'May','juni':'June','juli':'July','agustus':'August','september':'September','oktober':'October','november':'November','desember':'December'
                    }
                    month_lower = month_name.lower()
                    month_name = month_map.get(month_lower, month_name.capitalize())
                else:
                    # Fallback ke data insight/month sebelumnya
                    try:
                        month_num = int(getattr(insight, 'month', 0))
                        month_name = datetime(1900, month_num, 1).strftime("%B") if month_num else current_month_name
                    except (ValueError, TypeError):
                        month_name = current_month_name
                    year_val = current_year

                # 4) Nominal total perubahan revenue
                amount_match = re.search(r"Total\s+perubahan\s+revenue\s+adalah\s+Rp\s*([\d\.,]+)", summary_text, flags=re.IGNORECASE)
                amount_val = amount_match.group(1) if amount_match else None

                # 5) Informasi produk & layanan
                service = getattr(insight, 'service_type', None) or ""
                product = getattr(insight, 'product_type', None).replace("_", " ") if getattr(insight, 'product_type', None) else ""

                parts = []
                parts.append(f"Terjadi {change_type} revenue sebesar {percentage}")
                if product or service:
                    if product and service:
                        parts.append(f"pada service {service} ({product})")
                    elif product:
                        parts.append(f"pada produk {product}")
                    else:
                        parts.append(f"pada service {service}")
                parts.append(f"pada bulan {month_name} {year_val}.")
                if amount_val:
                    parts.append(f"Total perubahan revenue adalah Rp{amount_val}.")
                title = " ".join(parts)

                for user in users:
                    query = select(Notifications).filter(
                        Notifications.title == title,
                        Notifications.user_id == user.id,
                        Notifications.is_active == True
                    )
                    notification = session.execute(query).scalar()
                    
                    if notification:
                        # Update existing notification
                        notification.message = insight.summary_text
                        notification.insight = getattr(insight, 'insight', None) or DEFAULT_INSIGHT_MESSAGE
                        notification.updated_at = datetime.now(timezone(TZ))
                        notifications_to_update.append(notification)
                    else:
                        # Create new notification per user
                        new_notification = Notifications(
                            title=title,
                            user_id=user.id,
                            message=insight.summary_text,
                            created_at=datetime.now(timezone(TZ)),
                            insight=getattr(insight, 'insight', None) or DEFAULT_INSIGHT_MESSAGE,
                            is_read=False,
                            is_active=True
                        )
                        notifications_to_add.append(new_notification)

            # Batch add new notifications
            if notifications_to_add:
                session.add_all(notifications_to_add)
            
            # Commit all changes at once
            session.commit()

            total_processed = len(notifications_to_add) + len(notifications_to_update)
            print(f"Berhasil memproses {total_processed} notifikasi (baru: {len(notifications_to_add)}, update: {len(notifications_to_update)}) untuk {len(summary_insights)} data bulan {current_month_name} {current_year}")
            
            return {
                "success": True,
                "month": current_month_name,
                "year": current_year,
                "total_insights": len(summary_insights),
                "new_notifications": len(notifications_to_add),
                "updated_notifications": len(notifications_to_update)
            }

    except Exception as e:
        print(f"Error dalam get_data_from_tabel: {e}")
        traceback.print_exc()
        return {
            "error": True,
            "message": f"Failed to get data from tabel: {str(e)}"
        }


def get_notif_data_1() -> Dict[str, Any]:
    """
    Fungsi untuk melakukan request ke endpoint pihak ketiga dengan autentikasi terlebih dahulu.
    Akan login untuk mendapatkan token, kemudian mengambil data dari endpoint /astinet/summary-permonth.
    
        Returns:
        Dict berisi summary_text dan insight dari analisis data Astinet
    """
    try:
        data_url = f"{THIRD_PARTY_API_BASE_URL}/scheduler/insight"
        
        
        with httpx.Client(timeout=80.0) as client:
            headers = {
                # "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json",
                "User-Agent": "Telkom-AI-Dashboard/1.0"
            }
            
            # Data untuk request body (sesuaikan dengan kebutuhan API)
            # Generate tanggal 1 bulan sebelumnya dengan format yyyy-mm-dd
            # current_date = datetime(2025, 1, 1)
            current_date = datetime.now()
            if current_date.month == 1:
                # Jika bulan sekarang Januari, bulan sebelumnya adalah Desember tahun lalu
                previous_month = 12
                year = current_date.year - 1
            else:
                previous_month = current_date.month - 1
                year = current_date.year
            
            previous_month_date = f"{year}-{previous_month:02d}-01"

            
            data_response = client.post(
                data_url,
                headers=headers,
            )
            
            if data_response.status_code != 200:
                raise Exception(f"Gagal mengambil data. Status code: {data_response.status_code}, Response: {data_response.text}")
            
            astinet_data = data_response.json()
            print("Data berhasil diambil dari endpoint")
            
            # Step 3: Generate summary_text dan insight berdasarkan data yang diperoleh
            summary_text = astinet_data['data']['summary_text']
            insight = astinet_data['data']['insight']
            print("Summary Text:", summary_text)
            
            # buat dan isi variabel current month dengan bulan ini 12 septembt 2025, bulanya dinamis contoh hanya format
            current_month = datetime.now().strftime(MONTH_YEAR_FORMAT)
            
            with sync_session() as session:
                # Query untuk mencari notification yang sudah ada
                query = select(Notifications).filter(
                    Notifications.title == current_month,
                    Notifications.is_active == True
                )
                query_result = session.execute(query)
                notification = query_result.scalar()
                
                if notification:
                    # Update existing notification
                    notification.message = summary_text
                    notification.insight = insight
                    notification.updated_at = datetime.now(timezone(TZ))
                else:
                    # Create new notification
                    notification = Notifications(
                        title=current_month,
                        message=summary_text,
                        created_at=datetime.now(timezone(TZ)),
                        insight=insight if insight else DEFAULT_INSIGHT_MESSAGE,
                        is_read=False,
                        is_active=True
                    )
                session.add(notification)
                session.commit()
                
            return {
                "title": current_month,
                "insight": insight if insight else DEFAULT_INSIGHT_MESSAGE
            }
            
    except httpx.TimeoutException:
        print("Timeout saat melakukan request ke API")
        current_month = datetime.now().strftime(MONTH_YEAR_FORMAT)
        return {
            "title": current_month,
            "insight": DEFAULT_INSIGHT_MESSAGE
        }
    except httpx.RequestError as e:
        print(f"Error dalam request: {e}")
        traceback.print_exc()
        current_month = datetime.now().strftime(MONTH_YEAR_FORMAT)
        return {
            "title": current_month,
            "insight": DEFAULT_INSIGHT_MESSAGE
        }
    except Exception as e:
        print(f"Error dalam get_notif_data: {e}")
        traceback.print_exc()
        current_month = datetime.now().strftime(MONTH_YEAR_FORMAT)
        return {
            "title": current_month,
            "insight": DEFAULT_INSIGHT_MESSAGE
        }

async def get_notif_data() -> Dict[str, Any]:
    """
    Fungsi untuk melakukan request ke endpoint pihak ketiga dengan autentikasi terlebih dahulu.
    Akan login untuk mendapatkan token, kemudian mengambil data dari endpoint /astinet/summary-permonth.
    
        Returns:
        Dict berisi summary_text dan insight dari analisis data Astinet
    """
    try:
        data_url = f"{THIRD_PARTY_API_BASE_URL}/scheduler/insight"

        
        async with httpx.AsyncClient(timeout=30.0) as client:
            
            print("Mengambil data dari endpoint /astinet/summary-permonth...")
            headers = {
                # "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json",
                "User-Agent": "Telkom-AI-Dashboard/1.0"
            }
            
            
            data_response = await client.post(
                data_url,
                headers=headers,
            )
            
            if data_response.status_code != 200:
                raise Exception(f"Gagal mengambil data. Status code: {data_response.status_code}, Response: {data_response.text}")
            
            astinet_data = data_response.json()
            print("Data berhasil diambil dari endpoint")
            
            # Step 3: Generate summary_text dan insight berdasarkan data yang diperoleh
            summary_text = astinet_data['data']['summary_text']
            insight = astinet_data['data']['insight']
            current_month = datetime.now().strftime(MONTH_YEAR_FORMAT)  # Contoh: "September 2025"
            async with async_session() as session:
                from sqlalchemy import select
                # Query untuk mencari notification yang sudah ada
                query = select(Notifications).filter(
                    Notifications.title == current_month,
                    Notifications.is_active == True
                )
                result = await session.execute(query)
                notification = result.scalar()
                
                if notification:
                    # Update existing notification
                    notification.message = insight
                else:
                    # Create new notification
                    notification = Notifications(
                        title=current_month,
                        message=summary_text,
                        created_at=datetime.now(),
                        insight=insight,
                        is_read=False,
                        is_active=True
                    )
                    session.add(notification)
                await session.commit()
            return {
                "summary_text": summary_text,
                "insight": insight if insight else DEFAULT_INSIGHT_MESSAGE
            }
            
    except httpx.TimeoutException:
        print("Timeout saat melakukan request ke API")
        return {
            "summary_text": "Gagal mengambil data karena timeout",
            "insight": DEFAULT_INSIGHT_MESSAGE
        }
    except httpx.RequestError as e:
        print(f"Error dalam request: {e}")
        traceback.print_exc()
        return {
            "summary_text": "Gagal mengambil data karena error koneksi",
            "insight": DEFAULT_INSIGHT_MESSAGE
        }
    except Exception as e:
        print(f"Error dalam get_notif_data: {e}")
        traceback.print_exc()
        return {
            "summary_text": "Gagal mengambil data dari sistem eksternal",
            "insight": DEFAULT_INSIGHT_MESSAGE
        }

# Initialize RabbitMQ producer for BigSocial crawling
rabbitmq_producer = None
try:
    from settings import RABBITMQ_URL
    if RABBITMQ_URL:
        rabbitmq_producer = RabbitMQProducer(
            rabbitmq_url=RABBITMQ_URL,
            queue_name=f"file_events_{SERVICE_NAME}"  # file_events_be-dcs-dashboard
        )
        logger.info("RabbitMQ producer initialized successfully for BigSocial crawling")
    else:
        logger.warning("RABBITMQ_URL not configured, RabbitMQ producer not initialized")
except Exception as e:
    logger.error(f"Failed to initialize RabbitMQ producer: {e}")
    rabbitmq_producer = None

async def get_bigsocial_crawl_and_save_pdf():
    """
    Scheduler untuk crawling data dari BigSocial, generate PDF, dan simpan ke file (local/minio)
    PDF hanya berisi data label dan spell dari semua keyword, hasil crawling tetap satu per keyword tapi digabung ke satu PDF.
    """
    
    def sanitize_filename(text: str) -> str:
        """Membuat nama file yang aman untuk filesystem"""
        invalid_chars = ['<', '>', ':', '"', '|', '?', '*', ' ', '/', '\\']
        result = text
        for char in invalid_chars:
            if char == ' ':
                result = result.replace(char, '_')
            elif char == ':':
                result = result.replace(char, '-')
            else:
                result = result.replace(char, '_')
        return result
    
    try:
        # 1. Generate tanggal untuk bulan sebelumnya
        today = date.today()
        first_day_of_current_month = today.replace(day=1)
        last_day_of_previous_month = first_day_of_current_month - timedelta(days=1)
        first_day_of_previous_month = last_day_of_previous_month.replace(day=1)

        start_date = f"{first_day_of_previous_month.strftime('%Y-%m-%d')} 00:00:00"
        end_date = f"{last_day_of_previous_month.strftime('%Y-%m-%d')} 23:59:59"
        time_now = datetime.now(timezone(TZ)).strftime('%H-%M-%S')
        # 2. Ambil keywords dari database
        keywords_list = []  
        
        try:
            async with async_session() as session:
                from models.Keywords import Keywords
                result = await session.execute(select(Keywords.keyword).where(Keywords.isact == True))
                db_keywords = result.scalars().all()
                
                if db_keywords:
                    keywords_list = db_keywords
                else:
                    logger.warning("Tidak ada keywords aktif ditemukan di database, menggunakan keywords default")
                    keywords_list = [
                        "astinet",  
                        "layanan astinet",
                    ]
        except Exception as e:
            logger.warning(f"Error mengambil keywords dari database: {e}, menggunakan keywords default")
            keywords_list = [
                "astinet", 
                "layanan astinet",
            ]

        # 3. Setup BigSocial API configuration
        url = BIGSOCIAL_API_URL
        headers = {
            'Content-Type': 'application/json',
            'Authorization': BIGSOCIAL_AUTH
        }
        
        print(f"\n🌐 BigSocial API Configuration:")
        print(f"🔗 URL: {url}")
        print(f"🔑 Auth: {BIGSOCIAL_AUTH[:20]}..." if BIGSOCIAL_AUTH and len(BIGSOCIAL_AUTH) > 20 else f"🔑 Auth: {BIGSOCIAL_AUTH}")
        print(f"📅 Start Date: {start_date}")
        print(f"📅 End Date: {end_date}")
        print(f"⏰ Time Now: {time_now}")
        print(f"📊 Total Keywords: {len(keywords_list)}")
        print(f"🔑 Keywords: {keywords_list}")

        # 4. Crawling data untuk setiap keyword
        all_docs = []
        async with httpx.AsyncClient(timeout=60.0) as client:
            for keyword in keywords_list:
                payload = {
                    "keyword": [keyword],  
                    "start_date": start_date,
                    "end_date": end_date
                }
                try:
                    print(f"\n🔍 Mencoba crawling keyword: '{keyword}'")
                    print(f"📅 Periode: {start_date} s/d {end_date}")
                    print(f"📤 Payload: {payload}")
                    
                    resp = await client.post(url, headers=headers, json=payload)
                    resp.raise_for_status()
                    data = resp.json()
                    
                    print(f"📥 Response status: {resp.status_code}")
                    print(f"📊 Response data structure: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                    
                    # Debug response structure
                    if 'data' in data:
                        print(f"📋 Data keys: {list(data['data'].keys()) if isinstance(data['data'], dict) else 'Data not a dict'}")
                        if 'response' in data['data']:
                            print(f"📡 Response keys: {list(data['data']['response'].keys()) if isinstance(data['data']['response'], dict) else 'Response not a dict'}")
                    
                    docs = data.get('data', {}).get('response', {}).get('docs', [])
                    
                    # Debug docs
                    print(f"📄 Docs type: {type(docs)}")
                    print(f"📄 Docs length: {len(docs) if docs else 0}")
                    if docs and len(docs) > 0:
                        print(f"📄 First doc sample: {docs[0]}")
                    
                    # Simpan docs beserta keyword-nya
                    all_docs.append({"keyword": keyword, "docs": docs})
                    print(f"✅ Crawling BigSocial berhasil untuk keyword '{keyword}'")
                    print(f"📊 Jumlah docs: {len(docs)}")
                    
                except Exception as e:
                    logger.error(f"❌ Gagal crawling BigSocial untuk keyword '{keyword}': {e}")
                    print(f"❌ Error detail: {e}")
                    traceback.print_exc()

        # Gabungkan semua hasil ke satu markdown
        print(f"\n📋 SUMMARY HASIL CRAWLING:")
        print(f"📊 Total keywords diproses: {len(keywords_list)}")
        print(f"📄 Total docs ditemukan: {sum(len(entry['docs']) for entry in all_docs)}")
        
        for entry in all_docs:
            keyword = entry["keyword"]
            docs = entry["docs"]
            print(f"  • {keyword}: {len(docs)} docs")
        
        markdown = f"""# Hasil Crawling BigSocial\n\n**Keyword:** {', '.join(keywords_list)}\n**Periode:** {start_date} s/d {end_date}\n\n"""
        idx = 1
        for entry in all_docs:
            keyword = entry["keyword"]
            docs = entry["docs"]
            markdown += f"# Keyword: {keyword}\n\n"
            for doc in docs:
                label = doc.get('label', '-')
                spell = doc.get('spell', [])
                markdown += f"## {idx}. {label}\n"
                if isinstance(spell, list):
                    for s in spell:
                        markdown += f"{s}\n\n"
                else:
                    markdown += f"{spell}\n\n"
                idx += 1
        
        print(f"📝 Markdown length: {len(markdown)} characters")
        print(f"📝 Markdown preview: {markdown[:200]}...")

        # Generate PDF (base64)
        print(f"\n🔄 Generating PDF...")
        pdf_base64 = MarkdownToPDF().generate_pdf(f"Crawling BigSocial {start_date} - {end_date} - {time_now}", markdown)
        pdf_bytes = base64.b64decode(pdf_base64)
        
        print(f"✅ PDF generated successfully")
        print(f"📊 PDF size: {len(pdf_bytes)} bytes")

        # Simpan PDF menggunakan upload_file
        safe_start_date = sanitize_filename(start_date)
        safe_end_date = sanitize_filename(end_date)
        filename = f"bigsocial_crawl_{safe_start_date}_to_{safe_end_date}_{time_now}.pdf"

        dest_path = f"{filename}"
        file_path = f"{filename}"

        pdf_file_obj = BytesIO(pdf_bytes)
        pdf_upload_file = UploadFile(filename=filename, file=pdf_file_obj)

        # Upload file ke storage (local/minio/obs)
        uploaded_path = await upload_file(upload_file=pdf_upload_file, path=dest_path)
        print(f"PDF hasil crawling berhasil disimpan: {uploaded_path}")
        logger.info(f"PDF hasil crawling berhasil disimpan: {uploaded_path}")
        file_id = save_uploaded_file_to_db(
                    directory="documents",
                    filename= filename,
                    filesize=len(pdf_bytes),
                    filetype="application/pdf",
                    user_id=None,
                    status="processing",
                    service="external_crawling"
                )
        if rabbitmq_producer:
            try:
                # Metadata untuk event
                metadata = {
                    "file_id": file_id,
                    "filename": filename,
                    "file_path": file_path,
                    "doc_type": "web_crawling_insight",
                    "content_type": "application/pdf",
                    "size": len(pdf_bytes),
                    "source": "bigsocial_crawler",
                    "keywords": keywords_list,
                    "start_date": start_date,
                    "end_date": end_date,
                    "storage_adapter": FILE_STORAGE_ADAPTER,
                    "timestamp": str(datetime.now()),
                    "total_docs": sum(len(entry["docs"]) for entry in all_docs)
                }

                # Publish event file created
                success = rabbitmq_producer.publish_file_created(
                    file_path=file_path,
                    metadata=metadata
                )

                if success:
                    logger.info(f"Event file_created berhasil dikirim ke RabbitMQ untuk: {file_path}")
                else:
                    logger.error(f"Gagal mengirim event file_created ke RabbitMQ untuk: {file_path}")

            except Exception as mq_error:
                logger.error(f"Error saat mengirim event ke RabbitMQ: {mq_error}")
                traceback.print_exc()
        else:
            logger.warning("RabbitMQ producer tidak tersedia, event tidak dikirim")

    except Exception as e:
        logger.error(f"Gagal proses crawling dan simpan PDF BigSocial: {e}")
        traceback.print_exc()

def cleanup_rabbitmq_producer():
    """Cleanup RabbitMQ producer connection"""
    global rabbitmq_producer
    if rabbitmq_producer:
        try:
            rabbitmq_producer.close_connection()
            logger.info("RabbitMQ producer connection closed")
        except Exception as e:
            logger.error(f"Error closing RabbitMQ producer connection: {e}")
        finally:
            rabbitmq_producer = None
