from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any
from datetime import datetime
import logging

from models import get_db
from core.security import oauth2_scheme
from core.responses import common_response, Ok, BadRequest, NotFound
from schemas.common import (
    BadRequestResponse,
    UnauthorizedResponse,
    NotFoundResponse,
    InternalServerErrorResponse,
)
from repository.predictive_analytics import PredictiveAnalyticsRepository

router = APIRouter(tags=["Predictive Analytics"])
logger = logging.getLogger(__name__)


# @router.get(
#     "/current-month",
#     responses={
#         "200": {"description": "Success"},
#         "400": {"model": BadRequestResponse},
#         "401": {"model": UnauthorizedResponse},
#         "500": {"model": InternalServerErrorResponse},
#     },
# )
# async def get_current_month_predictions(
#     db: AsyncSession = Depends(get_db),
#     token: str = Depends(oauth2_scheme)
# ):
#     """
#     API untuk mendapatkan prediksi revenue bulan ini dan prediksi bulan depan
#     dengan model version terbaru
#     """
#     try:
#         # Dapatkan model version terbaru
#         latest_version = await PredictiveAnalyticsRepository.get_latest_model_version(db)
#         if not latest_version:
#             return common_response(NotFound(message="No predictions found"))
        
#         # Tentukan bulan ini dan bulan depan
#         current_date = datetime.now()
#         current_month = current_date.month
#         current_year = current_date.year
        
#         if current_month == 12:
#             next_month = 1
#             next_year = current_year + 1
#         else:
#             next_month = current_month + 1
#             next_year = current_year
        
#         # Dapatkan prediksi bulan ini
#         current_month_predictions = await PredictiveAnalyticsRepository.get_current_month_predictions(
#             db, current_month, current_year, latest_version
#         )
        
#         # Dapatkan prediksi bulan depan
#         next_month_predictions = await PredictiveAnalyticsRepository.get_next_month_predictions(
#             db, next_month, next_year, latest_version
#         )
        
#         # Hitung total revenue
#         total_current_month = sum(pred["prediction_revenue"] for pred in current_month_predictions)
#         total_next_month = sum(pred["prediction_revenue"] for pred in next_month_predictions)
        
#         # Hitung persentase perubahan
#         if total_current_month > 0:
#             percentage_change = ((total_next_month - total_current_month) / total_current_month) * 100
#         else:
#             percentage_change = 0
        
#         # Format response
#         response_data = {
#             "current_month": {
#                 "month": f"{current_year}-{current_month:02d}",
#                 "total_prediction_revenue": round(total_current_month, 2),
#                 "predictions": current_month_predictions
#             },
#             "next_month": {
#                 "month": f"{next_year}-{next_month:02d}",
#                 "total_prediction_revenue": round(total_next_month, 2),
#                 "predictions": next_month_predictions
#             },
#             "summary": {
#                 "model_version": latest_version,
#                 "total_percentage_change": round(percentage_change, 2),
#                 "trend": "up" if percentage_change > 0 else "down" if percentage_change < 0 else "stable"
#             }
#         }
        
#         return common_response(Ok(data=response_data))
        
#     except Exception as e:
#         logger.error(f"Error getting current month predictions: {e}")
#         return common_response(BadRequest(message=str(e)))


@router.get(
    "/available-periods",
    responses={
        "200": {"description": "Success"},
        "400": {"model": BadRequestResponse},
        "401": {"model": UnauthorizedResponse},
        "404": {"model": NotFoundResponse},
        "500": {"model": InternalServerErrorResponse},
    },
)
async def get_available_periods(
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    API untuk mendapatkan opsi periode yang tersedia dari versi model terbaru
    """
    try:
        # Dapatkan model version terbaru
        latest_version = await PredictiveAnalyticsRepository.get_latest_model_version(db)
        if not latest_version:
            response_data = {
                "model_version": None,
                "available_periods": [],
                "total_periods": 0
            }
            return common_response(Ok(data=response_data))
        
        # Dapatkan opsi periode yang tersedia
        available_periods = await PredictiveAnalyticsRepository.get_available_periods(db, latest_version)
        
        response_data = {
            "model_version": latest_version,
            "available_periods": available_periods if available_periods else [],
            "total_periods": len(available_periods) if available_periods else 0
        }
        
        return common_response(Ok(data=response_data))
        
    except Exception as e:
        logger.error(f"Error getting available periods: {e}")
        return common_response(BadRequest(message=str(e)))


# @router.get(
#     "/monthly/{year}/{month}",
#     responses={
#         "200": {"description": "Success"},
#         "400": {"model": BadRequestResponse},
#         "401": {"model": UnauthorizedResponse},
#         "404": {"model": NotFoundResponse},
#         "500": {"model": InternalServerErrorResponse},
#     },
# )
# async def get_monthly_predictions(
#     year: int,
#     month: int,
#     db: AsyncSession = Depends(get_db),
#     token: str = Depends(oauth2_scheme)
# ):
#     """
#     API untuk mendapatkan prediksi revenue berdasarkan bulan dan tahun tertentu
#     dengan model version terbaru
#     """
#     try:
#         # Validasi input
#         if month < 1 or month > 12:
#             return common_response(BadRequest(message="Month must be between 1 and 12"))
        
#         if year < 2020 or year > 2030:
#             return common_response(BadRequest(message="Year must be between 2020 and 2030"))
        
#         # Dapatkan model version terbaru
#         latest_version = await PredictiveAnalyticsRepository.get_latest_model_version(db)
#         if not latest_version:
#             return common_response(NotFound(message="No predictions found"))
        
#         # Dapatkan prediksi bulan yang diminta
#         monthly_predictions = await PredictiveAnalyticsRepository.get_monthly_predictions(
#             db, year, month, latest_version
#         )
        
#         if not monthly_predictions:
#             return common_response(
#                 NotFound(message=f"No predictions found for {year}-{month:02d}")
#             )
        
#         # Hitung total revenue
#         total_revenue = sum(pred["prediction_revenue"] for pred in monthly_predictions)
        
#         # Hitung rata-rata percentage difference
#         percentage_differences = [pred["percentage_difference"] for pred in monthly_predictions if pred["percentage_difference"]]
#         avg_percentage_diff = sum(percentage_differences) / len(percentage_differences) if percentage_differences else 0
        
#         # Format response
#         response_data = {
#             "period": f"{year}-{month:02d}",
#             "model_version": latest_version,
#             "total_prediction_revenue": round(total_revenue, 2),
#             "average_percentage_difference": round(avg_percentage_diff, 4),
#             "total_products": len(monthly_predictions),
#             "predictions": monthly_predictions
#         }
        
#         return common_response(Ok(data=response_data))
        
#     except Exception as e:
#         logger.error(f"Error getting monthly predictions: {e}")
#         return common_response(BadRequest(message=str(e)))


@router.get(
    "/next-month-all",
    responses={
        "200": {"description": "Success"},
        "400": {"model": BadRequestResponse},
        "401": {"model": UnauthorizedResponse},
        "404": {"model": NotFoundResponse},
        "500": {"model": InternalServerErrorResponse},
    },
)
async def get_all_next_month_predictions(
    period: str = None,
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    API untuk mendapatkan semua prediksi untuk periode tertentu atau bulan depan untuk semua gl_dss_prod_cat
    Halaman utama Predictive Analytics
    
    Parameters:
    - period: Periode dalam format "YYYY-MM" (opsional, default: bulan depan)
    
    Behavior:
    - Jika period tidak dikirim: akan menampilkan prediksi bulan depan dengan model version terbaru
    - Jika period dikirim: akan mencari data di model version terbaru, jika tidak ada akan fallback ke model version sebelumnya
    """
    try:
        # Dapatkan model version terbaru
        latest_version = await PredictiveAnalyticsRepository.get_latest_model_version(db)
        if not latest_version:
            response_data = {
                "period": period if period else "N/A",
                "model_version": None,
                "summary": {},
                "predictions": [],
                "fallback_info": {
                    "fallback_used": False,
                    "message": "No model version available"
                }
            }
            return common_response(Ok(data=response_data))
        
        # Tentukan periode target
        if period:
            target_period = period
        else:
            # Tentukan bulan depan untuk default
            current_date = datetime.now()
            current_month = current_date.month
            current_year = current_date.year
            
            if current_month == 12:
                next_month = 1
                next_year = current_year + 1
            else:
                next_month = current_month + 1
                next_year = current_year
            
            target_period = f"{next_year}-{next_month:02d}"
        
        # Gunakan method dengan fallback untuk mendapatkan prediksi
        result = await PredictiveAnalyticsRepository.get_all_next_month_predictions_with_fallback(
            db, target_period, latest_version
        )
        
        if not result["predictions"]:
            response_data = {
                "period": target_period,
                "model_version": result.get("model_version", latest_version),
                "summary": {},
                "predictions": [],
                "fallback_info": {
                    "fallback_used": result.get("fallback_used", False),
                    "message": result.get("fallback_info", "No predictions available for the specified period")
                }
            }
            return common_response(Ok(data=response_data))
        
        # Dapatkan summary berdasarkan model version yang digunakan
        summary = await PredictiveAnalyticsRepository.get_prediction_summary(db, result["model_version"], target_period)
        
        # Format response dengan informasi fallback
        response_data = {
            "period": target_period,
            "model_version": result["model_version"],
            "summary": summary if summary else {},
            "predictions": result["predictions"],
            "fallback_info": {
                "fallback_used": result.get("fallback_used", False),
                "message": result.get("fallback_info", "Data retrieved from preferred model version")
            }
        }
        
        return common_response(Ok(data=response_data))
        
    except Exception as e:
        logger.error(f"Error getting all next month predictions: {e}")
        return common_response(BadRequest(message=str(e)))


@router.get(
    "/summary",
    responses={
        "200": {"description": "Success"},
        "400": {"model": BadRequestResponse},
        "401": {"model": UnauthorizedResponse},
        "404": {"model": NotFoundResponse},
        "500": {"model": InternalServerErrorResponse},
    },
)
async def get_prediction_summary(
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    API untuk mendapatkan summary dari semua prediksi bulan depan
    """
    try:
        # Dapatkan model version terbaru
        latest_version = await PredictiveAnalyticsRepository.get_latest_model_version(db)
        if not latest_version:
            response_data = {
                "model_version": None,
                "summary": {}
            }
            return common_response(Ok(data=response_data))
        
        # Dapatkan summary
        summary = await PredictiveAnalyticsRepository.get_prediction_summary(db, latest_version)
        
        response_data = {
            "model_version": latest_version,
            "summary": summary if summary else {}
        }
        
        return common_response(Ok(data=response_data))
        
    except Exception as e:
        logger.error(f"Error getting prediction summary: {e}")
        return common_response(BadRequest(message=str(e)))


@router.get(
    "/test-retrain",
    responses={
        "200": {"description": "Success"},
        "400": {"model": BadRequestResponse},
        "500": {"model": InternalServerErrorResponse},
    },
)
async def test_predictive_analytics_retrain():
    """
    Endpoint untuk testing scheduler Predictive Analytics retrain secara manual
    """
    try:
        from services.predictive_analytics_retrain_service import run_predictive_analytics_retrain_sync
        
        logger.info("Manually triggering Predictive Analytics retrain for testing.")
        results = run_predictive_analytics_retrain_sync()
        
        # Format response dengan detail hasil retrain
        if results:
            successful = sum(results.values())
            total = len(results)
            message = f"Predictive Analytics retrain completed. Success: {successful}/{total} products."
            
            return common_response(
                Ok(message=message, data={
                    "total_products": total,
                    "successful": successful,
                    "failed": total - successful,
                    "results": results
                })
            )
        else:
            return common_response(
                BadRequest(message="Predictive Analytics retrain failed. Check console logs for details.")
            )
        
    except Exception as e:
        logger.error(f"Error during manual Predictive Analytics retrain test: {e}")
        return common_response(BadRequest(message=str(e)))


@router.get(
    "/test-retrain-product/{product_name}",
    responses={
        "200": {"description": "Success"},
        "400": {"model": BadRequestResponse},
        "500": {"model": InternalServerErrorResponse},
    },
)
async def test_predictive_analytics_retrain_product(product_name: str):
    """
    Endpoint untuk testing retrain Predictive Analytics untuk produk tertentu
    """
    try:
        from services.predictive_analytics_retrain_service import PredictiveAnalyticsRetrainService
        from config.model_parameters import PRODUCT_CONFIGS
        
        if product_name not in PRODUCT_CONFIGS:
            return common_response(
                BadRequest(message=f"Product '{product_name}' not found. Available products: {list(PRODUCT_CONFIGS.keys())}")
            )
        
        logger.info(f"Manually triggering Predictive Analytics retrain for product: {product_name}")
        
        service = PredictiveAnalyticsRetrainService()
        success = service.retrain_single_product(product_name, PRODUCT_CONFIGS[product_name])
        
        if success:
            return common_response(
                Ok(message=f"Retrain for product '{product_name}' completed successfully.")
            )
        else:
            return common_response(
                BadRequest(message=f"Retrain for product '{product_name}' failed. Check console logs for details.")
            )
        
    except Exception as e:
        logger.error(f"Error during manual Predictive Analytics retrain for product {product_name}: {e}")
        return common_response(BadRequest(message=str(e)))


@router.get(
    "/anomaly-detection",
    responses={
        "200": {"description": "Success"},
        "400": {"model": BadRequestResponse},
        "401": {"model": UnauthorizedResponse},
        "404": {"model": NotFoundResponse},
        "500": {"model": InternalServerErrorResponse},
    },
)
async def get_anomaly_detection(
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    API untuk mendapatkan anomali terdeteksi berdasarkan perubahan terbesar dari notifikasi bulan terbaru
    Fallback ke SummaryInsight jika tabel notifications tidak tersedia
    
    Returns:
    - anomaly_data: List data anomali yang terdeteksi
    - period: Periode bulan terbaru
    - summary: Ringkasan statistik anomali
    """
    try:
        # Dapatkan data anomali
        anomaly_result = await PredictiveAnalyticsRepository.get_anomaly_detection(db)
        
        
        # Format response
        response_data = {
            "period": anomaly_result["period"],
            "summary": anomaly_result["summary"],
            "anomaly_data": anomaly_result["anomaly_data"]
        }
        
        return common_response(Ok(data=response_data))
        
    except Exception as e:
        logger.error(f"Error getting anomaly detection: {e}")
        return common_response(BadRequest(message=str(e)))


# @router.get(
#     "/cron-config",
#     responses={
#         "200": {"description": "Success"},
#         "400": {"model": BadRequestResponse},
#         "401": {"model": UnauthorizedResponse},
#         "500": {"model": InternalServerErrorResponse},
#     },
# )
# async def get_cron_config(
#     db: AsyncSession = Depends(get_db),
#     token: str = Depends(oauth2_scheme)
# ):
#     """
#     API untuk mendapatkan konfigurasi cron scheduler dari global_variabel
#     """
#     try:
#         from sqlalchemy import select
#         from models.GlobalVariabel import GlobalVariabel
        
#         # Query untuk mendapatkan konfigurasi cron
#         query = select(GlobalVariabel).where(
#             GlobalVariabel.name.like("cron_%"),
#             GlobalVariabel.isact == True
#         )
#         result = await db.execute(query)
#         cron_configs = result.scalars().all()
        
#         config_list = []
#         for config in cron_configs:
#             task_name = config.name.replace("cron_", "")
#             config_list.append({
#                 "task_name": task_name,
#                 "cron_expression": config.value,
#                 "group": config.group,
#                 "description": config.satuan if config.satuan else "",
#                 "is_active": config.isact,
#                 "created_at": config.created_at.isoformat() if config.created_at else None
#             })
        
#         response_data = {
#             "total_tasks": len(config_list),
#             "cron_configs": config_list
#         }
        
#         return common_response(Ok(data=response_data))
        
#     except Exception as e:
#         logger.error(f"Error getting cron config: {e}")
#         return common_response(BadRequest(message=str(e)))


# @router.post(
#     "/cron-config",
#     responses={
#         "200": {"description": "Success"},
#         "400": {"model": BadRequestResponse},
#         "401": {"model": UnauthorizedResponse},
#         "500": {"model": InternalServerErrorResponse},
#     },
# )
# async def update_cron_config(
#     task_name: str,
#     cron_expression: str,
#     description: str = None,
#     db: AsyncSession = Depends(get_db),
#     token: str = Depends(oauth2_scheme)
# ):
#     """
#     API untuk mengupdate konfigurasi cron scheduler
    
#     Parameters:
#     - task_name: Nama task (tanpa prefix 'cron_')
#     - cron_expression: Expression cron (format: minute hour day month day_of_week)
#     - description: Deskripsi task (opsional)
#     """
#     try:
#         from sqlalchemy import select, update
#         from models.GlobalVariabel import GlobalVariabel
        
#         # Validasi cron expression (basic validation)
#         cron_parts = cron_expression.split()
#         if len(cron_parts) != 5:
#             return common_response(BadRequest(message="Invalid cron expression. Must have 5 parts: minute hour day month day_of_week"))
        
#         # Cek apakah konfigurasi sudah ada
#         config_name = f"cron_{task_name}"
#         query = select(GlobalVariabel).where(GlobalVariabel.name == config_name)
#         result = await db.execute(query)
#         existing_config = result.scalar()
        
#         if existing_config:
#             # Update existing config
#             update_query = update(GlobalVariabel).where(
#                 GlobalVariabel.name == config_name
#             ).values(
#                 value=cron_expression,
#                 satuan=description if description else existing_config.satuan,
#                 isact=True
#             )
#             await db.execute(update_query)
#             message = f"Cron config for {task_name} updated successfully"
#         else:
#             # Insert new config
#             from sqlalchemy import insert
#             insert_query = insert(GlobalVariabel).values(
#                 name=config_name,
#                 value=cron_expression,
#                 group="scheduler",
#                 satuan=description if description else f"Cron config for {task_name}",
#                 isact=True
#             )
#             await db.execute(insert_query)
#             message = f"Cron config for {task_name} created successfully"
        
#         await db.commit()
        
#         response_data = {
#             "task_name": task_name,
#             "cron_expression": cron_expression,
#             "description": description,
#             "message": message
#         }
        
#         return common_response(Ok(data=response_data, message=message))
        
#     except Exception as e:
#         logger.error(f"Error updating cron config: {e}")
#         return common_response(BadRequest(message=str(e)))


# @router.delete(
#     "/cron-config/{task_name}",
#     responses={
#         "200": {"description": "Success"},
#         "400": {"model": BadRequestResponse},
#         "401": {"model": UnauthorizedResponse},
#         "500": {"model": InternalServerErrorResponse},
#     },
# )
# async def delete_cron_config(
#     task_name: str,
#     db: AsyncSession = Depends(get_db),
#     token: str = Depends(oauth2_scheme)
# ):
#     """
#     API untuk menghapus konfigurasi cron scheduler
    
#     Parameters:
#     - task_name: Nama task (tanpa prefix 'cron_')
#     """
#     try:
#         from sqlalchemy import select, update
#         from models.GlobalVariabel import GlobalVariabel
        
#         config_name = f"cron_{task_name}"
        
#         # Cek apakah konfigurasi ada
#         query = select(GlobalVariabel).where(GlobalVariabel.name == config_name)
#         result = await db.execute(query)
#         existing_config = result.scalar()
        
#         if not existing_config:
#             return common_response(NotFound(message=f"Cron config for {task_name} not found"))
        
#         # Soft delete dengan mengubah isact menjadi False
#         update_query = update(GlobalVariabel).where(
#             GlobalVariabel.name == config_name
#         ).values(isact=False)
        
#         await db.execute(update_query)
#         await db.commit()
        
#         response_data = {
#             "task_name": task_name,
#             "message": f"Cron config for {task_name} deleted successfully"
#         }
        
#         return common_response(Ok(data=response_data, message=f"Cron config for {task_name} deleted successfully"))
        
#     except Exception as e:
#         logger.error(f"Error deleting cron config: {e}")
#         return common_response(BadRequest(message=str(e)))
