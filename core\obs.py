import logging
from typing import Dict, List, Optional, Union, Any
from obs import ObsClient


class HuaweiOBS:
    """
    Huawei Object Storage Service (OBS) client wrapper.

    This class provides simplified access to Huawei OBS operations like
    bucket management, object upload/download, and listing.
    """

    def __init__(
        self,
        access_key: str,
        secret_key: str,
        server: str,
        bucket_name: Optional[str] = None,
    ):
        """
        Initialize the Huawei OBS client.

        Args:
            access_key: Access key ID
            secret_key: Secret access key
            server: OBS endpoint (e.g., 'https://obs.ap-southeast-3.myhuaweicloud.com')
            bucket_name: Default bucket name to use (optional)
        """
        self.access_key = access_key
        self.secret_key = secret_key
        self.server = server
        self.bucket_name = bucket_name
        self.client = self._create_client()

    def _create_client(self) -> ObsClient:
        """Create and return an OBS client instance."""
        return ObsClient(
            access_key_id=self.access_key,
            secret_access_key=self.secret_key,
            server=self.server,
            signature='v2', 
            path_style=True, 
        )

    def create_bucket(self, bucket_name: str) -> Dict[str, Any]:
        """
        Create a new bucket.

        Args:
            bucket_name: Name of the bucket to create

        Returns:
            Response from the OBS service
        """
        return self.client.createBucket(bucket_name)

    def list_buckets(self) -> List[Dict[str, Any]]:
        """
        List all available buckets.

        Returns:
            List of bucket information
        """
        resp = self.client.listBuckets()
        if resp.status < 300:
            return resp.body.buckets
        else:
            logging.error(f"Failed to list buckets: {resp.errorMessage}")
            return []

    def upload_file(
        self, local_file_path: str, object_key: str, bucket_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Upload a local file to OBS.

        Args:
            local_file_path: Path to the local file
            object_key: Object key (name) in the OBS bucket
            bucket_name: Target bucket name (uses default if not specified)

        Returns:
            Response from the OBS service
        """
        bucket = bucket_name or self.bucket_name
        if not bucket:
            raise ValueError("Bucket name must be provided")

        return self.client.putFile(bucket, object_key, local_file_path)

    def download_file(
        self, object_key: str, local_file_path: str, bucket_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Download an object from OBS to a local file.

        Args:
            object_key: Object key in the OBS bucket
            local_file_path: Path where to save the downloaded file
            bucket_name: Source bucket name (uses default if not specified)

        Returns:
            Response from the OBS service
        """
        bucket = bucket_name or self.bucket_name
        if not bucket:
            raise ValueError("Bucket name must be provided")

        return self.client.getObject(bucket, object_key, downloadPath=local_file_path)

    def list_objects(
        self, prefix: str = "", bucket_name: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        List objects in a bucket with an optional prefix.

        Args:
            prefix: Filter objects by prefix
            bucket_name: Bucket to list objects from (uses default if not specified)

        Returns:
            List of object information
        """
        bucket = bucket_name or self.bucket_name
        if not bucket:
            raise ValueError("Bucket name must be provided")

        resp = self.client.listObjects(bucket, prefix=prefix)
        if resp.status < 300:
            return resp.body.contents
        else:
            logging.error(f"Failed to list objects: {resp.errorMessage}")
            return []

    def delete_object(
        self, object_key: str, bucket_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Delete an object from OBS.

        Args:
            object_key: Object key to delete
            bucket_name: Bucket containing the object (uses default if not specified)

        Returns:
            Response from the OBS service
        """
        bucket = bucket_name or self.bucket_name
        if not bucket:
            raise ValueError("Bucket name must be provided")

        return self.client.deleteObject(bucket, object_key)

    def generate_presigned_url(
        self,
        object_key: str,
        expiry_seconds: int = 3600,
        bucket_name: Optional[str] = None,
    ) -> str:
        """
        Generate a presigned URL for temporary object access.

        Args:
            object_key: Object key to generate URL for
            expiry_seconds: URL validity period in seconds
            bucket_name: Bucket containing the object (uses default if not specified)

        Returns:
            Presigned URL string
        """
        bucket = bucket_name or self.bucket_name
        if not bucket:
            raise ValueError("Bucket name must be provided")

        return self.client.createSignedUrl(
            "GET", bucket, object_key, expires=expiry_seconds
        )

    def close(self):
        """Close the OBS client and release resources."""
        if hasattr(self, "client"):
            self.client.close()


def create_obs_client(
    access_key: str, secret_key: str, server: str, bucket_name: Optional[str] = None
) -> HuaweiOBS:
    """
    Factory function to create and initialize a Huawei OBS client.

    Args:
        access_key: Access key ID
        secret_key: Secret access key
        server: OBS endpoint
        bucket_name: Default bucket name

    Returns:
        Configured HuaweiOBS instance
    """
    print(f"OBS client initialized with server: {server}, bucket: {bucket_name}")
    return HuaweiOBS(access_key, secret_key, server, bucket_name)
