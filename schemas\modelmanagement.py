from pydantic import BaseModel
from typing import List, Optional


class MetaResponse(BaseModel):
    count: int
    page_count: int
    page_size: int
    page: int


class ListUploadedFile(BaseModel):
    id: str
    filename: str
    filesize: int
    filetype: str
    processing_status: str
    service: Optional[str] = None
    created_at: str
    user_id: Optional[str] = None
    name: Optional[str] = None
    message: Optional[str] = None


class ListUploadedFileResponse(BaseModel):
    meta: MetaResponse
    data: List[ListUploadedFile]
    status: str
    code: int
    message: str
