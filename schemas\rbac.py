from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime

class MetaResponse(BaseModel):
    count:int
    page_count:int
    page_size:int
    page:int

class PermissionSchema(BaseModel):
    id: int
    module: Optional[str]
    access: bool


class RoleManagementSchema(BaseModel):
    role_id: int
    name: str
    description: Optional[str]
    group: Optional[str]
    access_feature: Optional[str] 
    permissions: List[PermissionSchema]
    created_at: Optional[datetime]
    updated_at: Optional[datetime]
    isact: Optional[bool]

class ListRole(BaseModel):
    id: int
    name: Optional[str]=''
    group: Optional[str]=''
    description: Optional[str]=''

class ListRoleResponse(BaseModel):
    meta: MetaResponse
    data: List[ListRole]
    status: str
    code: int
    message: str

class ListUserWoRole(BaseModel):
    id:str
    name:Optional[str] = ''
    email:Optional[str] = ''
    role_id: Optional[int] = None
    role_name: Optional[str] = ''

class ListUserRole(BaseModel):
    id:str
    name:Optional[str] = ''
    email:Optional[str] = ''
    role_id: Optional[int] = None
    role_name: Optional[str] = ''

class ListUserRoleResponse(BaseModel):
    meta: MetaResponse
    data: List[ListUserRole]
    status: str
    code: int
    message: str
    
class AssignRoleRequest(BaseModel):
    id:str
    name:Optional[str] = ''
    email:Optional[str] = ''
    role_id: Optional[int] = None
    role_name: Optional[str] = ''

class AddRoleRequest(BaseModel):
    name: str
    description: Optional[str] = None
    access_feature: Optional[str] = None

class DeleteRoleRequest(BaseModel):
    role_id: int

class ListUserWoRoleResponse(BaseModel):
    meta: MetaResponse
    data: List[ListUserWoRole]
    status: str
    code: int
    message: str

class ListPermissionRequest(BaseModel):
    permission_id: int
    access: bool

class UpdatePermissionRequest(BaseModel):
    role_id: Optional[int] = None
    permissions: List[ListPermissionRequest] = []

class UpdatePermissionResponse(BaseModel):
    role_id: int
    permission_id: int
    isact: bool
    message: str


class UpdateMultiplePermissionRequest(BaseModel):
    data: List[UpdatePermissionRequest]


class UpdateMultiplePermissionResponse(BaseModel):
    updated_permissions: List[UpdatePermissionResponse]
