import traceback
from typing import Optional, List
from sqlalchemy import func, select, and_, or_, update
from models.Role import Role
from models.Permission import Permission
from models.RolePermission import RolePermission
from models.User import User
from models.UserRole import UserRole
from models.Module import Module
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime
from sqlalchemy.orm import selectinload
from schemas.rbac import AssignRoleRequest, ListRole, ListUserRole, ListUserWoRole, AddRoleRequest, DeleteRoleRequest
from schemas.auth import EditUserRequest, SignUpRequest
from core.security import generate_hash_password

# Fungsi untuk mengambil data dari model User yang tidak mempunyai roles

async def get_users_without_roles(
    db: AsyncSession,
    page: int = 1,
    page_size: int = 10,
    src: Optional[str] = None,
):
    limit = page_size
    offset = (page - 1) * limit

    base_query = (
        select(User)
        .outerjoin(UserRole, User.id == UserRole.c.emp_id)
        .where(UserRole.c.role_id == None)
        .where(User.isact == True)  
    )
    if src:
        base_query = base_query.filter(or_(
            User.email.ilike(f"%{src}%"),
            User.name.ilike(f"%{src}%"),
        ))

    # Query for paginated data
    data_query = base_query.offset(offset).limit(limit)
    result = await db.execute(data_query)
    users = result.scalars().all()

    # Query for total count
    count_query = (
        select(func.count())
        .select_from(User)
        .outerjoin(UserRole, User.id == UserRole.c.emp_id)
        .where(UserRole.c.role_id == None)
        .where(User.isact == True)
    )
    if src:
        count_query = count_query.filter(User.email == src)
    count_result = await db.execute(count_query)
    num_data = count_result.scalar() or 0
    num_page = (num_data + limit - 1) // limit if limit else 1
    ls_data=[]
    for item in users:
        ls_data.append(
            ListUserWoRole(
                id=str(item.id),
                name=item.name if  item.name else '',
                email=item.email if item.email else '',
                role_id=None,
                role_name=''
            ).model_dump()
        )

    return ls_data, num_data, num_page

async def delete_role_from_user_repo(
    db: AsyncSession,
    email:str,
):
    try:
        query = select(User).where(User.email == email, User.isact == True)
        result = await db.execute(query)
        user = result.scalar()
        if not user:
            raise ValueError(f"User with email {email} not found")
        
        # Delete all roles associated with the user
        delete_stmt = (
            UserRole.delete()
            .where(UserRole.c.emp_id == user.id)
        )
        await db.execute(delete_stmt)
        await db.commit()
        return True
    except Exception as e:
        traceback.print_exc()
        raise ValueError(f"Error in delete_role_from_user_repo: {str(e)}")

async def ls_user_role(
    db: AsyncSession,
    page: int = 1,
    page_size: int = 10,
    src: Optional[str] = None,
    id_role: Optional[int] = None
):
    limit = page_size
    offset = (page - 1) * limit

    base_query = (
        select(User)
        .options(
            selectinload(User.roles)
        )
        .outerjoin(UserRole, User.id == UserRole.c.emp_id)
        .where(UserRole.c.role_id == id_role)
        .where(User.isact == True)  
    )
    if src:
        base_query = base_query.filter(or_(
            User.email.ilike(f"%{src}%"),
            User.name.ilike(f"%{src}%"),
        ))

    # Query for paginated data
    data_query = base_query.offset(offset).limit(limit)
    result = await db.execute(data_query)
    users = result.scalars().all()

    # Query for total count
    count_query = (
        select(func.count())
        .select_from(User)
        .outerjoin(UserRole, User.id == UserRole.c.emp_id)
        .where(UserRole.c.role_id == id_role)
        .where(User.isact == True)
    )
    if src:
        count_query = count_query.filter(or_(
            User.email.ilike(f"%{src}%"),
            User.name.ilike(f"%{src}%"),
        ))
    count_result = await db.execute(count_query)
    num_data = count_result.scalar() or 0
    num_page = (num_data + limit - 1) // limit if limit else 1
    ls_data=[]
    for item in users:
        ls_data.append(
            ListUserRole(
                id=str(item.id),
                name=item.name if  item.name else '',
                email=item.email if item.email else '',
                role_id=item.roles[0].id if item.roles else None,
                role_name=item.roles[0].name if item.roles else ''
            ).model_dump()
        )

    return ls_data, num_data, num_page


async def assign_role_to_user(
    db: AsyncSession,
    payload:AssignRoleRequest,
):
    try:
        query = select(User).where(User.id == payload.id)
        result = await db.execute(query)
        user = result.scalar()
        if not user:
            raise ValueError(f"User with ID {payload.id} not found")
        query = select(Role).where(Role.id == payload.role_id)
        result = await db.execute(query)
        role = result.scalar()
        if not role:
            raise ValueError(f"Role with ID {payload.role_id} not found")
        
        # Insert directly into the user_role table
        stmt = UserRole.insert().values(
            emp_id=user.id,
            role_id=role.id
        )
        await db.execute(stmt)
        await db.commit()
        return True
    except ValueError as ve:
        raise ve
    except Exception as e:
        traceback.print_exc()
        raise ValueError(f"Error in assign_role_to_user: {str(e)}")

async def get_role(db:AsyncSession, src:Optional[str]=None):
    try:
        query = select(Role).where(Role.isact == True)
        if src:
            query = query.filter(Role.name.ilike(f"%{src}%"))
        result = await db.execute(query)
        roles = result.scalars().all()
        ls_data=[]
        for item in roles:
            ls_data.append(
                ListRole(
                    id=item.id,
                    name=item.name if item.name else '',
                    group=item.group if item.group else '',
                    description=item.description if item.description else ''
                ).model_dump()
            )
        return ls_data
    except Exception as e: 
        traceback.print_exc()
        raise ValueError(f"Error in get_role: {str(e)}")

async def get_role_management(
    db: AsyncSession,
    isact: Optional[bool] = True
) -> List[dict]:
    try:
        query = (
            select(Role)
            .filter(Role.isact == isact)
        )
        
        result = await db.execute(query)
        roles = result.scalars().all()
        
        role_list = []
        for role in roles:
            perm_query = (
                select(Permission, Module, RolePermission.c.isact)
                .join(RolePermission, and_(
                    RolePermission.c.permission_id == Permission.id,
                    RolePermission.c.role_id == role.id,
                    RolePermission.c.isdel == False
                ))
                .outerjoin(Module, Permission.module_id == Module.id)
                .filter(Permission.isact == True)
            ).order_by(Module.name.asc())
            perm_result = await db.execute(perm_query)
            permissions_with_modules = perm_result.all()
            
            permissions_data = []
            for perm, module, isact in permissions_with_modules:
                module_data = None
                if module:
                    module_data = module.name
                
                permissions_data.append({
                    "permission_id": perm.id,
                    "permission_name": perm.name,
                    "module": module_data,
                    "access": isact
                })
            
            role_data = {
                "role_id": role.id,
                "name": role.name,
                "description": role.description,
                "group": role.group,
                "access_feature": role.access_feature,
                "permissions": permissions_data,
                "created_at": role.created_at.isoformat() if role.created_at else None,
                "updated_at": role.updated_at.isoformat() if role.updated_at else None,
                "isact": role.isact
            }
            role_list.append(role_data)
            
        return role_list
        
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        raise ValueError(f"Error in get_role_management: {str(e)}\n{error_details}")
    
    
async def update_permission(
    db: AsyncSession,
    role_id: int,
    permissions: int,
    # isact: bool,
):
    try:
        # role = await db.execute(
        #     select(Role).filter(Role.id == role_id)
        # )
        # role = role.scalar()
        # if not role:
        #     raise ValueError(f"Role dengan ID {role_id} tidak ditemukan")

        for item in permissions:
            permission = await db.execute(
                select(Permission).filter(Permission.id == item.permission_id)
            )
            permission = permission.scalar()
            if not permission:
                raise ValueError(f"Permission dengan ID {item.permission_id} tidak ditemukan")

            existing_role_permission = await db.execute(
                select(RolePermission).filter(
                    and_(
                        RolePermission.c.role_id == role_id,
                        RolePermission.c.permission_id == item.permission_id
                    )
                )
            )
            existing_role_permission = existing_role_permission.scalar()

            if existing_role_permission:
                stmt = (
                    update(RolePermission)
                    .where(
                        and_(
                            RolePermission.c.role_id == role_id,
                            RolePermission.c.permission_id == item.permission_id
                        )
                    )
                    .values(isact=item.access)
                )
                await db.execute(stmt)
            # else:
            #     stmt = RolePermission.insert().values(
            #         role_id=role_id,
            #         permission_id=item.permission_id,
            #         isact=item.access
            #     )
            #     await db.execute(stmt)

            await db.commit()

        return

    except Exception as e:
        await db.rollback()
        raise ValueError(f"Error in update_permission: {str(e)}")

async def get_all_permissions(db: AsyncSession) -> List[dict]:
    """Mendapatkan semua permission yang tersedia"""
    try:
        query = (
            select(Permission, Module)
            .outerjoin(Module, Permission.module_id == Module.id)
            .filter(Permission.isact == True)
            .order_by(Module.name.asc(), Permission.name.asc())
        )
        
        result = await db.execute(query)
        permissions = result.all()
        
        permission_list = []
        for perm, module in permissions:
            module_data = None
            if module:
                module_data = module.name
            
            permission_list.append({
                "permission_id": perm.id,
                "permission_name": perm.name,
                "module": module_data,
                "module_id": perm.module_id
            })
        
        return permission_list
        
    except Exception as e:
        traceback.print_exc()
        raise ValueError(f"Error in get_all_permissions: {str(e)}")

async def add_role(
    db: AsyncSession,
    payload: AddRoleRequest,
    user_id: str
) -> dict:
    try:
        # Check if role name already exists
        existing_role_query = select(Role).where(
            and_(
                Role.name == payload.name,
                Role.isact == True
            )
        )
        existing_role_result = await db.execute(existing_role_query)
        existing_role = existing_role_result.scalar()
        
        if existing_role:
            raise ValueError(f"Role dengan nama '{payload.name}' sudah ada")
        
        # Create new role
        new_role = Role(
            name=payload.name,
            description=payload.description,
            group="Dashboard Access",
            access_feature=payload.access_feature,
            created_by=user_id,
            created_at=datetime.now(),
            isact=True
        )
        
        db.add(new_role)
        await db.commit()
        await db.refresh(new_role)
        
        all_permissions = await get_all_permissions(db)
        
        role_permission_data = []
        for permission in all_permissions:
            role_permission_data.append({
                "role_id": new_role.id,
                "permission_id": permission["permission_id"],
                "isact": False, 
                "isdel": False
            })
        
        # Insert all role_permission entries
        if role_permission_data:
            stmt = RolePermission.insert().values(role_permission_data)
            await db.execute(stmt)
            await db.commit()
        
        return {
            "id": new_role.id,
            "name": new_role.name,
            "description": new_role.description,
            "group": new_role.group,
            "access_feature": new_role.access_feature,
            "created_at": new_role.created_at.isoformat() if new_role.created_at else None,
            "isact": new_role.isact,
            "permissions_count": len(all_permissions)
        }
        
    except ValueError as ve:
        raise ve
    except Exception as e:
        await db.rollback()
        traceback.print_exc()
        raise ValueError(f"Error in add_role: {str(e)}")

async def delete_role(
    db: AsyncSession,
    role_id: int,
    user_id: str
) -> dict:
    try:
        # Check if role exists and is active
        role_query = select(Role).where(
            and_(
                Role.id == role_id,
                Role.isact == True
            )
        )
        role_result = await db.execute(role_query)
        role = role_result.scalar()
        
        if not role:
            raise ValueError(f"Role dengan ID {role_id} tidak ditemukan atau sudah dihapus")
        
        role.isact = False
        role.updated_by = user_id
        role.updated_at = datetime.now()
        
        role_permission_update = (
            update(RolePermission)
            .where(RolePermission.c.role_id == role_id)
            .values(isdel=True)
        )
        await db.execute(role_permission_update)
        
        user_role_delete = (
            UserRole.delete()
            .where(UserRole.c.role_id == role_id)
        )
        await db.execute(user_role_delete)
        
        await db.commit()
        
        return {
            "id": role.id,
            "name": role.name,
            "description": role.description,
            "group": role.group,
            "access_feature": role.access_feature,
            "deleted_at": role.updated_at.isoformat() if role.updated_at else None,
            "deleted_by": role.updated_by,
            "isact": role.isact
        }
        
    except ValueError as ve:
        raise ve
    except Exception as e:
        await db.rollback()
        traceback.print_exc()
        raise ValueError(f"Error in delete_role: {str(e)}")


# User Management Functions
async def list_user_management(
    db: AsyncSession,
    page: int = 1,
    page_size: int = 10,
    src: Optional[str] = None,
):
    """
    List all users with pagination and search functionality
    """
    try:
        limit = page_size
        offset = (page - 1) * limit

        base_query = (
            select(User)
            .options(selectinload(User.roles))
            .where(User.isact == True)
        )
        
        if src:
            base_query = base_query.filter(
                or_(
                    User.name.ilike(f"%{src}%"),
                    User.email.ilike(f"%{src}%")
                )
            )

        # Query for paginated data
        data_query = base_query.offset(offset).limit(limit)
        result = await db.execute(data_query)
        users = result.scalars().all()

        # Query for total count
        count_query = (
            select(func.count())
            .select_from(User)
            .where(User.isact == True)
        )
        if src:
            count_query = count_query.filter(
                or_(
                    User.name.ilike(f"%{src}%"),
                    User.email.ilike(f"%{src}%")
                )
            )
        
        count_result = await db.execute(count_query)
        num_data = count_result.scalar() or 0
        num_page = (num_data + limit - 1) // limit if limit else 1

        user_list = []
        for user in users:
            user_data = {
                "id": str(user.id),
                "nik": user.nik if user.nik else "",
                "name": user.name if user.name else "",
                "email": user.email if user.email else "",
                "phone": user.phone if user.phone else "",
                "address": user.address if user.address else "",
                "role_id": user.roles[0].id if user.roles else None,
                "role_name": user.roles[0].name if user.roles else "",
                "isact": user.isact,
                "created_at": user.created_at.strftime("%Y-%m-%d %H:%M:%S") if user.created_at else "",
                "updated_at": user.updated_at.strftime("%Y-%m-%d %H:%M:%S") if user.updated_at else "",
            }
            user_list.append(user_data)

        return user_list, num_data, num_page

    except Exception as e:
        traceback.print_exc()
        raise ValueError(f"Error in list_user_management: {str(e)}")

async def get_user_detail(
    db: AsyncSession,
    user_id: str,
):
    """
    Get detailed information of a specific user
    """
    try:
        query = (
            select(User)
            .options(selectinload(User.roles))
            .where(User.id == user_id, User.isact == True)
        )
        
        result = await db.execute(query)
        user = result.scalar_one_or_none()
        
        if not user:
            return None

        user_data = {
            "id": str(user.id),
            "nik": user.nik if user.nik else "",
            "name": user.name if user.name else "",
            "email": user.email if user.email else "",
            "phone": user.phone if user.phone else "",
            "address": user.address if user.address else "",
            "role_id": user.roles[0].id if user.roles else None,
            "role_name": user.roles[0].name if user.roles else "",
            "isact": user.isact,
            "created_at": user.created_at.strftime("%Y-%m-%d %H:%M:%S") if user.created_at else "",
            "updated_at": user.updated_at.strftime("%Y-%m-%d %H:%M:%S") if user.updated_at else "",
        }
        
        return user_data

    except Exception as e:
        traceback.print_exc()
        raise ValueError(f"Error in get_user_detail: {str(e)}")

async def update_user_management(
    db: AsyncSession,
    user_id: str,
    request: EditUserRequest,
):
    """
    Update user information
    """
    try:
        user = await get_user_by_id(db, user_id)
        if not user:
            return None

        if request.name is not None:
            user.name = request.name

        if request.role_id is not None:
            # Remove existing roles
            existing_roles = await db.execute(
                select(UserRole).where(UserRole.c.emp_id == user.id)
            )
            if existing_roles.first():
                delete_stmt = UserRole.delete().where(UserRole.c.emp_id == user.id)
                await db.execute(delete_stmt)

            # Add new role
            role_query = select(Role).filter(Role.id == request.role_id, Role.isact == True)
            role_result = await db.execute(role_query)
            new_role = role_result.scalar_one_or_none()
            
            if new_role:
                stmt = UserRole.insert().values(
                    emp_id=user.id,
                    role_id=new_role.id
                )
                await db.execute(stmt)

        db.add(user)
        await db.commit()
        await db.refresh(user)
        
        return user

    except Exception as e:
        await db.rollback()
        traceback.print_exc()
        raise ValueError(f"Error in update_user_management: {str(e)}")

async def delete_user_management(
    db: AsyncSession,
    user_id: str,
):
    """
    Soft delete a user (set isact to False)
    """
    try:
        user = await get_user_by_id(db, user_id)
        if not user:
            return False

        user.isact = False
        db.add(user)
        await db.commit()
        
        return True

    except Exception as e:
        await db.rollback()
        traceback.print_exc()
        raise ValueError(f"Error in delete_user_management: {str(e)}")

async def create_user_management(
    db: AsyncSession,
    request: SignUpRequest,
):
    """
    Create a new user
    """
    try:
        # Check if user already exists
        existing_user = await get_user_by_email(db, request.nik, exclude_soft_delete=True)
        if existing_user:
            raise ValueError(f"User with nik {request.nik} already exists")

        # Create new user
        new_user = User(
            email=request.nik,
            nik=request.nik,
            name=request.name,
            phone="",
            address="",
            isact=True,
            status=True,
            created_at=datetime.now(),
        )

        
        
        db.add(new_user)
        await db.commit()
        await db.refresh(new_user)
        
        stmt = UserRole.insert().values(
            emp_id=new_user.id,
            role_id=request.role_id
        )
        await db.execute(stmt)
        await db.commit()

        return new_user

    except Exception as e:
        await db.rollback()
        traceback.print_exc()
        raise ValueError(f"{str(e)}")

async def get_role_options_for_user(
    db: AsyncSession,
):
    """
    Get available role options for user assignment
    """
    try:
        query = (
            select(Role)
            .where(Role.isact == True)
            .order_by(Role.name.asc())
        )
        
        result = await db.execute(query)
        roles = result.scalars().all()
        
        role_options = []
        for role in roles:
            role_options.append({
                "id": role.id,
                "name": role.name,
                "description": role.description if role.description else "",
                "group": role.group if role.group else "",
            })
        
        return role_options

    except Exception as e:
        traceback.print_exc()
        raise ValueError(f"Error in get_role_options_for_user: {str(e)}")

async def get_user_by_id(
    db: AsyncSession,
    user_id: str,
) -> Optional[User]:
    """
    Get user by ID
    """
    try:
        query = select(User).filter(
            User.id == user_id,
            User.isact == True
        ).options(
            selectinload(User.roles)
        )
        
        result = await db.execute(query)
        user = result.scalar_one_or_none()
        
        return user

    except Exception as e:
        raise ValueError(f"Error in get_user_by_id: {str(e)}")

async def get_user_by_email(
    db: AsyncSession,
    email: str,
    exclude_soft_delete: bool = False
) -> Optional[User]:
    """
    Get user by email
    """
    try:
        query = select(User).filter(User.email == email)
        if exclude_soft_delete:
            query = query.filter(User.isact == True)
        
        result = await db.execute(query)
        user = result.scalar_one_or_none()
        
        return user

    except Exception as e:
        raise ValueError(f"Error in get_user_by_email: {str(e)}")


async def get_permissions(db: AsyncSession) -> List[dict]:
    """
    Get all permissions with module information
    """
    try:
        query = (
            select(Permission, Module)
            .outerjoin(Module, Permission.module_id == Module.id)
            .filter(Permission.isact == True)
            .order_by(Module.name.asc(), Permission.name.asc())
        )
        
        result = await db.execute(query)
        permissions_with_modules = result.all()
        
        permissions_data = []
        for perm, module in permissions_with_modules:
            module_data = None
            if module:
                module_data = module.name
            
            permissions_data.append({
                "permission_id": perm.id,
                "permission_name": perm.name,
                "module": module_data,
                "description": perm.description if perm.description else "",
            })
        
        return permissions_data
        
    except Exception as e:
        traceback.print_exc()
        raise ValueError(f"Error in get_permissions: {str(e)}")


