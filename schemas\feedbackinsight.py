from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime


class MetaResponse(BaseModel):
    count: int
    page_count: int
    page_size: int
    page: int


class ICAFeedbackPayload(BaseModel):
    thumbs: Optional[bool] = None
    feedback: Optional[str] = None
    aiAnswerRevision: Optional[str] = None
    aiAnswer: Optional[str] = None
    humanQuestion: Optional[str] = None
    chatbotId: Optional[str] = None
    username: Optional[str] = None
    train: Optional[bool] = None
    session: Optional[bool] = None


class AddFeedbackInsight(BaseModel):
    question: Optional[str] = None
    insight: str
    feedback: bool
    message: Optional[str] = None


class FeedbackInsightResponse(AddFeedbackInsight):
    id: int


class FeedbackInsightListResponse(BaseModel):
    meta: MetaResponse
    data: List[FeedbackInsightResponse]
    status: str
    code: int
    message: str
