import pika
import json
import logging
import os
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional
import threading

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RabbitMQProducer:
    def __init__(self, rabbitmq_url: str = "amqp://localhost", queue_name: str = None):
        """
        Initialize the RabbitMQ producer for file events

        Args:
            rabbitmq_url: RabbitMQ connection URL
            queue_name: Name of the queue to publish to
        """
        self.rabbitmq_url = rabbitmq_url
        self.queue_name = queue_name
        self.heartbeat_interval = 10
        self.reconnect_delay = 5

        self._connection = None
        self._channel = None

        self._lock = threading.Lock()

    def _is_connected(self) -> bool:
        """Memeriksa apakah koneksi dan channel dalam keadaan baik."""
        return (
            self._connection and self._connection.is_open and
            self._channel and self._channel.is_open
        )

    def connect(self):
        """Establish connection to RabbitMQ"""

        if self._is_connected():
            return
            
        logger.info("Connecting to RabbitMQ...")

        try:
            parameters = pika.URLParameters(self.rabbitmq_url)
            parameters.heartbeat = self.heartbeat_interval
            parameters.blocked_connection_timeout = 300

            self._connection = pika.BlockingConnection(parameters)
            self._channel = self._connection.channel()

            self._channel.exchange_declare(
                exchange="file_events", exchange_type="topic", durable=True
            )
            if self.queue_name:
                self._channel.queue_declare(queue=self.queue_name, durable=True)
            
            logger.info("Successfully connected to RabbitMQ.")

        except pika.exceptions.AMQPConnectionError as e:
            logger.error(f"Failed to connect to RabbitMQ: {e}")
            self.close() # Pastikan state bersih jika gagal
            raise

    def close(self):
        """Menutup koneksi dan channel dengan aman."""
        try:
            if self._channel and self._channel.is_open:
                self._channel.close()
            if self._connection and self._connection.is_open:
                self._connection.close()
        except Exception as e:
            logger.warning(f"Error while closing RabbitMQ connection: {e}")
        finally:
            self._connection = None
            self._channel = None

    def publish_file_event(
        self,
        file_path: str,
        event_type: str,
        file_size: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Publish event dengan logika retry dan rekoneksi yang tangguh.
        """
        # Menggunakan lock untuk mencegah race condition dari thread lain
        with self._lock:
            # (Logika untuk menyiapkan message tetap sama)
            _, file_extension = os.path.splitext(file_path)
            file_extension = file_extension[1:] if file_extension else "unknown"
            routing_key = f"file.{event_type}.{file_extension}"
            message_body = json.dumps({
                "file_path": file_path, "event_type": event_type,
                "timestamp": datetime.now(timezone.utc).isoformat(), "file_size": file_size,
                "metadata": metadata or {},
            })
            properties = pika.BasicProperties(
                delivery_mode=2, content_type="application/json"
            )

            # Coba publish, jika gagal, coba rekoneksi dan ulangi
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    # 1. Cek sebelum digunakan, jika tidak terhubung, coba sambungkan
                    if not self._is_connected():
                        self.connect()

                    # 2. Coba lakukan operasi publish
                    self._channel.basic_publish(
                        exchange="file_events",
                        routing_key=routing_key,
                        body=message_body,
                        properties=properties,
                    )
                    logger.info(f"Published event for {file_path} on attempt {attempt + 1}")
                    return True # Sukses, keluar dari fungsi

                # 3. Tangani kegagalan spesifik yang menandakan koneksi putus
                except (pika.exceptions.StreamLostError, pika.exceptions.AMQPConnectionError) as e:
                    logger.warning(
                        f"Connection error on attempt {attempt + 1}/{max_retries}: {e}. "
                        f"Closing connection and retrying in {self.reconnect_delay}s..."
                    )
                    self.close() # Pastikan koneksi lama yang rusak ditutup
                    time.sleep(self.reconnect_delay)
                
                # Tangani error lain yang mungkin terjadi
                except Exception as e:
                    logger.error(f"An unexpected error occurred during publish: {e}")
                    self.close()
                    return False

            logger.error(f"Failed to publish event for {file_path} after {max_retries} attempts.")
            return False

    def publish_file_created(
        self, file_path: str, metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Publish a file creation event"""
        return self.publish_file_event(file_path, "created", metadata=metadata)

    def publish_file_modified(
        self, file_path: str, metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Publish a file modification event"""
        return self.publish_file_event(file_path, "modified", metadata=metadata)

    def publish_file_deleted(
        self, file_path: str, metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Publish a file deletion event"""
        return self.publish_file_event(
            file_path, "deleted", file_size=None, metadata=metadata
        )

    def publish_batch_events(self, events: list) -> int:
        """
        Publish multiple file events in batch

        Args:
            events: List of event dictionaries with keys: file_path, event_type, metadata

        Returns:
            int: Number of successfully published events
        """
        if not self.channel:
            self.connect()

        successful_count = 0

        for event in events:
            file_path = event.get("file_path")
            event_type = event.get("event_type")
            metadata = event.get("metadata")

            if not file_path or not event_type:
                logger.warning(f"Skipping invalid event: {event}")
                continue

            if self.publish_file_event(file_path, event_type, metadata=metadata):
                successful_count += 1

        logger.info(f"Published {successful_count}/{len(events)} events successfully")
        return successful_count

    def close_connection(self):
        """Close the RabbitMQ connection"""
        if self._connection and not self._connection.is_closed:
            self._connection.close()
            logger.info("RabbitMQ connection closed")


class FileWatcherProducer(RabbitMQProducer):
    """Extended producer with file system watching capabilities"""

    def __init__(
        self, rabbitmq_url: str = "amqp://localhost", queue_name: str = "file_events"
    ):
        super().__init__(rabbitmq_url, queue_name)
        self.watched_directories = set()

    def watch_directory(self, directory_path: str):
        """
        Watch a directory for file changes and publish events
        Note: This is a basic example. For production, use watchdog library
        """
        try:
            from watchdog.observers import Observer
            from watchdog.events import FileSystemEventHandler

            class FileEventHandler(FileSystemEventHandler):
                def __init__(self, producer):
                    self.producer = producer

                def on_created(self, event):
                    if not event.is_directory:
                        self.producer.publish_file_created(event.src_path)

                def on_modified(self, event):
                    if not event.is_directory:
                        self.producer.publish_file_modified(event.src_path)

                def on_deleted(self, event):
                    if not event.is_directory:
                        self.producer.publish_file_deleted(event.src_path)

            event_handler = FileEventHandler(self)
            observer = Observer()
            observer.schedule(event_handler, directory_path, recursive=True)
            observer.start()

            self.watched_directories.add(directory_path)
            logger.info(f"Started watching directory: {directory_path}")

            return observer

        except ImportError:
            logger.error(
                "watchdog library not installed. Install with: pip install watchdog"
            )
            return None


def main():
    """Example usage of the producer"""
    # Configuration from environment variables or defaults
    rabbitmq_url = os.getenv("RABBITMQ_URL", "amqp://localhost")
    queue_name = os.getenv("QUEUE_NAME", "file_events")

    # Create producer
    producer = RabbitMQProducer(rabbitmq_url, queue_name)

    try:
        # Example: Publish some test events
        producer.publish_file_created(
            "/path/to/test/file.txt", metadata={"source": "manual_test"}
        )
        producer.publish_file_modified(
            "/path/to/test/file.txt", metadata={"changes": "content_updated"}
        )

        # Example batch publishing
        events = [
            {"file_path": "/path/to/file1.txt", "event_type": "created"},
            {"file_path": "/path/to/file2.txt", "event_type": "modified"},
            {"file_path": "/path/to/file3.txt", "event_type": "deleted"},
        ]
        producer.publish_batch_events(events)

    except Exception as e:
        logger.error(f"Producer failed: {e}")
    finally:
        producer.close_connection()


if __name__ == "__main__":
    main()
