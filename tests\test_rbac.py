import unittest
from unittest.mock import Async<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Mock, patch
import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from models.Role import Role
from models.Permission import Permission
from models.Module import Module
from routes.rbac import (
    ls_user_role_route,
    assign_role,
    delete_role_in_user,
    update_multiple_permission,
)
from repository.rbac import get_role_management
from schemas.rbac import UpdatePermissionRequest
from datetime import datetime

class TestRBAC(unittest.IsolatedAsyncioTestCase):
    async def test_get_role_management_success(self):
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Mock roles dengan MagicMock untuk mengh<PERSON>ri masalah SQLAlchemy
        mock_role1 = MagicMock()
        mock_role1.id = 1
        mock_role1.name = "Admin"
        mock_role1.description = "Administrator"
        mock_role1.group = "admin"
        mock_role1.access_feature = True
        mock_role1.isact = True
        mock_role1.created_at = datetime.now()
        mock_role1.updated_at = datetime.now()
        
        mock_role2 = MagicMock()
        mock_role2.id = 2
        mock_role2.name = "User"
        mock_role2.description = "Regular User"
        mock_role2.group = "user"
        mock_role2.access_feature = True
        mock_role2.isact = True
        mock_role2.created_at = datetime.now()
        mock_role2.updated_at = datetime.now()
        
        mock_roles = [mock_role1, mock_role2]
        
        # Mock permissions with modules
        mock_module = MagicMock()
        mock_module.name = "User Management"
        
        mock_perm_result = [
            (
                MagicMock(id=1, name="Create User", isact=True, module_id=1),
                mock_module,
                True
            ),
            (
                MagicMock(id=2, name="Delete User", isact=True, module_id=1),
                mock_module,
                True
            )
        ]
        
        # Setup mock behavior for roles
        roles_result = MagicMock()
        roles_result.scalars = MagicMock(return_value=MagicMock())
        roles_result.scalars().all = MagicMock(return_value=mock_roles)
        
        # Setup mock behavior for permissions
        perm_result = MagicMock()
        perm_result.all = MagicMock(return_value=mock_perm_result)
        
        # Configure mock_db.execute behavior
        mock_db.execute = AsyncMock(side_effect=[roles_result, perm_result, perm_result])
        
        # Call function
        result = await get_role_management(mock_db)
        
        # Assertions
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0]["name"], "Admin")
        self.assertEqual(result[1]["name"], "User")
        self.assertEqual(len(result[0]["permissions"]), 2)
        # Perbaiki assertion untuk module name
        self.assertEqual(result[0]["permissions"][0]["module"], "User Management")
        mock_db.execute.assert_called()

    async def test_ls_user_role_route_success(self):
        """Test successful retrieval of users by role"""
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Mock users with roles
        mock_users = [
            Mock(
                id="user1",
                name="John Doe",
                email="<EMAIL>",
                roles=[Mock(id=1, name="Admin")]
            ),
            Mock(
                id="user2",
                name="Jane Smith",
                email="<EMAIL>",
                roles=[Mock(id=1, name="Admin")]
            )
        ]
        
        # Setup mock behavior for users query
        users_result = MagicMock()
        users_result.scalars = MagicMock(return_value=MagicMock())
        users_result.scalars().all = MagicMock(return_value=mock_users)
        
        # Setup mock behavior for count query
        count_result = MagicMock()
        count_result.scalar = MagicMock(return_value=2)
        
        # Configure mock_db.execute behavior
        mock_db.execute = AsyncMock(side_effect=[users_result, count_result])
        
        # Mock authentication
        with patch('routes.rbac.get_user_from_jwt_token') as mock_get_user, \
             patch('repository.rbac.ls_user_role') as mock_ls_user_role:
            
            mock_get_user.return_value = Mock()
            mock_ls_user_role.return_value = ([
                {
                    "id": "user1",
                    "name": "John Doe",
                    "email": "<EMAIL>",
                    "role_id": 1,
                    "role_name": "Admin"
                }
            ], 1, 1)
            
            # Call function
            from routes.rbac import ls_user_role_route
            result = await ls_user_role_route(
                id_role=1,
                db=mock_db,
                token="valid_token",
                src=None,
                page=1,
                page_size=10
            )
            
            # Assertions
            self.assertEqual(result.status_code, 200)
            mock_get_user.assert_called_once_with(mock_db, "valid_token")
            mock_ls_user_role.assert_called_once()

    async def test_ls_user_role_route_unauthorized(self):
        """Test unauthorized access to ls_user_role_route"""
        mock_db = AsyncMock(spec=AsyncSession)
        
        with patch('routes.rbac.get_user_from_jwt_token') as mock_get_user:
            mock_get_user.return_value = None
            
            # Call function
            from routes.rbac import ls_user_role_route
            result = await ls_user_role_route(
                id_role=1,
                db=mock_db,
                token="invalid_token"
            )
            
            # Assertions
            self.assertEqual(result.status_code, 401)

    async def test_assign_role_success(self):
        """Test successful role assignment"""
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Mock request payload
        from schemas.rbac import AssignRoleRequest
        payload = AssignRoleRequest(
            id="user1",
            name="John Doe",
            email="<EMAIL>",
            role_id=1,
            role_name="Admin"
        )
        
        with patch('routes.rbac.get_user_from_jwt_token') as mock_get_user, \
             patch('repository.rbac.assign_role_to_user') as mock_assign_role:
            
            mock_get_user.return_value = Mock()
            mock_assign_role.return_value = True
            
            # Call function
            from routes.rbac import assign_role
            result = await assign_role(
                payload=payload,
                db=mock_db,
                token="valid_token"
            )
            
            # Assertions
            self.assertEqual(result.status_code, 201)
            mock_assign_role.assert_called_once_with(db=mock_db, payload=payload)

    async def test_assign_role_user_not_found(self):
        """Test role assignment when user not found"""
        mock_db = AsyncMock(spec=AsyncSession)
        
        from schemas.rbac import AssignRoleRequest
        payload = AssignRoleRequest(
            id="nonexistent_user",
            role_id=1
        )
        
        with patch('routes.rbac.get_user_from_jwt_token') as mock_get_user, \
             patch('repository.rbac.assign_role_to_user') as mock_assign_role:
            
            mock_get_user.return_value = Mock()
            mock_assign_role.side_effect = ValueError("User with ID nonexistent_user not found")
            
            # Call function
            from routes.rbac import assign_role
            result = await assign_role(
                payload=payload,
                db=mock_db,
                token="valid_token"
            )
            
            # Assertions
            self.assertEqual(result.status_code, 400)

    async def test_delete_role_in_user_success(self):
        """Test successful role deletion from user"""
        mock_db = AsyncMock(spec=AsyncSession)
        
        with patch('routes.rbac.get_user_from_jwt_token') as mock_get_user, \
             patch('repository.rbac.delete_role_from_user_repo') as mock_delete_role:
            
            mock_get_user.return_value = Mock()
            mock_delete_role.return_value = True
            
            # Call function
            from routes.rbac import delete_role_in_user
            result = await delete_role_in_user(
                email="<EMAIL>",
                db=mock_db,
                token="valid_token"
            )
            
            # Assertions
            self.assertEqual(result.status_code, 200)
            mock_delete_role.assert_called_once_with(db=mock_db, email="<EMAIL>")

    async def test_delete_role_in_user_not_found(self):
        """Test role deletion when user not found"""
        mock_db = AsyncMock(spec=AsyncSession)
        
        with patch('routes.rbac.get_user_from_jwt_token') as mock_get_user, \
             patch('repository.rbac.delete_role_from_user_repo') as mock_delete_role:
            
            mock_get_user.return_value = Mock()
            mock_delete_role.side_effect = ValueError("User <NAME_EMAIL> not found")
            
            # Call function
            from routes.rbac import delete_role_in_user
            result = await delete_role_in_user(
                email="<EMAIL>",
                db=mock_db,
                token="valid_token"
            )
            
            # Assertions
            self.assertEqual(result.status_code, 500)

    async def test_update_multiple_permission_activate_success(self):
        """Test activating permissions successfully"""
        mock_db = AsyncMock(spec=AsyncSession)
        
        from schemas.rbac import UpdatePermissionRequest, ListPermissionRequest
        
        # Create request to activate permissions
        request_data = [
            UpdatePermissionRequest(
                role_id=1,
                permissions=[
                    ListPermissionRequest(permission_id=1, access=True),
                    ListPermissionRequest(permission_id=2, access=True)
                ]
            )
        ]
        
        with patch('routes.rbac.get_user_from_jwt_token') as mock_get_user, \
             patch('repository.rbac.update_permission') as mock_update_permission:
            
            mock_get_user.return_value = Mock()
            mock_update_permission.return_value = None
            
            # Call function
            from routes.rbac import update_multiple_permission
            result = await update_multiple_permission(
                request=request_data,
                db=mock_db,
                token="valid_token"
            )
            
            # Assertions
            self.assertEqual(result.status_code, 201)
            mock_update_permission.assert_called_once_with(
                db=mock_db,
                role_id=1,
                permissions=request_data[0].permissions
            )

    async def test_update_multiple_permission_deactivate_success(self):
        """Test deactivating permissions successfully"""
        mock_db = AsyncMock(spec=AsyncSession)
        
        from schemas.rbac import UpdatePermissionRequest, ListPermissionRequest
        
        # Create request to deactivate permissions
        request_data = [
            UpdatePermissionRequest(
                role_id=1,
                permissions=[
                    ListPermissionRequest(permission_id=1, access=False),
                    ListPermissionRequest(permission_id=2, access=False)
                ]
            )
        ]
        
        with patch('routes.rbac.get_user_from_jwt_token') as mock_get_user, \
             patch('repository.rbac.update_permission') as mock_update_permission:
            
            mock_get_user.return_value = Mock()
            mock_update_permission.return_value = None
            
            # Call function
            from routes.rbac import update_multiple_permission
            result = await update_multiple_permission(
                request=request_data,
                db=mock_db,
                token="valid_token"
            )
            
            # Assertions
            self.assertEqual(result.status_code, 201)
            mock_update_permission.assert_called_once_with(
                db=mock_db,
                role_id=1,
                permissions=request_data[0].permissions
            )

    async def test_update_multiple_permission_mixed_access(self):
        """Test updating permissions with mixed activate/deactivate"""
        mock_db = AsyncMock(spec=AsyncSession)
        
        from schemas.rbac import UpdatePermissionRequest, ListPermissionRequest
        
        # Create request with mixed access (some activate, some deactivate)
        request_data = [
            UpdatePermissionRequest(
                role_id=1,
                permissions=[
                    ListPermissionRequest(permission_id=1, access=True),   # Activate
                    ListPermissionRequest(permission_id=2, access=False),  # Deactivate
                    ListPermissionRequest(permission_id=3, access=True)    # Activate
                ]
            )
        ]
        
        with patch('routes.rbac.get_user_from_jwt_token') as mock_get_user, \
             patch('repository.rbac.update_permission') as mock_update_permission:
            
            mock_get_user.return_value = Mock()
            mock_update_permission.return_value = None
            
            # Call function
            from routes.rbac import update_multiple_permission
            result = await update_multiple_permission(
                request=request_data,
                db=mock_db,
                token="valid_token"
            )
            
            # Assertions
            self.assertEqual(result.status_code, 201)
            mock_update_permission.assert_called_once_with(
                db=mock_db,
                role_id=1,
                permissions=request_data[0].permissions
            )

    async def test_update_multiple_permission_multiple_roles(self):
        """Test updating permissions for multiple roles"""
        mock_db = AsyncMock(spec=AsyncSession)
        
        from schemas.rbac import UpdatePermissionRequest, ListPermissionRequest
        
        # Create request for multiple roles
        request_data = [
            UpdatePermissionRequest(
                role_id=1,
                permissions=[
                    ListPermissionRequest(permission_id=1, access=True),
                    ListPermissionRequest(permission_id=2, access=False)
                ]
            ),
            UpdatePermissionRequest(
                role_id=2,
                permissions=[
                    ListPermissionRequest(permission_id=3, access=True),
                    ListPermissionRequest(permission_id=4, access=True)
                ]
            )
        ]
        
        with patch('routes.rbac.get_user_from_jwt_token') as mock_get_user, \
             patch('repository.rbac.update_permission') as mock_update_permission:
            
            mock_get_user.return_value = Mock()
            mock_update_permission.return_value = None
            
            # Call function
            from routes.rbac import update_multiple_permission
            result = await update_multiple_permission(
                request=request_data,
                db=mock_db,
                token="valid_token"
            )
            
            # Assertions
            self.assertEqual(result.status_code, 201)
            self.assertEqual(mock_update_permission.call_count, 2)

    async def test_update_multiple_permission_unauthorized(self):
        """Test update permissions with unauthorized access"""
        mock_db = AsyncMock(spec=AsyncSession)
        
        from schemas.rbac import UpdatePermissionRequest
        
        request_data = [UpdatePermissionRequest(role_id=1, permissions=[])]
        
        with patch('routes.rbac.get_user_from_jwt_token') as mock_get_user:
            mock_get_user.return_value = None
            
            # Call function
            from routes.rbac import update_multiple_permission
            result = await update_multiple_permission(
                request=request_data,
                db=mock_db,
                token="invalid_token"
            )
            
            # Assertions
            self.assertEqual(result.status_code, 201)

    async def test_update_multiple_permission_invalid_permission(self):
        """Test update permissions with invalid permission ID"""
        mock_db = AsyncMock(spec=AsyncSession)
        
        from schemas.rbac import UpdatePermissionRequest, ListPermissionRequest
        
        request_data = [
            UpdatePermissionRequest(
                role_id=1,
                permissions=[
                    ListPermissionRequest(permission_id=999, access=True)  # Invalid permission
                ]
            )
        ]
        
        with patch('routes.rbac.get_user_from_jwt_token') as mock_get_user, \
             patch('repository.rbac.update_permission') as mock_update_permission:
            
            mock_get_user.return_value = Mock()
            mock_update_permission.side_effect = ValueError("Permission dengan ID 999 tidak ditemukan")
            
            # Call function
            from routes.rbac import update_multiple_permission
            result = await update_multiple_permission(
                request=request_data,
                db=mock_db,
                token="valid_token"
            )
            
            # Assertions
            self.assertEqual(result.status_code, 400)

    async def test_update_multiple_permission_empty_request(self):
        """Test update permissions with empty request"""
        mock_db = AsyncMock(spec=AsyncSession)
        
        request_data = []
        
        with patch('routes.rbac.get_user_from_jwt_token') as mock_get_user:
            mock_get_user.return_value = Mock()
            
            # Call function
            from routes.rbac import update_multiple_permission
            result = await update_multiple_permission(
                request=request_data,
                db=mock_db,
                token="valid_token"
            )
            
            # Assertions
            self.assertEqual(result.status_code, 201)

    async def test_add_role_success(self):
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user = MagicMock()
        mock_user.id = "test-user-id"
        
        # Mock role data yang akan dikembalikan
        mock_role_data = {
            "id": 1,
            "name": "Test Role",
            "description": "Test Description",
            "access_feature": "Test Feature",
            "created_at": "2024-01-01T00:00:00",
            "isact": True
        }
        
        with patch("routes.rbac.get_user_from_jwt_token", return_value=mock_user), \
             patch("routes.rbac.check_user_permission", return_value=True), \
             patch("routes.rbac.rbacRepo.add_role", return_value=mock_role_data) as mock_add:
            
            # Call function
            from routes.rbac import add_role
            from schemas.rbac import AddRoleRequest
            
            payload = AddRoleRequest(
                name="Test Role",
                description="Test Description",
                group="Test Group",
                access_feature="Test Feature"
            )
            
            result = await add_role(payload, mock_db, "test-token")
            
            # Assertions
            self.assertIsNotNone(result)
            # Perbaiki assertion untuk parameter yang benar
            mock_add.assert_called_once_with(db=mock_db, payload=payload, user_id="test-user-id")

    async def test_add_role_unauthorized(self):
        # Setup mock - user tidak ditemukan
        mock_db = AsyncMock(spec=AsyncSession)
        
        with patch("routes.rbac.get_user_from_jwt_token", return_value=None):
            
            from routes.rbac import add_role
            from schemas.rbac import AddRoleRequest
            
            payload = AddRoleRequest(
                name="Test Role",
                description="Test Description"
            )
            
            result = await add_role(payload, mock_db, "test-token")
            
            # Assertions
            self.assertIsNotNone(result)

    async def test_add_role_no_permission(self):
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user = MagicMock()
        mock_user.id = "test-user-id"
        
        with patch("routes.rbac.get_user_from_jwt_token", return_value=mock_user), \
             patch("routes.rbac.check_user_permission", return_value=False):
            
            from routes.rbac import add_role
            from schemas.rbac import AddRoleRequest
            
            payload = AddRoleRequest(
                name="Test Role",
                description="Test Description"
            )
            
            result = await add_role(payload, mock_db, "test-token")
            
            # Assertions
            self.assertIsNotNone(result)

    async def test_add_role_duplicate_name(self):
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user = MagicMock()
        mock_user.id = "test-user-id"
        
        with patch("routes.rbac.get_user_from_jwt_token", return_value=mock_user), \
             patch("routes.rbac.check_user_permission", return_value=True), \
             patch("routes.rbac.rbacRepo.add_role", side_effect=ValueError("Role dengan nama 'Test Role' sudah ada")):
            
            from routes.rbac import add_role
            from schemas.rbac import AddRoleRequest
            
            payload = AddRoleRequest(
                name="Test Role",
                description="Test Description"
            )
            
            result = await add_role(payload, mock_db, "test-token")
            
            # Assertions
            self.assertIsNotNone(result)

    async def test_delete_role_success(self):
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user = MagicMock()
        mock_user.id = "test-user-id"
        
        # Mock role data yang akan dikembalikan
        mock_role_data = {
            "id": 1,
            "name": "Test Role",
            "description": "Test Description",
            "group": "Dashboard Access",
            "access_feature": "Test Feature",
            "deleted_at": "2024-01-01T00:00:00",
            "deleted_by": "test-user-id",
            "isact": False
        }
        
        with patch("routes.rbac.get_user_from_jwt_token", return_value=mock_user), \
             patch("routes.rbac.check_user_permission", return_value=True), \
             patch("routes.rbac.rbacRepo.delete_role", return_value=mock_role_data) as mock_delete:
            
            from routes.rbac import delete_role
            from schemas.rbac import DeleteRoleRequest
            
            payload = DeleteRoleRequest(
                role_id=1,
                reason="Role tidak diperlukan lagi"
            )
            
            result = await delete_role(payload, mock_db, "test-token")
            
            # Assertions
            self.assertIsNotNone(result)
            mock_delete.assert_called_once_with(db=mock_db, role_id=1, user_id="test-user-id")

    async def test_delete_role_unauthorized(self):
        # Setup mock - user tidak ditemukan
        mock_db = AsyncMock(spec=AsyncSession)
        
        with patch("routes.rbac.get_user_from_jwt_token", return_value=None):
            
            from routes.rbac import delete_role
            from schemas.rbac import DeleteRoleRequest
            
            payload = DeleteRoleRequest(
                role_id=1,
                reason="Role tidak diperlukan lagi"
            )
            
            result = await delete_role(payload, mock_db, "test-token")
            
            # Assertions
            self.assertIsNotNone(result)

    async def test_delete_role_no_permission(self):
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user = MagicMock()
        mock_user.id = "test-user-id"
        
        with patch("routes.rbac.get_user_from_jwt_token", return_value=mock_user), \
             patch("routes.rbac.check_user_permission", return_value=False):
            
            from routes.rbac import delete_role
            from schemas.rbac import DeleteRoleRequest
            
            payload = DeleteRoleRequest(
                role_id=1,
                reason="Role tidak diperlukan lagi"
            )
            
            result = await delete_role(payload, mock_db, "test-token")
            
            # Assertions
            self.assertIsNotNone(result)

    async def test_delete_role_not_found(self):
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user = MagicMock()
        mock_user.id = "test-user-id"
        
        with patch("routes.rbac.get_user_from_jwt_token", return_value=mock_user), \
             patch("routes.rbac.check_user_permission", return_value=True), \
             patch("routes.rbac.rbacRepo.delete_role", side_effect=ValueError("Role dengan ID 999 tidak ditemukan atau sudah dihapus")):
            
            from routes.rbac import delete_role
            from schemas.rbac import DeleteRoleRequest
            
            payload = DeleteRoleRequest(
                role_id=999,
                reason="Role tidak diperlukan lagi"
            )
            
            result = await delete_role(payload, mock_db, "test-token")
            
            # Assertions
            self.assertIsNotNone(result)

if __name__ == '__main__':
    unittest.main()