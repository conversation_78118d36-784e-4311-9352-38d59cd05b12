import unittest
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Mo<PERSON>, patch
import pytest
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from routes.auth import (
    login_route
)
from models.User import User
from models.Role import Role
from schemas.auth import (
    EditPassRequest,
    EditUserRequest,
    SignUpRequest,
    LoginRequest,
    LoginSuccess
)
from core.responses import common_response, Ok, BadRequest
import repository.auth as authRepo
from core.security import generate_jwt_token_from_user, generate_jwt_token_from_user_ldap
import os

class TestAuth(unittest.IsolatedAsyncioTestCase):
    # Test for login_route (login-temp endpoint)
    @patch("routes.auth.authRepo.check_user_password")
    @patch("routes.auth.generate_jwt_token_from_user")
    @patch("routes.auth.authRepo.create_user_session")
    async def test_login_route_success(self, mock_create_session, mock_generate_token, mock_check_password):
        # Setup mocks
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user = User(id=1, email="<EMAIL>", name="Test User", isact=True)
        mock_check_password.return_value = mock_user
        mock_generate_token.return_value = "test_jwt_token"
        mock_create_session.return_value = "succes"
        
        # Create request
        request = LoginRequest(email="<EMAIL>", password="password123")
        
        # Call function
        response = await login_route(request, mock_db)
        
        # Assertions
        self.assertEqual(response.status_code, 200)
        response_data = response.body.decode()
        self.assertIn("Success login", response_data)
        self.assertIn("test_jwt_token", response_data)
        mock_check_password.assert_called_once_with(mock_db, "<EMAIL>", "password123")
        mock_generate_token.assert_called_once_with(user=mock_user)
        mock_create_session.assert_called_once_with(db=mock_db, user_id=mock_user.id, token="test_jwt_token")

    @patch("routes.auth.authRepo.check_user_password")
    async def test_login_route_invalid_credentials(self, mock_check_password):
        # Setup mocks
        mock_db = AsyncMock(spec=AsyncSession)
        mock_check_password.return_value = None
        
        # Create request
        request = LoginRequest(email="<EMAIL>", password="wrongpassword")
        
        # Call function
        response = await login_route(request, mock_db)
        
        # Assertions
        self.assertEqual(response.status_code, 400)
        response_data = response.body.decode()
        self.assertIn("Invalid Credentials", response_data)
        mock_check_password.assert_called_once_with(mock_db, "<EMAIL>", "wrongpassword")

    @patch("routes.auth.authRepo.check_user_password")
    async def test_login_route_exception(self, mock_check_password):
        # Setup mocks
        mock_db = AsyncMock(spec=AsyncSession)
        mock_check_password.side_effect = Exception("Database error")
        
        # Create request
        request = LoginRequest(email="<EMAIL>", password="password123")
        
        # Call function
        response = await login_route(request, mock_db)
        
        # Assertions
        self.assertEqual(response.status_code, 400)
        response_data = response.body.decode()
        self.assertIn("Database error", response_data)



    # Test for login_route with database session creation failure
    @patch("routes.auth.authRepo.check_user_password")
    @patch("routes.auth.generate_jwt_token_from_user")
    @patch("routes.auth.authRepo.create_user_session")
    async def test_login_route_session_creation_failure(self, mock_create_session, mock_generate_token, mock_check_password):
        # Setup mocks
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user = User(id=1, email="<EMAIL>", name="Test User", isact=True)
        mock_check_password.return_value = mock_user
        mock_generate_token.return_value = "test_jwt_token"
        mock_create_session.side_effect = ValueError("Failed to create user session")
        
        # Create request
        request = LoginRequest(email="<EMAIL>", password="password123")
        
        # Call function
        response = await login_route(request, mock_db)
        
        # Assertions
        self.assertEqual(response.status_code, 400)
        response_data = response.body.decode()
        self.assertIn("Failed to create user session", response_data)