from sqlalchemy import <PERSON>umn, Foreign<PERSON>ey, Integer, Table, String
from sqlalchemy.dialects.postgresql import UUID
from models import Base

UserRole = Table(
    "user_role",
    Base.metadata,
    Column("id", Integer, primary_key=True, nullable=False),
    <PERSON>umn("emp_id", String(36), Foreign<PERSON>ey("user.id"), nullable=False),
    <PERSON>umn("role_id", Integer, ForeignKey("role.id"), nullable=False)
)
