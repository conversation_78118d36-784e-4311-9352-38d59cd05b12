import unittest
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Mo<PERSON>, patch
import pytest
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from repository.auth import (
    add_success_user,
    change_user_password_by_token,
    generate_token_forgot_password,
    logout_user,
    generate_menu_tree_for_user,
    login_user,
    create_user_session,
    forgot_password,
    get_user_by_email,
    check_user_password,
    list_user,
    get_user_by_id,
    edit_user,
    get_role_options,
    sign_up
)
from models.User import User
from models.Role import Role
from schemas.auth import (
    EditPassRequest,
    EditUserRequest,
    SignUpRequest,
    LoginRequest
)
import os

class TestAuth(unittest.IsolatedAsyncioTestCase):
    async def test_get_user_by_email_success(self):
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user = User(
            id=1,
            email="<EMAIL>",
            name="Test User",
            isact=True
        )
        
        # Setup mock behavior
        mock_result = MagicMock()
        mock_result.scalar = Mock(return_value=mock_user)
        mock_db.execute.return_value = mock_result
        
        # Call function
        user = await get_user_by_email(mock_db, "<EMAIL>")
        
        # Assertions
        self.assertIsNotNone(user)
        self.assertEqual(user.email, "<EMAIL>")
        self.assertEqual(user.name, "Test User")
        mock_db.execute.assert_called_once()

    @patch("repository.auth.validated_user_password")
    async def test_check_user_password_success(self, mock_validate):
        # Setup mocks
        mock_db = AsyncMock(spec=AsyncSession)
        mock_validate.return_value = True
        mock_user = User(
            id=1,
            email="<EMAIL>",
            password="$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBAQHxJ5Gq8K8y", # hashed "password123"
            isact=True
        )
        
        # Setup mock behavior
        mock_result = MagicMock()
        mock_result.scalar = Mock(return_value=mock_user)
        mock_db.execute.return_value = mock_result

        # Call function
        result = await check_user_password(mock_db, "<EMAIL>", "password123")

        # Assertions
        self.assertIsNotNone(result)
        self.assertEqual(result.email, "<EMAIL>")
        mock_db.execute.assert_called_once()
        mock_validate.assert_called_once_with(mock_user.password, "password123")

    async def test_list_user_success(self):
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_users = [User(id=1, name="User 1"), User(id=2, name="User 2")]
        mock_result = MagicMock()
        mock_result.all = Mock(return_value=mock_users)
        mock_count_result = MagicMock()
        mock_count_result.scalar = Mock(return_value=2)
        # execute pertama untuk data, kedua untuk count
        mock_db.execute = AsyncMock(side_effect=[mock_result, mock_count_result])
        users, total, pages = await list_user(mock_db, page=1, page_size=10)
        
        # Assertions
        self.assertEqual(len(users), 2)
        self.assertEqual(total, 2)
        self.assertEqual(pages, 1)

    async def test_get_user_by_id_success(self):
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user = User(
            id=1,
            email="<EMAIL>",
            name="Test User",
            isact=True
        )
        
        # Setup mock behavior
        mock_db.execute.return_value.scalar_one_or_none.return_value = mock_user
        
        # Call function
        user = await get_user_by_id(mock_db, "1")
        
        # Assertions
        self.assertIsNotNone(user)
        self.assertEqual(user.id, 1)
        self.assertEqual(user.email, "<EMAIL>")
        mock_db.execute.assert_called_once()

    async def test_edit_user_success(self):
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user = User(
            id=1,
            email="<EMAIL>",
            name="Test User",
            isact=True
        )
        mock_role = Role(id=1, name="Admin", isact=True)

        # Create mock result object for db.execute
        mock_execute_result_1 = MagicMock()
        mock_execute_result_1.scalar_one_or_none = AsyncMock(return_value=mock_role)

        mock_execute_result_2 = MagicMock()
        mock_execute_result_2.scalar_one_or_none = AsyncMock(return_value=mock_user)

        # db.execute should return mock results in order (get_user_by_id, then role)
        mock_db.execute = AsyncMock(side_effect=[mock_execute_result_2, mock_execute_result_1])

        # Call function
        request = EditUserRequest(
            name="Updated User",
            phone="1234567890",
            address="New Address",
            isact=True,
            role_id=1
        )

        updated_user = await edit_user(mock_db, "1", request)

        # Assertions
        assert updated_user is not None
        assert updated_user.name == "Updated User"
        assert updated_user.phone == "1234567890"
        assert updated_user.roles[0].id == 1
    
    async def test_get_role_options_success(self):
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_roles = [
            Role(id=1, name="Admin", group="admin", isact=True),
            Role(id=2, name="User", group="user", isact=True)
        ]
        
        # Setup mock behavior
        mock_db.execute.return_value.scalars.return_value = mock_roles
        
        # Call function
        roles = await get_role_options(mock_db)
        
        # Assertions
        self.assertEqual(len(roles), 2)
        self.assertEqual(roles[0]["name"], "Admin")
        self.assertEqual(roles[1]["name"], "User")
        mock_db.execute.assert_called_once()

    async def test_sign_up_success(self):
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_role = Role(id=1, name="User", isact=True)
        
        mock_db.execute.return_value.scalar = Mock(return_value=mock_role)
        
        # Call function
        request = SignUpRequest(
            email="<EMAIL>",
            password="password123",
            name="New User",
            phone="1234567890"
        )
        result = await sign_up(mock_db, request)
        
        # Assertions
        self.assertTrue(result)
        mock_db.execute.assert_called_once()
        mock_db.add.assert_called_once()  
        mock_db.commit.assert_called_once()
        
    async def test_forgot_password_success(self):
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user = User(
            id=1,
            email="<EMAIL>",
            isact=True
        )

        # Mock result of `execute().scalar()`
        mock_result = AsyncMock()
        mock_result.scalar.return_value = mock_user
        mock_db.execute.return_value = mock_result

        # Call function
        result = await forgot_password(mock_db, "<EMAIL>")

        # Assertions
        self.assertTrue(result)
        mock_db.execute.assert_called_once()

    async def test_logout_user_success(self):
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user = User(
            id=1,
            email="<EMAIL>",
            isact=True
        )
        
        # Create mock UserToken object
        mock_user_token = MagicMock()
        mock_user_token.isact = True
        
        # Setup mock behavior
        mock_result = AsyncMock()
        mock_result.scalar = AsyncMock(return_value=mock_user_token)
        mock_db.execute.return_value = mock_result
        
        # Call function
        result = await logout_user(mock_db, mock_user, "valid_token")
        
        # Assertions
        self.assertEqual(result, "oke")
        mock_db.execute.assert_called_once()
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once() 
    
    async def test_get_user_by_email_failed(self):
        mock_db = AsyncMock(spec=AsyncSession)
        mock_result = MagicMock()
        mock_result.scalar = Mock(return_value=None)
        mock_db.execute.return_value = mock_result

        user = await get_user_by_email(mock_db, "<EMAIL>")
        self.assertIsNone(user)

    @patch("repository.auth.validated_user_password")
    async def test_check_user_password_failed_wrong_password(self, mock_validate):
        mock_db = AsyncMock(spec=AsyncSession)
        mock_validate.return_value = False
        mock_user = User(id=1, email="<EMAIL>", password="hashed", isact=True)

        mock_result = MagicMock()
        mock_result.scalar = Mock(return_value=mock_user)
        mock_db.execute.return_value = mock_result

        result = await check_user_password(mock_db, "<EMAIL>", "wrongpassword")
        self.assertIsNone(result)
        mock_validate.assert_called_once()

    async def test_forgot_password_failed_user_not_found(self):
        mock_db = AsyncMock(spec=AsyncSession)
        mock_result = MagicMock()
        mock_result.scalar = Mock(return_value=None)
        mock_db.execute.return_value = mock_result

        with self.assertRaises(ValueError) as context:
            await forgot_password(mock_db, "<EMAIL>")
    

    async def test_logout_user_failed_session_not_found(self):
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user = User(id=1, email="<EMAIL>", isact=True)

        mock_result = AsyncMock()
        mock_result.scalar = AsyncMock(return_value=None)
        mock_db.execute.return_value = mock_result

        with self.assertRaises(ValueError) as context:
            await logout_user(mock_db, mock_user, "invalid_token")

        self.assertIn("Logout Failed", str(context.exception))

    async def test_edit_user_failed_user_not_found(self):
        mock_db = AsyncMock(spec=AsyncSession)
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none = AsyncMock(return_value=None)
        mock_db.execute.return_value = mock_result

        request = EditUserRequest(name="Does Not Exist")
        result = await edit_user(mock_db, "nonexistent_id", request)

        self.assertIsNone(result)

    async def test_create_user_session_new_session(self):
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Setup mock behavior for no existing session
        mock_scalar_result = AsyncMock()
        mock_scalar_result.scalar = Mock(return_value=None)
        mock_db.execute = AsyncMock(return_value=mock_scalar_result)
        
        # Call function
        result = await create_user_session(mock_db, user_id="1", token="test_token")
        
        # Assertions
        self.assertEqual(result, "succes")
        mock_db.execute.assert_called_once()
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()

    async def test_create_user_session_existing_session(self):
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Create mock UserToken
        mock_user_token = MagicMock()
        
        # Setup mock behavior for existing session
        mock_scalar_result = AsyncMock()
        mock_scalar_result.scalar = Mock(return_value=mock_user_token)
        
        # Pastikan bahwa execute() mengembalikan mock_scalar_result
        mock_db.execute = AsyncMock(return_value=mock_scalar_result)
        
        # Call function
        result = await create_user_session(mock_db, user_id="1", token="test_token")
        
        # Assertions
        self.assertEqual(result, "succes")
        mock_db.execute.assert_called_once()
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()

    @patch("repository.auth.generate_token_forgot_password")
    async def test_generate_token_forgot_password_success(self, mock_generate_token):
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user = User(id=1, email="<EMAIL>")
        
        # Mock token generation
        mock_generate_token.return_value = "generated_token"
        
        # Call function
        result = await generate_token_forgot_password(mock_db, mock_user)
        
        # Assertions
        self.assertEqual(result, "generated_token")
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_generate_token.assert_called_once()
    
    @patch("repository.auth.generate_token_forgot_password")
    async def test_generate_token_forgot_password_error(self, mock_generate_token):
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user = User(id=1, email="<EMAIL>")
        mock_generate_token.return_value = "generated_token"
        mock_db.add = AsyncMock()
        # commit error sebelum generate_token dipanggil
        async def commit_side_effect():
            raise Exception("Database error")
        mock_db.commit = AsyncMock(side_effect=commit_side_effect)
        with self.assertRaises(ValueError) as context:
            await generate_token_forgot_password(mock_db, mock_user)
        self.assertIn("Failed to generate token forgot password", str(context.exception))
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        # Tidak perlu assert mock_generate_token karena tidak dipanggil jika commit error

    async def test_change_user_password_by_token_success(self):
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Mock data
        mock_forgot_password = MagicMock()
        mock_forgot_password.user_id = 1
        mock_forgot_password.created_date = datetime.now() - timedelta(minutes=5)  # Valid token (less than 10 minutes)
        
        mock_user = User(id=1, email="<EMAIL>")
        
        mock_result_1 = AsyncMock()
        mock_result_1.scalar = Mock(return_value=mock_forgot_password)
        
        mock_result_2 = AsyncMock()
        mock_result_2.scalar = Mock(return_value=mock_user)
        
        mock_db.execute = AsyncMock(side_effect=[mock_result_1, mock_result_2, MagicMock()])
        
        # Call function
        result = await change_user_password_by_token(mock_db, "valid_token", "new_password")
        
        # Assertions
        self.assertEqual(result, mock_user)
        self.assertEqual(mock_db.execute.call_count, 3)
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()

    async def test_change_user_password_by_token_invalid_token(self):
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)

        mock_result = MagicMock()
        mock_result.scalar = Mock(return_value=None)  
        mock_db.execute = AsyncMock(return_value=mock_result)

        # Call function
        result = await change_user_password_by_token(mock_db, "invalid_token", "new_password")

        # Assertions
        self.assertIsNone(result)
        mock_db.execute.assert_called_once()
        mock_db.add.assert_not_called()
        mock_db.commit.assert_not_called()

    async def test_change_user_password_by_token_expired_token(self):
        # Setup mock
        mock_db = AsyncMock(spec=AsyncSession)

        mock_forgot_password = MagicMock()
        mock_forgot_password.user_id = 1
        mock_forgot_password.created_date = datetime.now() - timedelta(minutes=15)  # token expired

        mock_result = MagicMock()
        mock_result.scalar = Mock(return_value=mock_forgot_password) 
        mock_db.execute = AsyncMock(return_value=mock_result)

        # Call function
        result = await change_user_password_by_token(mock_db, "expired_token", "new_password")

        # Assertions
        self.assertFalse(result)
        mock_db.execute.assert_called_once()
        mock_db.add.assert_not_called()
        mock_db.commit.assert_not_called()

    async def test_add_success_user_success(self):
        mock_db = AsyncMock(spec=AsyncSession)
        exist_result = MagicMock()
        exist_result.scalar_one_or_none = Mock(return_value=None)
        role_result = MagicMock()
        role_result.scalar = Mock(return_value=Role(id=1, name="Admin", isact=True))
        mock_db.execute = AsyncMock(side_effect=[exist_result, role_result])
        mock_db.commit = AsyncMock()
        async def refresh_side_effect(user):
            user.id = 1
            user.name = "Test User"
            user.isact = True
        mock_db.refresh = AsyncMock(side_effect=refresh_side_effect)
        result = await add_success_user(mock_db, "Test User")
        self.assertEqual(result, 1)
        mock_db.commit.assert_called()

    async def test_add_success_user_fail(self):
        mock_db = AsyncMock(spec=AsyncSession)
        mock_db.execute = AsyncMock(side_effect=Exception("DB Error"))
        with self.assertRaises(ValueError):
            await add_success_user(mock_db, "Test User")

    @patch.dict(os.environ, {"APPSNAME": "dummy", "APPSTOKEN": "dummy", "COOKIE": "dummy"})
    async def test_login_user_success(self, *_):
        mock_db = AsyncMock(spec=AsyncSession)
        with patch("repository.auth.add_success_user", AsyncMock(return_value=1)):
            mock_response = MagicMock()
            mock_response.json = Mock(return_value={"code": 200, "id": 1})
            with patch("repository.auth.httpx.AsyncClient.post", AsyncMock(return_value=mock_response)):
                result = await login_user(mock_db, "username", "password", ip_address="127.0.0.1")
                self.assertEqual(result["id"], 1)

    async def test_login_user_fail(self):
        mock_db = AsyncMock(spec=AsyncSession)
        with patch("repository.auth.httpx.AsyncClient.post", AsyncMock(side_effect=Exception("Network error"))):
            with self.assertRaises(ValueError):
                await login_user(mock_db, "username", "password", ip_address="127.0.0.1")

    async def test_generate_menu_tree_for_user_success(self):
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user = User(id=1, email="<EMAIL>")
        with patch("repository.auth.get_user_permissions", return_value=[MagicMock()]):
            with patch("repository.auth.expand_menu_tree_with_permissions", return_value=[{"id": 1, "name": "Dashboard", "url": "/dashboard", "icon": "dashboard", "is_has_child": False, "isact": True, "is_show": True, "order": 1, "sub_menu": []}]):
                with patch("repository.auth.prune_menu_tree", return_value=[{"id": 1, "name": "Dashboard", "url": "/dashboard", "icon": "dashboard", "is_has_child": False, "isact": True, "is_show": True, "order": 1, "sub_menu": []}]):
                    with patch("repository.auth.sort_menu_tree_by_order", return_value=[{"id": 1, "title": "Dashboard", "path": "/dashboard", "icon": "dashboard", "is_show": True, "sub": False}]):
                        # Perbaiki scalars().all() agar tidak coroutine
                        mock_scalars = MagicMock()
                        mock_scalars.all.return_value = [MagicMock()]
                        mock_result = MagicMock()
                        mock_result.scalars.return_value = mock_scalars
                        mock_db.execute = AsyncMock(return_value=mock_result)
                        result = await generate_menu_tree_for_user(mock_db, mock_user)
                        self.assertEqual(result[0]["title"], "Dashboard")

    async def test_generate_menu_tree_for_user_fail(self):
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user = User(id=1, email="<EMAIL>")
        with patch("repository.auth.get_user_permissions", side_effect=Exception("DB Error")):
            with self.assertRaises(ValueError):
                await generate_menu_tree_for_user(mock_db, mock_user)

    async def test_change_user_password_by_token_success(self):
        mock_db = AsyncMock(spec=AsyncSession)
        mock_forgot_password = MagicMock()
        mock_forgot_password.user_id = 1
        mock_forgot_password.created_date = datetime.now() - timedelta(minutes=5)
        mock_user = User(id=1, email="<EMAIL>")
        mock_result_1 = AsyncMock()
        mock_result_1.scalar = Mock(return_value=mock_forgot_password)
        mock_result_2 = AsyncMock()
        mock_result_2.scalar = Mock(return_value=mock_user)
        mock_db.execute = AsyncMock(side_effect=[mock_result_1, mock_result_2, MagicMock()])
        mock_db.add = AsyncMock()
        mock_db.commit = AsyncMock()
        result = await change_user_password_by_token(mock_db, "valid_token", "new_password")
        self.assertEqual(result, mock_user)
        mock_db.commit.assert_called()

    async def test_change_user_password_by_token_fail(self):
        mock_db = AsyncMock(spec=AsyncSession)
        mock_result = MagicMock()
        mock_result.scalar = Mock(return_value=None)
        mock_db.execute = AsyncMock(return_value=mock_result)
        result = await change_user_password_by_token(mock_db, "invalid_token", "new_password")
        self.assertIsNone(result)

    async def test_generate_token_forgot_password_success(self):
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user = User(id=1, email="<EMAIL>")
        with patch("repository.auth.generate_token_custom", return_value="token123"):
            mock_db.add = AsyncMock()
            mock_db.commit = AsyncMock()
            result = await generate_token_forgot_password(mock_db, mock_user)
            self.assertEqual(result, "token123")
            mock_db.add.assert_called()
            mock_db.commit.assert_called()

    async def test_generate_token_forgot_password_fail(self):
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user = User(id=1, email="<EMAIL>")
        with patch("repository.auth.generate_token_custom", return_value="token123"):
            mock_db.add = AsyncMock()
            mock_db.commit = AsyncMock(side_effect=Exception("DB Error"))
            with self.assertRaises(ValueError):
                await generate_token_forgot_password(mock_db, mock_user)

    async def test_logout_user_success(self):
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user = User(id=1, email="<EMAIL>")
        mock_user_token = MagicMock()
        mock_user_token.isact = True
        mock_result = AsyncMock()
        mock_result.scalar = AsyncMock(return_value=mock_user_token)
        mock_db.execute = AsyncMock(return_value=mock_result)
        mock_db.add = AsyncMock()
        mock_db.commit = AsyncMock()
        result = await logout_user(mock_db, mock_user, "token")
        self.assertEqual(result, "oke")
        mock_db.commit.assert_called()

    async def test_logout_user_fail(self):
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user = User(id=1, email="<EMAIL>")
        mock_result = AsyncMock()
        mock_result.scalar = AsyncMock(return_value=None)
        mock_db.execute = AsyncMock(return_value=mock_result)
        with self.assertRaises(ValueError):
            await logout_user(mock_db, mock_user, "token")

    async def test_create_user_session_success(self):
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user_token = MagicMock()
        mock_scalar_result = AsyncMock()
        mock_scalar_result.scalar = Mock(return_value=mock_user_token)
        mock_db.execute = AsyncMock(return_value=mock_scalar_result)
        mock_db.add = AsyncMock()
        mock_db.commit = AsyncMock()
        result = await create_user_session(mock_db, user_id="1", token="token")
        self.assertEqual(result, "succes")
        mock_db.commit.assert_called()

    async def test_create_user_session_fail(self):
        mock_db = AsyncMock(spec=AsyncSession)
        mock_db.execute = AsyncMock(side_effect=Exception("DB Error"))
        with self.assertRaises(ValueError):
            await create_user_session(mock_db, user_id="1", token="token")

    async def test_forgot_password_success_case(self):
        mock_db = AsyncMock(spec=AsyncSession)
        mock_user = User(id=1, email="<EMAIL>")
        mock_result = MagicMock()
        mock_result.scalar = AsyncMock(return_value=mock_user)
        mock_db.execute = AsyncMock(return_value=mock_result)
        with patch("core.mail.send_reset_password_email", AsyncMock()):
            result = await forgot_password(mock_db, "<EMAIL>")
            self.assertTrue(result)

    async def test_forgot_password_fail_case(self):
        mock_db = AsyncMock(spec=AsyncSession)
        mock_result = AsyncMock()
        mock_result.scalar = Mock(return_value=None)
        mock_db.execute = AsyncMock(return_value=mock_result)
        with self.assertRaises(ValueError):
            await forgot_password(mock_db, "<EMAIL>")
