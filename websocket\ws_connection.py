from typing import List, Optional, TypedDict
from fastapi import WebSocket
import json


class WebsocketConnection(TypedDict):
    user_id: str
    role_id: int
    websocket: WebSocket
    listen_channel: str


class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebsocketConnection] = []
        self.listen_channel: List[str] = []

    async def connect(
        self,
        websocket: WebSocket,
        listen_channel: Optional[str] = None,
        user_id: Optional[str] = None,
        role_id: int = [],
    ):
        # Regiter connection
        await websocket.accept()
        self.active_connections.append(
            {
                "websocket": websocket,
                "user_id": user_id,
                "role_id": role_id,
                "listen_channel": listen_channel,
            }
        )
        print(self.active_connections)

    def disconnect(self, websocket: WebSocket):
        self.active_connections = [
            x for x in self.active_connections if x["websocket"] != websocket
        ]
        print(self.active_connections)
    async def send_personal_message(self, data: any, user_id: str):
        print('send to personal in async not blocking')
        for connection in self.active_connections:
            if str(connection["user_id"]) == user_id:
                print('user active : ', user_id)
                if isinstance(data, str):
                    data = json.loads(data)
                await connection["websocket"].send_json(data)
    async def send_to_role(self, data: any, role_id: int):
        print('Background task : Send to role in async not blocking')
        for connection in self.active_connections:
            if connection["role_id"] == role_id:
                if isinstance(data, str):
                    data = json.loads(data)
                await connection["websocket"].send_json(data)
    async def send_to_role_exclude_sender(
            self, 
            data: any, 
            role_id: int,
            user_id: str
            ):
        print('send to role without sender in async not blocking')
        print(self.active_connections)
        for connection in self.active_connections:
            if connection["role_id"] == role_id and str(connection["user_id"]) != user_id:
                if isinstance(data, str):
                    data = json.loads(data)
                await connection["websocket"].send_json(data)
    async def send_to_role_test(self, data: any, role_id: int):
        for connection in self.active_connections:
            if connection["role_id"] == role_id:
                if isinstance(data, str):
                    data = json.loads(data)
                await connection["websocket"].send_json(data)

    async def send_to_channel(self, listen_channel: str, message: str):
        for connection in self.active_connections:
            if connection["listen_channel"] == listen_channel:
                # await connection["websocket"].send_text(message)
                data = {"data": message}
                await connection["websocket"].send_json(data)
        print(self.active_connections)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            await connection["websocket"].send_text(message)
    async def clear_connection(self):
        for connection in self.active_connections:
            await connection["websocket"].close()
        self.active_connections = []
    async def clear_connection_by_id(self, user_id: str):
        for connection in self.active_connections:
            await connection["websocket"].close()
        self.active_connections = []
    async def clear_connection_by_id(self, user_id: str):
        try:
            print('Start clear connection')
            for connection in self.active_connections:
                if str(connection["user_id"]) == str(user_id):
                    await connection["websocket"].close()
            self.active_connections = [
                x for x in self.active_connections if str(x["user_id"]) != str(user_id)
            ]
        except Exception as e:
            print('Errror clear connection by id : ',e)



ws_manager = ConnectionManager()

