#!/usr/bin/env python3
"""
Script untuk menambahkan keywords default ke database
Jalankan setelah migrasi database: python seed_keywords.py
"""

import asyncio
from models import async_session
from models.Keywords import Keywords
from sqlalchemy import select

async def seed_keywords():
    """Menambahkan keywords default ke database"""
    
    # Keywords default untuk BigSocial crawling
    default_keywords = [
        {
            "keyword": "telkom",
            "description": "Keyword untuk Telkom Indonesia",
            "isact": True
        },
        {
            "keyword": "indihome",
            "description": "Keyword untuk IndiHome",
            "isact": True
        },
        {
            "keyword": "firstmedia",
            "description": "Keyword untuk First Media",
            "isact": True
        },
        {
            "keyword": "biznet",
            "description": "Keyword untuk Biznet",
            "isact": True
        },
        {
            "keyword": "xl",
            "description": "Keyword untuk XL Axiata",
            "isact": True
        },
        {
            "keyword": "tri",
            "description": "Keyword untuk 3 Indonesia",
            "isact": True
        },
        {
            "keyword": "smartfren",
            "description": "Keyword untuk Smartfren",
            "isact": True
        }
    ]
    
    try:
        async with async_session() as session:
            # Check existing keywords
            result = await session.execute(select(Keywords))
            existing_keywords = result.scalars().all()
            existing_keyword_names = [kw.keyword for kw in existing_keywords]
            
            # Add new keywords
            added_count = 0
            for keyword_data in default_keywords:
                if keyword_data["keyword"] not in existing_keyword_names:
                    keyword = Keywords(**keyword_data)
                    session.add(keyword)
                    added_count += 1
                    print(f"Menambahkan keyword: {keyword_data['keyword']}")
                else:
                    print(f"Keyword {keyword_data['keyword']} sudah ada, skip")
            
            await session.commit()
            print(f"\nBerhasil menambahkan {added_count} keywords baru")
            print(f"Total keywords di database: {len(existing_keywords) + added_count}")
            
    except Exception as e:
        print(f"Error saat seeding keywords: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(seed_keywords()) 