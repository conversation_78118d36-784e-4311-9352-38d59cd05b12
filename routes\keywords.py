from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from models import get_db
from schemas.keywords import KeywordCreate, KeywordUpdate, KeywordResponse
from typing import List
from core.logging_config import logger
from core.responses import (
    common_response,
    Ok,
    CudResponse,
    BadRequest,
    Unauthorized,
    NotFound,
    InternalServerError,
)
from schemas.common import (
    BadRequestResponse,
    UnauthorizedResponse,
    NotFoundResponse,
    InternalServerErrorResponse,
    CudResponseSchema,
)
import repository.keywords as keywordsRepo
from core.security import check_user_permission, get_user_from_jwt_token, oauth2_scheme

router = APIRouter(tags=["Keywords"])


@router.get(
    "/",
    response_model=List[KeywordResponse],
    responses={
        200: {"model": List[KeywordResponse]},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def get_keywords(db: AsyncSession = Depends(get_db), token: str = Depends(oauth2_scheme)):
    """Mendapatkan semua keywords"""
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        permission_user = check_user_permission(db, user, "view", "Keywords")
        if not permission_user:
            return common_response(Unauthorized())
        data = await keywordsRepo.get_all_keywords(db)
        return common_response(Ok(data=data))
    except ValueError as e:
        return common_response(BadRequest(message=str(e)))
    except Exception as e:
        logger.error(f"Error getting keywords: {e}")
        return common_response(InternalServerError())


@router.get(
    "/active",
    response_model=List[KeywordResponse],
    responses={
        200: {"model": List[KeywordResponse]},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def get_active_keywords(db: AsyncSession = Depends(get_db), token: str = Depends(oauth2_scheme)):
    """Mendapatkan keywords yang aktif"""
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        permission_user = check_user_permission(db, user, "view", "Keywords")
        if not permission_user:
            return common_response(Unauthorized())
        data = await keywordsRepo.get_active_keywords(db)
        return common_response(Ok(data=data))
    except ValueError as e:
        return common_response(BadRequest(message=str(e)))
    except Exception as e:
        logger.error(f"Error getting active keywords: {e}")
        return common_response(InternalServerError())


@router.post(
    "/",
    response_model=KeywordResponse,
    responses={
        201: {"model": KeywordResponse},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def create_keyword(
    keyword_data: KeywordCreate,
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Membuat keyword baru"""
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        permission_user = check_user_permission(db, user, "create", "Keywords")
        if not permission_user:
            return common_response(Unauthorized())
        data = await keywordsRepo.create_keyword(db, keyword_data)
        return common_response(
            CudResponse(
                data=data,
                message="Keyword berhasil dibuat"
            )
        )
    except ValueError as e:
        return common_response(BadRequest(message=str(e)))
    except Exception as e:
        logger.error(f"Error creating keyword: {e}")
        return common_response(InternalServerError())


@router.put(
    "/{keyword_id}",
    response_model=KeywordResponse,
    responses={
        200: {"model": KeywordResponse},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def update_keyword(
    keyword_id: str,  # Changed from int to str
    keyword_data: KeywordUpdate,
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Mengupdate keyword"""
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        permission_user = check_user_permission(db, user, "update", "Keywords")
        if not permission_user:
            return common_response(Unauthorized())
        data = await keywordsRepo.update_keyword(db, keyword_id, keyword_data)
        return common_response(
            CudResponse(
                data=data,
                message="Keyword berhasil diupdate"
            )
        )
    except ValueError as e:
        return common_response(BadRequest(message=str(e)))
    except Exception as e:
        logger.error(f"Error updating keyword: {e}")
        return common_response(InternalServerError())


@router.delete(
    "/{keyword_id}",
    responses={
        200: {"model": CudResponseSchema},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def delete_keyword(
    keyword_id: str,  # Changed from int to str
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Menghapus keyword"""
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        permission_user = check_user_permission(db, user, "delete", "Keywords")
        if not permission_user:
            return common_response(Unauthorized())
        await keywordsRepo.delete_keyword(db, keyword_id)
        return common_response(
            CudResponse(message="Keyword berhasil dihapus")
        )
    except ValueError as e:
        return common_response(BadRequest(message=str(e)))
    except Exception as e:
        logger.error(f"Error deleting keyword: {e}")
        return common_response(InternalServerError()) 
