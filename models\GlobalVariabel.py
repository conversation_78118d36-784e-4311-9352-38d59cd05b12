from sqlalchemy import Column, Integer, String, DateTime, Boolean
from sqlalchemy.sql import func
from models import Base

class GlobalVariabel(Base):
    __tablename__ = "global_variabel"

    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    name = Column(String, nullable=False)
    value = Column(String, nullable=True)
    group = Column(String, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=True)
    isact = Column(Boolean, nullable=True)
    satuan = Column(String, nullable=True)