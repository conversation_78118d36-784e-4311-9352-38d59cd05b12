from pydantic import BaseModel
from typing import Optional
from datetime import datetime


class KeywordBase(BaseModel):
    keyword: str
    description: Optional[str] = None
    isact: bool = True


class KeywordCreate(KeywordBase):
    pass


class KeywordUpdate(BaseModel):
    keyword: Optional[str] = None
    description: Optional[str] = None
    isact: Optional[bool] = None


class KeywordResponse(KeywordBase):
    id: str  # Changed from int to str to match UUID
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True 
