from sqlalchemy import Integer, Column, String, Foreign<PERSON>ey, <PERSON><PERSON>an, DateTime
from sqlalchemy.orm import relationship
from models import Base


class FeedbackInsight(Base):
    __tablename__ = "feedback_insight"

    id = Column("id", Integer, primary_key=True, nullable=False, index=True)
    question = Column("question", String, nullable=False)
    insight = Column("insight", String, nullable=False)
    message = Column("message", String, nullable=False)
    feedback = Column("feedback", Boolean, default=False)
    created_at = Column("created_at", DateTime(timezone=True))
    updated_at = Column("updated_at", DateTime(timezone=True))
