import asyncio
import threading
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import sentry_sdk
from core.myworker import run_scheduled_task, run_scheduled_task_1
from contextlib import asynccontextmanager
from fastapi_utilities import repeat_at
from core.security_headers import SecurityHeadersMiddleware
from services.rabbitmq_producer import RabbitMQProducer
from services.qdrant_client import QdrantService
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from models import get_db, GlobalVariabel
from settings import (
    SERVICE_NAME,
    VECTOR_DB_URL,
    VECTOR_DB_API_KEY,
)
from settings import (
    CORS_ALLOWED_ORIGINS,
    RABBITMQ_QUEUE_NAME,
    RABBITMQ_URL,
    SENTRY_DSN,
    SENTRY_TRACES_SAMPLE_RATES,
    VECTOR_COLLECTION_NAME,
    FILE_STORAGE_ADAPTER,
    EN<PERSON>RONT<PERSON>NT,
    LOCAL_PATH
)
from core.logging_config import logger
from routes.auth import router as auth_router
from routes.rbac import router as rbac_router
from routes.notification import router as notification_router
from routes.mywebsocket import router as mywebsocket_router
from routes.feedbackinsight import router as feedbackinsight_router
from routes.modelmanagement import router as modelmanagement_router
from routes.keywords import router as keywords_router
from routes.predictive_analytics import router as predictive_analytics_router
from fastapi.responses import HTMLResponse, JSONResponse
import os


if SENTRY_DSN != None:  # NOQA
    sentry_sdk.init(
        dsn=SENTRY_DSN,
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for performance monitoring.
        # We recommend adjusting this value in production,
        traces_sample_rate=SENTRY_TRACES_SAMPLE_RATES,
    )


# TARUH CONJOB DISINI YA BANG SEMUANYAA BIG HUGG
@asynccontextmanager
async def lifespan(app: FastAPI):
    # --- startup ---
    global rabbitmq_producer, consumer_thread, qdrant_service

    logger.info("Starting RabbitMQ services...")

    if not os.path.exists(LOCAL_PATH):
        print(f"Creating directory: {LOCAL_PATH}")
        os.makedirs(LOCAL_PATH, exist_ok=True)

    try:
        rabbitmq_producer = RabbitMQProducer(RABBITMQ_URL, RABBITMQ_QUEUE_NAME)
        rabbitmq_producer.connect()
        logger.info("RabbitMQ producer initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize RabbitMQ producer: {e}")

    # try:

    #     def start_consumer():
    #         consumer = FileEventConsumer(
    #             rabbitmq_url=RABBITMQ_URL,
    #             service_name=SERVICE_NAME,
    #         )
    #         consumer.start_consuming()

    #     consumer_thread = threading.Thread(target=start_consumer, daemon=True)
    #     consumer_thread.start()
    #     logger.info("RabbitMQ consumer started in background thread")
    # except Exception as e:
    #     logger.error(f"Failed to start RabbitMQ consumer: {e}")

    logger.info("Starting Qdrant service...")
    try:
        qdrant_service = QdrantService(
            url=VECTOR_DB_URL,
            https=True,
        )
        if qdrant_service.connect():
            logger.info("Qdrant service initialized successfully")
            # qdrant_service.create_hybrid_collection(
            #     collection_name=VECTOR_COLLECTION_NAME,
            #     vector_params={
            #         "size": 768,  # Example size for dense vectors
            #         "distance": "Cosine"
            #     },
            #     late_size=128,  # Example size for late interaction vectors
            #     model_name="nomic-embed-text-v2-moe"
            # )
        else:
            logger.warning("Failed to connect to Qdrant, service will not be available")
    except Exception as e:
        logger.error(f"Failed to initialize Qdrant service: {e}")
        qdrant_service = None
    # test push

    # await scheduled_task()
    scheduled_task_1()
    scheduled_bigsocial_crawl()  # Initialize BigSocial scheduler
    
    logger.info("Setting up dynamic cron scheduler...")
    create_dynamic_scheduler()
    
    yield

    logger.info("Shutting down RabbitMQ services...")

    if rabbitmq_producer:
        rabbitmq_producer.close_connection()
        logger.info("RabbitMQ producer connection closed")

    if qdrant_service:
        qdrant_service.close_connection()
        logger.info("Qdrant service connection closed")
    # --- shutdown ---


# Inisialisasi FastAPI berdasarkan ENVIRONTMENT
fastapi_kwargs = {
    "title": "Telkom AI",
    "swagger_ui_oauth2_redirect_url": "/docs/oauth2-redirect",
    "swagger_ui_init_oauth": {
        "clientId": "your-client-id",
        "authorizationUrl": "/auth/token",
        "tokenUrl": "/auth/token",
    },
    "lifespan": lifespan,  # Add the lifespan handler
}
if ENVIRONTMENT == "dev":
    fastapi_kwargs.update(
        {
            "docs_url": "/docs",
            "redoc_url": None,
            "openapi_url": "/openapi.json",
        }
    )
elif ENVIRONTMENT == "prod":
    fastapi_kwargs.update(
        {
            "docs_url": None,
            "redoc_url": None,
            "openapi_url": None,
        }
    )
app = FastAPI(**fastapi_kwargs)

app.add_middleware(SecurityHeadersMiddleware)
# TARUH CONJOB DISINI YA BANG SEMUANYAA BIG HUGG

async def get_cron_config_from_db():
    """
    Fungsi untuk membaca konfigurasi cron dari tabel global_variabel
    """
    try:
        async for db in get_db():
            query = select(GlobalVariabel).where(
                GlobalVariabel.name.like("cron_%"),
                GlobalVariabel.isact == True
            )
            result = await db.execute(query)
            cron_configs = result.scalars().all()
            
            config_dict = {}
            for config in cron_configs:
                task_name = config.name.replace("cron_", "")
                config_dict[task_name] = {
                    "cron": config.value,
                    "group": config.group,
                    "description": config.satuan if config.satuan else ""
                }
            
            await db.close()
            return config_dict
    except Exception as e:
        logger.error(f"Error reading cron config from database: {e}")
        return {}

def create_dynamic_scheduler():
    """
    Fungsi untuk membuat scheduler secara dinamis berdasarkan konfigurasi dari database
    """
    async def setup_scheduler():
        cron_configs = await get_cron_config_from_db()
        
        if not cron_configs:
            logger.warning("No cron configurations found in database, using default schedules")
            return
        
        logger.info(f"Setting up dynamic scheduler with {len(cron_configs)} tasks")
        
        for task_name, config in cron_configs.items():
            try:
                cron_expression = config["cron"]
                logger.info(f"Setting up scheduler for {task_name} with cron: {cron_expression}")
                
                # Buat decorator repeat_at secara dinamis
                if task_name == "notif_data":
                    @repeat_at(cron=cron_expression)
                    def scheduled_task_1():
                        logger.info(f"Running scheduled {task_name} with cron: {cron_expression}")
                        run_scheduled_task_1(func_name="get_notif_data")
                        print(f"Task {task_name} completed!")
                        
                elif task_name == "cron_get_data_from_db":
                    @repeat_at(cron=cron_expression)
                    def scheduled_task_1():
                        logger.info(f"Running scheduled {task_name} with cron: {cron_expression}")
                        run_scheduled_task_1(func_name="get_data_from_tabel")
                        print(f"Task {task_name} completed!")
                
                elif task_name == "bigsocial_crawl":
                    @repeat_at(cron=cron_expression)
                    def scheduled_bigsocial_crawl():
                        logger.info(f"Running scheduled {task_name} with cron: {cron_expression}")
                        run_scheduled_task_1(func_name="get_bigsocial_crawl_and_save_pdf")
                        print(f"Task {task_name} completed!")
                
                elif task_name == "predictive_analytics_retrain":
                    @repeat_at(cron=cron_expression)
                    def scheduled_predictive_analytics_retrain():
                        logger.info(f"Running scheduled {task_name} with cron: {cron_expression}")
                        from services.predictive_analytics_retrain_service import run_predictive_analytics_retrain_sync
                        run_predictive_analytics_retrain_sync()
                        print(f"Task {task_name} completed!")
                
                else:
                    logger.warning(f"Unknown task name: {task_name}, skipping...")
                    
            except Exception as e:
                logger.error(f"Error setting up scheduler for {task_name}: {e}")
    
    # Jalankan setup scheduler
    asyncio.create_task(setup_scheduler())

# Default scheduler (akan di-override jika ada konfigurasi di database)
@repeat_at(cron="2 5 10 * *")  # every first day of the month at 23:02
def scheduled_task_1():
    logger.info("Running scheduled get_notif_data (default)")
    run_scheduled_task_1(func_name="get_notif_data")
    print("Get Notification Data task run")
    
@repeat_at(cron="2 6 10 * *")  # every first day of the month at 23:02
def scheduled_task_2():
    logger.info("Running scheduled get_data_from_tabel (default)")
    run_scheduled_task_1(func_name="get_data_from_tabel")
    print("Get Data From Tabel task run")

# Scheduler untuk BigSocial crawling - setiap tanggal 1 bulan pukul 07:00
@repeat_at(cron="0 7 1 * *")  # every first day of the month at 07:00
def scheduled_bigsocial_crawl():
    logger.info("Running scheduled BigSocial crawl and PDF generation (default)")
    run_scheduled_task_1(func_name="get_bigsocial_crawl_and_save_pdf")
    print("BigSocial crawl & PDF task created!")

# Scheduler untuk Predictive Analytics Retrain - setiap tanggal 1 bulan pukul 02:00
@repeat_at(cron="0 2 10 * *")  # every first day of the month at 02:00
def scheduled_predictive_analytics_retrain():
    logger.info("Running scheduled Predictive Analytics retrain and prediction (default)")
    from services.predictive_analytics_retrain_service import run_predictive_analytics_retrain_sync
    run_predictive_analytics_retrain_sync()
    print("Predictive Analytics retrain task completed!")


app.add_middleware(
    CORSMiddleware,
    # allow_origins=CORS_ALLOWED_ORIGINS,
    allow_origins=[
        "https://dcsdashboarddev.apps.kpaasjt21.telkom.co.id",
        "https://dcsdashboard.apps.kpaasjt21.telkom.co.id",
        "http://localhost:3000",
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
if FILE_STORAGE_ADAPTER != "minio" and FILE_STORAGE_ADAPTER != "obs":
    app.mount("/static", StaticFiles(directory="static"))


app.include_router(auth_router, prefix="/auth")
app.include_router(rbac_router, prefix="/rbac")
app.include_router(notification_router, prefix="/notification")
# app.include_router(mywebsocket_router, prefix="/web-socket")
# app.include_router(feedbackinsight_router, prefix="/feedback-insight")
app.include_router(modelmanagement_router, prefix="/model-management")
# app.include_router(keywords_router, prefix="/keywords")
app.include_router(predictive_analytics_router, prefix="/predictive-analytics")


@app.get("/")
async def hello():
    html_content = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>Telkom AI dashboard</title>
        <style>
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: #fff;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 100vh;
                margin: 0;
            }
            .container {
                background: rgba(0,0,0,0.3);
                padding: 40px 60px;
                border-radius: 16px;
                box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
                text-align: center;
            }
            h1 {
                margin-bottom: 10px;
                font-size: 2.5rem;
                letter-spacing: 2px;
            }
            p {
                font-size: 1.2rem;
                margin-top: 0;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>👋 Welcome to Our Backend Service API</h1>
            <p>FastAPI + PostgreSQL + Sentry + CORS</p>
            <p>Happy Code</p>
            <p>Bismillah bro</p>
        </div>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@app.get("/test-bigsocial-scheduler")
async def trigger_test_bigsocial_scheduler():
    """
    Endpoint sementara untuk testing scheduler BigSocial crawling secara manual.
    """
    try:
        logger.info("Manually triggering BigSocial crawl and PDF generation for testing.")
        from core.myworker import get_bigsocial_crawl_and_save_pdf
        await get_bigsocial_crawl_and_save_pdf()
        return {"status": "success", "message": "BigSocial scheduler triggered. Check console and the storage folder."}
    except Exception as e:
        logger.error(f"Error during manual BigSocial scheduler test: {e}")
        return {"status": "error", "message": str(e)}


# @app.get("/health/obs")
# async def check_obs_health():
#     """
#     Endpoint untuk mengecek kesehatan koneksi OBS
#     """
#     try:
#         from core.file import check_obs_connection
#         result = check_obs_connection()
        
#         # Tentukan HTTP status code berdasarkan hasil
#         if result["status"] == "success":
#             status_code = 200
#         else:
#             status_code = 503  # Service Unavailable
        
#         return JSONResponse(
#             status_code=status_code,
#             content={
#                 "obs_status": result["status"],
#                 "message": result["message"],
#                 "details": {
#                     "can_list_buckets": result["can_list_buckets"],
#                     "bucket_exists": result["bucket_exists"],
#                     "can_access_bucket": result["can_access_bucket"],
#                     "error_details": result["error_details"]
#                 }
#             }
#         )
        
#     except Exception as e:
#         logger.error(f"Error during OBS health check: {str(e)}")
#         return JSONResponse(
#             status_code=500,
#             content={
#                 "obs_status": "error",
#                 "message": f"Internal server error: {str(e)}",
#                 "details": {"error_details": str(e)}
#             }
#         )


DB_USER = os.environ.get("DB_USER")
DB_PASS = os.environ.get("DB_PASS")
DB_HOST = os.environ.get("DB_HOST")
DB_PORT = os.environ.get("DB_PORT")
DB_NAME = os.environ.get("DB_NAME")
