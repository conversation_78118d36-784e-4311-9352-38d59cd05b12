from typing import Any, Dict, List, Optional
import numpy as np
from openai import OpenAI
from llama_index.core.base.embeddings.base import BaseEmbedding
from llama_index.core.callbacks import CallbackManager
from llama_index.core.bridge.pydantic import Field

from settings import APILOGY_KEY


class ApilogyEmbedding(BaseEmbedding):
    """Custom embedding class for Apilogy API."""

    api_key: str = Field(default=APILOGY_KEY, description="Your Apilogy API key")
    base_url: str = Field(
        default="https://telkom-ai-dag.api.apilogy.id/Text_Embedding/0.0.1/v1",
        description="The API endpoint URL",
    )
    model: str = Field(
        default="nomic-embed-text-v2-moe", description="The embedding model to use"
    )
    client: Any = Field(default=None, exclude=True)

    def __init__(
        self,
        api_key: str = APILOGY_KEY,
        base_url: str = "https://telkom-ai-dag.api.apilogy.id/Text_Embedding/0.0.1/v1",
        model: str = "nomic-embed-text-v2-moe",
        embed_batch_size: int = 100,
        callback_manager: Optional[CallbackManager] = None,
    ) -> None:
        """Initialize ApilogyEmbedding.

        Args:
            api_key: Your Apilogy API key
            base_url: The API endpoint URL
            model: The embedding model to use
            embed_batch_size: Number of embeddings to get in a single API call
        """
        super().__init__(
            embed_batch_size=embed_batch_size,
            callback_manager=callback_manager,
            model_name=model,
        )
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.client = OpenAI(
            api_key="dummy-key",
            base_url=base_url,
            default_headers={"x-api-key": api_key},
        )

    @classmethod
    def class_name(cls) -> str:
        return "ApilogyEmbedding"

    def _get_text_embedding(self, text: str) -> List[float]:
        """Get embedding for a single text."""
        response = self.client.embeddings.create(
            input=text,
            model=self.model,
            encoding_format="float",
        )
        return response.data[0].embedding

    async def _aget_text_embedding(self, text: str) -> List[float]:
        """Get embedding for a single text asynchronously."""
        return self._get_text_embedding(text)

    def _get_text_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Get embeddings for multiple texts."""
        results = []
        for i in range(0, len(texts), self.embed_batch_size):
            batch = texts[i : i + self.embed_batch_size]
            response = self.client.embeddings.create(
                input=batch,
                model=self.model,
                encoding_format="float",
            )
            results.extend([data.embedding for data in response.data])
        return results

    async def _aget_text_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Get embeddings for multiple texts asynchronously."""
        return self._get_text_embeddings(texts)

    def _get_query_embedding(self, query: str) -> List[float]:
        """Get embedding for a query text.

        For Apilogy, query embeddings use the same method as text embeddings.
        """
        return self._get_text_embedding(query)

    async def _aget_query_embedding(self, query: str) -> List[float]:
        """Get embedding for a query text asynchronously.

        For Apilogy, query embeddings use the same method as text embeddings.
        """
        return await self._aget_text_embedding(query)

    def similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """Calculate cosine similarity between two embeddings."""
        # Convert to numpy arrays for easier calculation
        vec1 = np.array(embedding1)
        vec2 = np.array(embedding2)

        # Calculate cosine similarity
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)

        # Return cosine similarity
        return dot_product / (norm1 * norm2)
