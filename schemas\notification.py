from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime

class MetaResponse(BaseModel):
    count:int
    page_count:int
    page_size:int
    page:int


class ListNotif(BaseModel):
    id: int
    title: Optional[str]=''
    message: Optional[str]=''
    description: Optional[str]=''

class ListNotifResponse(BaseModel):
    meta: MetaResponse
    data: List[ListNotif]
    status: str
    code: int
    message: str

class CoutNotif(BaseModel):
    total: Optional[int] = 0

class CountNotifResponse(BaseModel):
    meta: MetaResponse
    data: CoutNotif
    status: str
    code: int
    message: str

class PdfResponse(BaseModel):
    title: str
    pdf_base64: str

class PdfResponseSchema(BaseModel):
    data: PdfResponse
    status: str
    code: int
    message: str
