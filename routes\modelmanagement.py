import datetime
from core.file import (
    delete_file,
    delete_file_from_obs,
    upload_file as upload,
)
from core.responses import (
    common_response,
    Ok,
    CudResponse,
    BadRequest,
    Unauthorized,
    NotFound,
    InternalServerError,
)
from schemas.common import (
    BadRequestResponse,
    UnauthorizedResponse,
    NotFoundResponse,
    InternalServerErrorResponse,
    CudResponseSchema,
)
from models import get_db
from core.file import save_uploaded_file_to_db, update_file_status
from fastapi import APIRouter, Depends, BackgroundTasks, UploadFile, File, Form, Request
from sqlalchemy.ext.asyncio import AsyncSession
from core.security import (
    get_user_from_jwt_token,
    oauth2_scheme,
)
from core.logging_config import logger
import io

import repository.modelmanagement as modelmanagement_repo
from schemas.modelmanagement import ListUploadedFileResponse
from settings import FILE_STORAGE_ADAPTER, OBS_BUCKET

router = APIRouter(tags=["Model Management"])


@router.get(
    "/files",
    responses={
        200: {"model": ListUploadedFileResponse},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def list_uploaded_files(
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    page: int = 1,
    page_size: int = 10,
):
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())

        data, num_data, num_page = await modelmanagement_repo.get_uploaded_files(
            db=db,
            user_id=user.id,
            page=page,
            page_size=page_size,
        )

        # Simulating pagination metadata
        meta = {
            "count": num_data,
            "page_count": num_page,
            "page_size": page_size,
            "page": page,
        }

        return ListUploadedFileResponse(
            meta=meta,
            data=data,
            status="success",
            code=200,
            message="Files retrieved successfully",
        )

    except Exception as e:
        return InternalServerError(message=str(e))


@router.post(
    "/upload",
    responses={
        201: {"model": CudResponseSchema},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def upload_file(
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    file: UploadFile = File(),
):
    obs_object_path = None

    accepted_file_types = ['.xls', '.xlsx', '.txt', '.pdf']
    if not file.filename or not any(file.filename.endswith(ext) for ext in accepted_file_types):
        return common_response(
            BadRequest(message="Invalid file type. Only .xls, .xlsx, .txt, .pdf are allowed.")
        )

    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        
        obs_response = await upload(
            upload_file=file,
            path=f"{file.filename}",
        )

        await file.seek(0)
        file_content = await file.read()

        if not obs_response:
            return common_response(BadRequest(message="Failed to upload file to obs"))

        print(f"\n\nFile uploaded to OBS: {obs_response}\n\n")
        
        obs_object_path = file.filename

        file_id = save_uploaded_file_to_db(
                    directory="documents",
                    filename= file.filename,
                    filesize=len(file_content),
                    filetype=file.content_type,
                    user_id=user.id,
                    status="processing"
                )
        try:
            from main import rabbitmq_producer

            if not rabbitmq_producer:
                logger.warning(
                    "RabbitMQ producer not initialized, skipping message publishing"
                )
            else:
                rabbitmq_result = rabbitmq_producer.publish_file_created(
                    file_path=file.filename,
                    metadata={
                        "file_id": file_id,
                        "doc_type": "lapeks_insight",
                        "filename": file.filename,
                        "content_type": file.content_type,
                        "size": len(file_content),
                        "user_id": user.id,
                        "storage": FILE_STORAGE_ADAPTER,
                        "timestamp": str(datetime.datetime.now()),
                    },
                )

                logger.info(f"RabbitMQ publish result: {rabbitmq_result}")
                update_file_status(
                    file_id=file_id,
                    status="processing",
                    message="Queued for processing document"
                )

                if not rabbitmq_result:
                    raise Exception("Failed to publish to message queue")

        except Exception as e:
            if obs_object_path:
                logger.error(f"Error saving file metadata: {e}")
                await delete_file(path=f"{obs_object_path}")
            raise e

        return Ok(
            message="File uploaded successfully", data={"filename": file.filename}
        )

    except Exception as e:
        if obs_object_path:
            try:
                print(f"Error during file upload: {obs_object_path}")
                delete_file_from_obs(
                    bucket=OBS_BUCKET, filename=f"{obs_object_path}"
                )
            except Exception as cleanup_error:
                logger.error(f"Failed to clean up obs file: {cleanup_error}")

        return InternalServerError(message=str(e))
