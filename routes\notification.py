import traceback
from typing import List, Optional
from fastapi import APIRouter, Depends, Request, BackgroundTasks, UploadFile, File, Form, Request
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy import func, select
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from models.Notifications import Notifications
from core.responses import (
    common_response,
    Ok,
    CudResponse,
    BadRequest,
    Unauthorized,
    NotFound,
    InternalServerError,
)
from models import get_db
from core.security import check_user_permission, get_user_from_jwt_token, generate_jwt_token_from_user, get_user_permissions
from core.security import (
    get_user_from_jwt_token,
    oauth2_scheme,
)
from schemas.common import (
    BadRequestResponse,
    UnauthorizedResponse,
    NotFoundResponse,
    InternalServerErrorResponse,
    CudResponseSchema,
)
from core.myworker import get_data_from_tabel
from schemas.notification import CountNotifResponse, CoutNotif, ListNot<PERSON>, ListNotifResponse, PdfResponse, PdfResponseSchema
from core.pdf_generator import generate_notification_pdf


router = APIRouter(tags=["Notification"])



# buat api triger notifikasi dengan cara memanggi fungsi ini get_data_from_tabel
# berikan method post untuk ini
# jangan gunakan jwt

@router.post(
    "/trigger-notification",
    responses={
        200: {"model": CudResponseSchema},
        400: {"model": BadRequestResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def trigger_notification_route(
    db: AsyncSession = Depends(get_db)
):
    try:
        
        # Call the function to generate notifications
        result = get_data_from_tabel()
        
        if result.get("error"):
            return common_response(
                BadRequest(message=result.get("message", "Failed to trigger notification"))
            )
        
        return common_response(
            CudResponse(
                message="Notifications triggered successfully",
                data={
                    "processed_month": result.get("month"),
                    "processed_year": result.get("year"),
                    "total_insights": result.get("total_insights", 0),
                    "new_notifications": result.get("new_notifications", 0),
                    "updated_notifications": result.get("updated_notifications", 0)
                }
            )
        )
        
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in trigger_notification_route endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())


@router.get(
    "/total-notification",
    responses={
        200: {"model": CountNotifResponse},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def notif_total_route(
    db: AsyncSession = Depends(get_db), 
    token: str = Depends(oauth2_scheme)

):
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        
        #isi total dari total notifikasi dari tabel notifications untuk user yang login
        query = await db.execute(
            select(func.count(Notifications.id)).where(
                Notifications.is_read == False,
                Notifications.user_id == user.id,
            )
        )
        total_unread = query.scalar() or 0

        return common_response(
            Ok(data=CoutNotif(
                total=total_unread
            ).model_dump())
        )
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in role_management endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())

@router.get(
    "/list-notification",
    responses={
        200: {"model": ListNotifResponse},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def ls_notif_route(
    db: AsyncSession = Depends(get_db), 
    token: str = Depends(oauth2_scheme),
    page: int = 1,
    page_size: int = 10,
    src: Optional[str] = None,
):
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())        
        
        # Get total count for pagination
        total_query = await db.execute(
            select(func.count(Notifications.id)).where(
                Notifications.is_read == False,
                Notifications.user_id == user.id,
            )
        )
        total_count = total_query.scalar() or 0
        
        # Calculate pagination metadata
        page_count = (total_count + page_size - 1) // page_size  # Ceiling division
        
        # get data from notifications and use pagination
        offset = (page - 1) * page_size
        query = await db.execute(
            select(Notifications).where(
                Notifications.is_read == False,
                Notifications.user_id == user.id,
            ).offset(offset).limit(page_size)
        )
        notif_rows = query.scalars().all()
        
        ls_data_dummy = [
            ListNotif(
                id=row.id,
                title=row.title,
                message=row.message,
                is_read=row.is_read,
                created_at=row.created_at,
            ).model_dump()
            for row in notif_rows
        ]
        
        return common_response(
            Ok(
                data=ls_data_dummy,
                meta={
                    "count": len(ls_data_dummy),
                    "total_count": total_count,
                    "page_count": page_count,
                    "page_size": page_size,
                    "page": page,
                }
            )
        )
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in role_management endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in role_management endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())
@router.post(
    "/read-notification/{id}",
    responses={
        201: {"model": CudResponseSchema},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def read_notification(
    id: int,
    db: AsyncSession = Depends(get_db), 
    token: str = Depends(oauth2_scheme),
):
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        
        # ambil data from notifications where id = id ubah is_read to true
        query = await db.execute(
            select(Notifications).filter(
                Notifications.id == id,
                Notifications.user_id == user.id,
            )
        )
        notif = query.scalar_one_or_none()
        if not notif:
            return common_response(NotFound())
        notif.is_read = True
        await db.commit()
        await db.refresh(notif)
        return common_response(
            CudResponse(
            message="Notification marked as read successfully",
            )
        )
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in role_management endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())


@router.get(
    "/insight/{id}",
    responses={
        201: {"model": CudResponseSchema},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def get_insight(
    id: int,
    db: AsyncSession = Depends(get_db), 
    token: str = Depends(oauth2_scheme),
):
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        data = await db.execute(
            select(Notifications).filter(
                Notifications.id == id,
                Notifications.user_id == user.id,
            )
        )
        data = data.scalar_one_or_none()
        if not data:
            return common_response(NotFound())
        return common_response(
            CudResponse(
            message="Notification insight retrieved successfully",
            data={
                "title": data.title,
                "insight": data.insight,
            }
            )
        )
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in role_management endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())


@router.get(
    "/insight-pdf/{id}",
    responses={
        200: {"model": PdfResponseSchema},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def get_insight_pdf(
    id: int,
    db: AsyncSession = Depends(get_db), 
    token: str = Depends(oauth2_scheme),
):
    """
    Generate and return PDF from notification insight in base64 format
    """
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())
        
        # Get notification data
        data = await db.execute(
            select(Notifications).filter(
                Notifications.id == id,
                Notifications.user_id == user.id,
            )
        )
        data = data.scalar_one_or_none()
        if not data:
            return common_response(NotFound())
        
        # Check if insight data exists
        if not data.insight:
            return common_response(BadRequest(message="No insight data available for this notification"))
        
        # Generate PDF from markdown content
        pdf_base64 = generate_notification_pdf(data.title, data.insight)
        
        return common_response(
            Ok(data=PdfResponse(
                title=data.title,
                pdf_base64=pdf_base64
            ).model_dump())
        )
        
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in get_insight_pdf endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())