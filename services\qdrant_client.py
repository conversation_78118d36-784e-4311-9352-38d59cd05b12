from qdrant_client import QdrantClient
from qdrant_client.http import models as rest
from qdrant_client.http.models import (
    Distance,
    VectorParams,
    SparseVectorParams,
    MultiVectorConfig,
    MultiVectorComparator,
    HnswConfigDiff,
)
from qdrant_client.http.exceptions import UnexpectedResponse
from typing import Dict, List, Optional, Any, Union
import asyncio
import logging

qdrant_logger = logging.getLogger("be-dcs-dashboard.qdrant")


class QdrantService:
    def __init__(
        self,
        url: str = "localhost",
        api_key: Optional[str] = None,
        https: bool = False,
    ):
        """Initialize the Qdrant client service

        Args:
            host: Qdrant server host
            port: Qdrant REST API port
            grpc_port: Qdrant gRPC port
            https: Whether to use HTTPS
        """
        self.url = url
        self.https = https
        self.client = None
        self.api_key = api_key

    def connect(self):
        """Connect to Qdrant server"""
        try:
            if self.api_key:
                self.client = QdrantClient(
                    url=self.url, https=self.https, timeout=100.0, api_key=self.api_key
                )
            else:
                # self.client = QdrantClient(
                #     url=self.url, https=self.https, timeout=100.0
                # )
                self.client = QdrantClient(
                    url=self.url, https=False, timeout=100.0
                )
            qdrant_logger.info(f"Connected to Qdrant at {self.url}")
            return True
        except Exception as e:
            qdrant_logger.error(f"Failed to connect to Qdrant: {e}")
            return False

    async def create_hybrid_collection(
        self,
        collection_name: str,
        dense_size: int,
        late_size: int,
        model_name: str = "nomic-embed-text-v2-moe",
    ) -> str:
        """
        Create a Qdrant hybrid collection (dense + sparse + late interaction)

        Args:
            client: QdrantClient instance
            collection_name: Target collection name
            dense_size: Size of dense embedding vector (e.g., 768)
            late_size: Size of ColBERT subvector (e.g., 128)
            model_name: Name of the dense vector field (default: nomic-embed-text-v2-moe)

        Returns:
            Message string with success or already exists
        """

        try:
            collections = await asyncio.to_thread(self.client.get_collections)
            existing_names = [col.name for col in collections.collections]

            if collection_name in existing_names:
                return f"Collection '{collection_name}' already exists."

            await asyncio.to_thread(
                self.client.create_collection,
                collection_name=collection_name,
                vectors_config={
                    model_name: VectorParams(
                        size=dense_size,
                        distance=Distance.COSINE,
                    ),
                    "colbertv2.0": VectorParams(
                        size=late_size,
                        distance=Distance.COSINE,
                        multivector_config=MultiVectorConfig(
                            comparator=MultiVectorComparator.MAX_SIM,
                        ),
                        hnsw_config=HnswConfigDiff(m=0),
                    ),
                },
                sparse_vectors_config={
                    "bm25": SparseVectorParams()
                },
            )
            return f"Collection '{collection_name}' created successfully."

        except UnexpectedResponse as e:
            if "already exists" in str(e):
                return f"Collection '{collection_name}' already exists."
            raise RuntimeError(f"Qdrant creation error: {str(e)}")
        except Exception as e:
            raise RuntimeError(f"Unexpected error creating collection: {str(e)}")

    # async def create_collection(
    #     self, collection_name: str, vector_size: int, distance: str = "Cosine"
    # ):
    #     """Create a collection in Qdrant

    #     Args:
    #         collection_name: Name of the collection
    #         vector_size: Size of the vectors
    #         distance: Distance metric (Cosine, Euclid, Dot)
    #     """
    #     distance_map = {
    #         "cosine": Distance.COSINE,
    #         "euclid": Distance.EUCLID,
    #         "dot": Distance.DOT,
    #     }

    #     try:
    #         return await asyncio.to_thread(
    #             self.client.create_collection,
    #             collection_name=collection_name,
    #             vectors_config=VectorParams(
    #                 size=vector_size,
    #                 distance=distance_map.get(distance.lower(), Distance.COSINE),
    #             ),
    #         )
    #     except UnexpectedResponse as e:
    #         if "already exists" in str(e):
    #             qdrant_logger.warning(f"Collection {collection_name} already exists")
    #             return True
    #         qdrant_logger.error(f"Failed to create collection: {e}")
    #         raise
    #     except Exception as e:
    #         qdrant_logger.error(f"Error creating collection: {e}")
    #         raise

    async def upsert_points(self, collection_name: str, points: List[Dict[str, Any]]):
        """Add or update points in a collection

        Args:
            collection_name: Name of the collection
            points: List of points to upsert
        """
        try:
            return await asyncio.to_thread(
                self.client.upsert, collection_name=collection_name, points=points
            )
        except Exception as e:
            qdrant_logger.error(f"Error upserting points: {e}")
            raise

    async def search(
        self,
        collection_name: str,
        query_vector: List[float],
        limit: int = 10,
        filter: Optional[Dict] = None,
    ):
        """Search for similar vectors

        Args:
            collection_name: Name of the collection
            query_vector: Vector to search for
            limit: Maximum number of results
            filter: Optional filter conditions
        """
        try:
            return await asyncio.to_thread(
                self.client.search,
                collection_name=collection_name,
                query_vector=query_vector,
                limit=limit,
                query_filter=filter,
            )
        except Exception as e:
            qdrant_logger.error(f"Error searching: {e}")
            raise

    def close_connection(self):
        """Close the connection to Qdrant"""
        if self.client:
            # Qdrant client doesn't have an explicit close method
            # but we can set it to None to free resources
            self.client = None
            qdrant_logger.info("Qdrant client connection released")
