from datetime import datetime
import traceback
from typing import List, Optional
from fastapi import (
    APIRouter,
    Depends,
    Request,
    BackgroundTasks,
    UploadFile,
    File,
    Form,
    Request,
)
from fastapi.security import OAuth2PasswordRequestForm
import httpx
from sqlalchemy import func, select
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from models.FeedbackInsight import FeedbackInsight
from core.responses import (
    common_response,
    Ok,
    CudResponse,
    BadRequest,
    Unauthorized,
    NotFound,
    InternalServerError,
)
from models import get_db
from core.security import (
    check_user_permission,
    get_user_from_jwt_token,
    generate_jwt_token_from_user,
    get_user_permissions,
)
from core.security import (
    get_user_from_jwt_token,
    oauth2_scheme,
)
from schemas.common import (
    BadRequestResponse,
    UnauthorizedResponse,
    NotFoundResponse,
    InternalServerErrorResponse,
    CudResponseSchema,
)
from schemas.feedbackinsight import (
    AddFeedbackInsight,
    FeedbackInsightListResponse,
    FeedbackInsightResponse,
    ICAFeedbackPayload,
    MetaResponse,
)
from settings import ICA_BASE_URL, ICA_CHATBOT_ID, ICA_USERNAME


router = APIRouter(tags=["Feedback Insight"])


@router.post(
    "",
    responses={
        201: {"model": CudResponseSchema},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def add_feedback(
    payload: AddFeedbackInsight,
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme),
):
    try:
        user = await get_user_from_jwt_token(db, token)
        if not user:
            return common_response(Unauthorized())

        # token = await get_ica_token()
        # if not token:
        #     return {"error": "Failed to obtain ICA token"}

        # headers = {"Authorization": f"Bearer {token}", "Accept": "application/json"}

        # url = f"{ICA_BASE_URL}/tools/v1/feedback/public"

        # payload_data = ICAFeedbackPayload(
        #     thumbs=payload.feedback,
        #     feedback=payload.message,
        #     aiAnswerRevision=payload.message,
        #     aiAnswer=payload.insight,
        #     humanQuestion=payload.question,
        #     chatbotId=ICA_CHATBOT_ID,
        #     username=ICA_USERNAME,
        #     train=False,
        #     session=False,
        # )

        # response = httpx.post(
        #     url,
        #     headers=headers,
        #     json=payload_data.model_dump(),
        # )

        # if response.status_code == 200:
        #     new_feedback = FeedbackInsight(
        #         question=payload.question if payload.question else "",
        #         insight=payload.insight,
        #         message=payload.message if payload.message else "",
        #         feedback=payload.feedback,
        #         created_at=datetime.now(),
        #     )
        #     db.add(new_feedback)
        #     await db.commit()
        #     await db.refresh(new_feedback)

        return common_response(Ok(message="Feedback added successfully"))
        # else:
        #     return common_response(
        #         BadRequest(
        #             message="Failed to send feedback to ICA",
        #         )
        #     )
    except Exception as e:
        import traceback

        error_details = traceback.format_exc()
        print(f"Error in feedback endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())


@router.get(
    "",
    responses={
        200: {"model": FeedbackInsightListResponse},
        400: {"model": BadRequestResponse},
        401: {"model": UnauthorizedResponse},
        404: {"model": NotFoundResponse},
        500: {"model": InternalServerErrorResponse},
    },
)
async def get_feedback(
    db: AsyncSession = Depends(get_db),
    start: Optional[str] = None,
    end: Optional[str] = None,
    # token: str = Depends(oauth2_scheme),
):
    try:
        # user = await get_user_from_jwt_token(db, token)
        # if not user:
        #     return common_response(Unauthorized())

        ls_data: List[FeedbackInsight] = []

        query = select(FeedbackInsight).order_by(FeedbackInsight.created_at.desc())
        if start:
            start_date = datetime.strptime(start, "%Y-%m-%d")
            query = query.where(FeedbackInsight.created_at >= start_date)
        if end:
            end_date = datetime.strptime(end, "%Y-%m-%d")
            query = query.where(FeedbackInsight.created_at <= end_date)
        datas = await db.execute(query)

        datas = datas.scalars().all()

        for data in datas:
            ls_data.append(
                FeedbackInsightResponse(
                    id=data.id,
                    question=data.question,
                    insight=data.insight,
                    feedback=data.feedback,
                    message=data.message,
                ).model_dump()
            )

        return common_response(
            Ok(
                data=FeedbackInsightListResponse(
                    meta=MetaResponse(
                        count=len(ls_data), page_count=1, page_size=len(ls_data), page=1
                    ),
                    data=ls_data,
                    status="success",
                    code=200,
                    message="Feedback retrieved successfully",
                ).model_dump()
            )
        )
    except Exception as e:
        import traceback

        error_details = traceback.format_exc()
        print(f"Error in role_management endpoint: {str(e)}\n{error_details}")
        return common_response(InternalServerError())
